@echo off
echo 🇪🇬 EGX Professional SMC Dashboard
echo ================================

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo 🔧 Activating virtual environment...
    call venv\Scripts\activate.bat
)

REM Install dependencies
echo 📦 Installing dependencies...
pip install streamlit pandas numpy plotly ta python-dateutil protobuf

REM Run the dashboard
echo 🚀 Starting SMC Dashboard...
echo 📊 Dashboard will open at http://localhost:8501
echo 🛑 Press Ctrl+C to stop
echo.

python -m streamlit run streamlit_smc_dashboard.py

pause
