#!/usr/bin/env python3
"""
🇪🇬 EGX SMC Analysis Dashboard - Streamlit Version
Pure CSV-based SMC analysis for Egyptian stocks with web interface
"""

import streamlit as st
import pandas as pd
import os
import json
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
from typing import Dict, List, Optional
import sys

# Add the project root to the path so we can import our modules
sys.path.append('.')

# Import SMC modules
try:
    from indicators.market_structure import detect_market_structure
    from indicators.order_blocks import detect_order_blocks, get_active_order_blocks, analyze_order_block_confluence
    from indicators.fvg import detect_fvg, get_active_fvgs, analyze_fvg_confluence
    from indicators.liquidity_zones import detect_liquidity_zones, get_active_liquidity_zones, analyze_liquidity_confluence
    from indicators.displacement import detect_displacement, get_recent_displacements, analyze_displacement_confluence
    from indicators.eqh_eql import find_equal_highs_lows
    from log_setup import logger
except ImportError as e:
    st.error(f"❌ Error importing SMC modules: {e}")
    st.stop()

# Page configuration
st.set_page_config(
    page_title="🇪🇬 EGX SMC Analysis Dashboard",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .success-metric {
        border-left-color: #28a745;
    }
    .warning-metric {
        border-left-color: #ffc107;
    }
    .danger-metric {
        border-left-color: #dc3545;
    }
</style>
""", unsafe_allow_html=True)

class StreamlitSMCAnalyzer:
    """Streamlit-based SMC analyzer"""
    
    def __init__(self, data_dir: str = "data/historical"):
        self.data_dir = data_dir
        
    def get_egx_symbols(self) -> List[str]:
        """Get available EGX symbols (exclude crypto)"""
        if not os.path.exists(self.data_dir):
            return []
            
        all_files = [f.replace('.csv', '') for f in os.listdir(self.data_dir) if f.endswith('.csv')]
        
        # Filter out crypto symbols
        crypto_patterns = ['USDT', 'BTC', 'ETH', 'ADA', 'BNB', 'SOL', 'XRP', 'ATOM', 'EOS', '_1h', '_15m', '_1m', '_4h']
        
        egx_symbols = []
        for file in all_files:
            is_crypto = any(pattern in file.upper() for pattern in crypto_patterns)
            if not is_crypto:
                egx_symbols.append(file)
                
        return sorted(egx_symbols)
    
    def load_csv_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """Load CSV data for a symbol"""
        csv_path = os.path.join(self.data_dir, f"{symbol}.csv")
        
        if not os.path.exists(csv_path):
            st.error(f"❌ CSV file not found: {csv_path}")
            return None
            
        try:
            df = pd.read_csv(csv_path)
            
            # Ensure required columns
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            if not all(col in df.columns for col in required_cols):
                st.error(f"❌ Missing required columns in {symbol}.csv")
                return None
                
            # Convert to numeric
            for col in required_cols:
                df[col] = pd.to_numeric(df[col], errors='coerce')
                
            # Remove any rows with NaN values
            df = df.dropna()
            
            if len(df) < 50:
                st.warning(f"⚠️ Insufficient data for {symbol}: {len(df)} rows")
                return None
                
            return df
            
        except Exception as e:
            st.error(f"❌ Error loading {symbol}.csv: {e}")
            return None
    
    def analyze_symbol(self, symbol: str) -> Optional[Dict]:
        """Run complete SMC analysis on a symbol"""
        df = self.load_csv_data(symbol)
        if df is None:
            return None
            
        try:
            current_price = float(df['close'].iloc[-1])
            
            # === COMPLETE SMC ANALYSIS ===
            
            # 1. Market Structure
            ms = detect_market_structure(df)
            
            # 2. Order Blocks
            order_blocks = get_active_order_blocks(df, current_price, max_distance_pct=5.0)
            ob_confluence = analyze_order_block_confluence(order_blocks, current_price)
            
            # 3. Fair Value Gaps
            fvgs = get_active_fvgs(df, current_price, max_distance_pct=3.0)
            fvg_confluence = analyze_fvg_confluence(fvgs, current_price)
            
            # 4. Liquidity Zones
            liquidity_zones = get_active_liquidity_zones(df, current_price, max_distance_pct=8.0)
            liq_confluence = analyze_liquidity_confluence(liquidity_zones, current_price)
            
            # 5. Displacement
            displacements = get_recent_displacements(df, lookback_periods=30)
            disp_confluence = analyze_displacement_confluence(displacements, current_price)
            
            # 6. Equal Highs/Lows
            eqh_eql = find_equal_highs_lows(df)
            
            # Calculate overall confluence
            confluence_factors = [
                ob_confluence.get("confluence", False),
                fvg_confluence.get("confluence", False),
                liq_confluence.get("confluence", False),
                disp_confluence.get("confluence", False)
            ]
            
            active_factors = sum(confluence_factors)
            confluence_strength = active_factors / 4
            
            # Build analysis result
            analysis = {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "current_price": current_price,
                "data_points": len(df),
                "market_structure": {
                    "trend": ms.get("trend", "UNKNOWN"),
                    "event": ms.get("event"),
                    "strength": len(ms.get("highs", [])) + len(ms.get("lows", []))
                },
                "order_blocks": {
                    "count": len(order_blocks),
                    "confluence": ob_confluence.get("confluence", False),
                    "strength": ob_confluence.get("strength", 0),
                    "blocks": order_blocks[:5]  # Top 5
                },
                "fair_value_gaps": {
                    "count": len(fvgs),
                    "confluence": fvg_confluence.get("confluence", False),
                    "strength": fvg_confluence.get("strength", 0),
                    "gaps": fvgs[:5]  # Top 5
                },
                "liquidity_zones": {
                    "count": len(liquidity_zones),
                    "confluence": liq_confluence.get("confluence", False),
                    "strength": liq_confluence.get("strength", 0),
                    "zones": liquidity_zones[:5]  # Top 5
                },
                "displacements": {
                    "count": len(displacements),
                    "confluence": disp_confluence.get("confluence", False),
                    "strength": disp_confluence.get("strength", 0),
                    "moves": displacements[:5]  # Top 5
                },
                "equal_highs_lows": eqh_eql,
                "confluence_summary": {
                    "total_factors": int(active_factors),
                    "confluence_strength": float(confluence_strength),
                    "aligned_factors": [
                        factor for factor, active in [
                            ("order_blocks", ob_confluence.get("confluence", False)),
                            ("fvgs", fvg_confluence.get("confluence", False)),
                            ("liquidity_zones", liq_confluence.get("confluence", False)),
                            ("displacements", disp_confluence.get("confluence", False))
                        ] if active
                    ],
                    "signal_strength": "STRONG" if confluence_strength >= 0.75 else "MODERATE" if confluence_strength >= 0.5 else "WEAK"
                },
                "dataframe": df  # Include for plotting
            }
            
            return analysis
            
        except Exception as e:
            st.error(f"❌ Error analyzing {symbol}: {e}")
            return None

def create_price_chart(df: pd.DataFrame, symbol: str, analysis: Dict) -> go.Figure:
    """Create an interactive price chart with SMC levels"""
    fig = go.Figure()
    
    # Add candlestick chart
    fig.add_trace(go.Candlestick(
        x=df.index,
        open=df['open'],
        high=df['high'],
        low=df['low'],
        close=df['close'],
        name=symbol,
        increasing_line_color='#26a69a',
        decreasing_line_color='#ef5350'
    ))
    
    # Add Order Blocks
    for ob in analysis['order_blocks']['blocks']:
        fig.add_hline(
            y=ob.high,
            line_dash="dash",
            line_color="blue",
            annotation_text=f"OB High: {ob.high:.2f}",
            annotation_position="bottom right"
        )
        fig.add_hline(
            y=ob.low,
            line_dash="dash",
            line_color="blue",
            annotation_text=f"OB Low: {ob.low:.2f}",
            annotation_position="top right"
        )
    
    # Add FVGs
    for fvg in analysis['fair_value_gaps']['gaps']:
        fig.add_hrect(
            y0=fvg.low,
            y1=fvg.high,
            fillcolor="yellow",
            opacity=0.2,
            line_width=0
        )
        # Add annotation separately to avoid position errors
        fig.add_annotation(
            x=df.index[len(df)//2],
            y=(fvg.low + fvg.high) / 2,
            text=f"FVG: {fvg.gap_type}",
            showarrow=False,
            font=dict(color="black", size=10),
            bgcolor="rgba(255,255,0,0.7)",
            bordercolor="orange",
            borderwidth=1
        )
    
    # Add Liquidity Zones
    for zone in analysis['liquidity_zones']['zones']:
        fig.add_hrect(
            y0=zone.low,
            y1=zone.high,
            fillcolor="purple",
            opacity=0.1,
            line_width=1,
            line_color="purple"
        )
        # Add annotation separately to avoid position errors
        fig.add_annotation(
            x=df.index[len(df)*2//3],
            y=(zone.low + zone.high) / 2,
            text=f"LZ: {zone.zone_type}",
            showarrow=False,
            font=dict(color="purple", size=10),
            bgcolor="rgba(128,0,128,0.2)",
            bordercolor="purple",
            borderwidth=1
        )
    
    fig.update_layout(
        title=f"{symbol} - SMC Analysis",
        xaxis_title="Time",
        yaxis_title="Price",
        height=600,
        showlegend=True
    )
    
    return fig

def main():
    """Main Streamlit app"""
    
    # Header
    st.markdown('<h1 class="main-header">🇪🇬 EGX Smart Money Concepts Analysis</h1>', unsafe_allow_html=True)
    st.markdown("**Comprehensive SMC analysis for Egyptian Exchange stocks using CSV data**")
    
    # Initialize analyzer
    analyzer = StreamlitSMCAnalyzer()
    
    # Sidebar
    st.sidebar.header("📊 Analysis Controls")
    
    # Get available symbols
    egx_symbols = analyzer.get_egx_symbols()
    
    if not egx_symbols:
        st.error("❌ No EGX stock CSV files found in data/historical/")
        st.info("📁 Please add your EGX stock CSV files to data/historical/")
        st.info("💡 Note: Crypto files (with USDT, BTC, etc.) are automatically excluded")
        st.stop()
    
    st.sidebar.success(f"✅ Found {len(egx_symbols)} EGX stocks")
    
    # Symbol selection
    selected_symbol = st.sidebar.selectbox(
        "🔍 Select EGX Stock:",
        egx_symbols,
        index=0 if egx_symbols else None
    )
    
    # Analysis button
    if st.sidebar.button("🚀 Run SMC Analysis", type="primary"):
        if selected_symbol:
            with st.spinner(f"🔍 Analyzing {selected_symbol}..."):
                analysis = analyzer.analyze_symbol(selected_symbol)
                
                if analysis:
                    st.session_state['analysis'] = analysis
                    st.session_state['selected_symbol'] = selected_symbol
                    st.success(f"✅ Analysis complete for {selected_symbol}")
                else:
                    st.error(f"❌ Failed to analyze {selected_symbol}")
    
    # Display results if available
    if 'analysis' in st.session_state:
        analysis = st.session_state['analysis']
        symbol = st.session_state['selected_symbol']
        
        # Main metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "💰 Current Price",
                f"{analysis['current_price']:.2f} EGP",
                delta=None
            )
        
        with col2:
            trend = analysis['market_structure']['trend']
            trend_color = "🟢" if trend == "bullish" else "🔴" if trend == "bearish" else "🟡"
            st.metric(
                "📈 Market Trend",
                f"{trend_color} {trend.upper()}",
                delta=None
            )
        
        with col3:
            confluence = analysis['confluence_summary']
            strength_color = "🟢" if confluence['signal_strength'] == "STRONG" else "🟡" if confluence['signal_strength'] == "MODERATE" else "🔴"
            st.metric(
                "🎯 Signal Strength",
                f"{strength_color} {confluence['signal_strength']}",
                delta=f"{confluence['total_factors']}/4 factors"
            )
        
        with col4:
            st.metric(
                "📊 Data Points",
                f"{analysis['data_points']:,}",
                delta=None
            )
        
        # SMC Analysis Details
        st.header("🔍 Smart Money Concepts Analysis")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # Order Blocks
            st.subheader("🔲 Order Blocks")
            ob_data = analysis['order_blocks']
            st.metric("Active Order Blocks", ob_data['count'])
            st.write(f"**Confluence:** {'✅ Yes' if ob_data['confluence'] else '❌ No'}")
            
            if ob_data['blocks']:
                for i, ob in enumerate(ob_data['blocks'][:3]):
                    st.write(f"**Block {i+1}:** {ob.block_type} | High: {ob.high:.2f} | Low: {ob.low:.2f} | Strength: {ob.strength:.2f}")
            
            # Fair Value Gaps
            st.subheader("⚡ Fair Value Gaps")
            fvg_data = analysis['fair_value_gaps']
            st.metric("Active FVGs", fvg_data['count'])
            st.write(f"**Confluence:** {'✅ Yes' if fvg_data['confluence'] else '❌ No'}")
            
            if fvg_data['gaps']:
                for i, fvg in enumerate(fvg_data['gaps'][:3]):
                    st.write(f"**FVG {i+1}:** {fvg.gap_type} | High: {fvg.high:.2f} | Low: {fvg.low:.2f} | Filled: {'Yes' if fvg.filled else 'No'}")
        
        with col2:
            # Liquidity Zones
            st.subheader("💧 Liquidity Zones")
            liq_data = analysis['liquidity_zones']
            st.metric("Active Zones", liq_data['count'])
            st.write(f"**Confluence:** {'✅ Yes' if liq_data['confluence'] else '❌ No'}")
            
            if liq_data['zones']:
                for i, zone in enumerate(liq_data['zones'][:3]):
                    zone_type = zone.zone_type.value if hasattr(zone.zone_type, 'value') else str(zone.zone_type)
                    st.write(f"**Zone {i+1}:** {zone_type} | High: {zone.high:.2f} | Low: {zone.low:.2f} | Touches: {zone.touches}")
            
            # Displacements
            st.subheader("🚀 Displacements")
            disp_data = analysis['displacements']
            st.metric("Recent Moves", disp_data['count'])
            st.write(f"**Confluence:** {'✅ Yes' if disp_data['confluence'] else '❌ No'}")
            
            if disp_data['moves']:
                for i, disp in enumerate(disp_data['moves'][:3]):
                    disp_type = disp.displacement_type.value if hasattr(disp.displacement_type, 'value') else str(disp.displacement_type)
                    st.write(f"**Move {i+1}:** {disp_type} | Size: {disp.displacement_pct:.2f}% | Strength: {disp.strength:.2f}")
        
        # Price Chart
        st.header("📈 Price Chart with SMC Levels")
        df = analysis['dataframe']
        chart = create_price_chart(df, symbol, analysis)
        st.plotly_chart(chart, use_container_width=True)
        
        # Confluence Summary
        st.header("🎯 Confluence Summary")
        confluence = analysis['confluence_summary']
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Total Factors", f"{confluence['total_factors']}/4")
        
        with col2:
            st.metric("Confluence Strength", f"{confluence['confluence_strength']:.1%}")
        
        with col3:
            strength = confluence['signal_strength']
            color = "success" if strength == "STRONG" else "warning" if strength == "MODERATE" else "danger"
            st.markdown(f'<div class="metric-card {color}-metric"><strong>Signal Strength:</strong> {strength}</div>', unsafe_allow_html=True)
        
        if confluence['aligned_factors']:
            st.write("**Aligned Factors:**")
            for factor in confluence['aligned_factors']:
                st.write(f"✅ {factor.replace('_', ' ').title()}")
        else:
            st.write("❌ No aligned factors detected")

if __name__ == "__main__":
    main()
