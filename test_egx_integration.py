#!/usr/bin/env python3
"""
Test script for EGX integration with TradingView bridge
Verifies that the Python bot can communicate with the Node.js bridge
"""

import asyncio
import sys
import logging
from datetime import datetime

# Add project root to path
sys.path.append('.')

from api.egx_async import (
    get_ohlcv, get_current_price, get_multiple_prices,
    search_symbols, get_common_egx_symbols, check_bridge_status,
    init_egx_client, EGXAPIError
)
from config.egx_config import (
    DEFAULT_TRADING_SYMBOLS, get_symbol_info, 
    get_market_status, EGX_SYMBOLS
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_bridge_connection():
    """Test connection to the TradingView bridge"""
    print("🔌 Testing bridge connection...")
    try:
        status = await check_bridge_status()
        print(f"✅ Bridge Status: Connected={status.get('connected', False)}")
        print(f"📊 Cache Size: {status.get('cacheSize', 0)}")
        print(f"⏱️ Uptime: {status.get('uptime', 0):.2f} seconds")
        return True
    except Exception as e:
        print(f"❌ Bridge connection failed: {e}")
        return False

async def test_symbol_search():
    """Test symbol search functionality"""
    print("\n🔍 Testing symbol search...")
    try:
        # Test search
        results = await search_symbols("CIB")
        print(f"✅ Search results for 'CIB': {len(results)} found")
        
        # Test common symbols
        common = await get_common_egx_symbols()
        print(f"✅ Common EGX symbols: {len(common)} available")
        
        # Display some symbols
        for symbol in common[:5]:
            print(f"   📈 {symbol['symbol']}: {symbol['name']}")
        
        return True
    except Exception as e:
        print(f"❌ Symbol search failed: {e}")
        return False

async def test_price_data():
    """Test price data fetching"""
    print("\n💰 Testing price data...")
    try:
        # Test single price
        symbol = "CIB"
        price_data = await get_current_price(symbol)
        print(f"✅ {symbol} Current Price: {price_data['price']:.2f} EGP")
        print(f"   📊 Change: {price_data['change']:+.2f} ({price_data['changePercent']:+.2f}%)")
        
        # Test multiple prices
        symbols = ["CIB", "ETEL", "HELI"]
        prices = await get_multiple_prices(symbols)
        print(f"✅ Batch prices for {len(symbols)} symbols:")
        
        for sym, data in prices.items():
            if data:
                print(f"   📈 {sym}: {data['price']:.2f} EGP ({data['changePercent']:+.2f}%)")
            else:
                print(f"   ❌ {sym}: Failed to fetch")
        
        return True
    except Exception as e:
        print(f"❌ Price data test failed: {e}")
        return False

async def test_ohlcv_data():
    """Test OHLCV data fetching"""
    print("\n📊 Testing OHLCV data...")
    try:
        symbol = "CIB"
        intervals = ["1h", "1d"]
        
        for interval in intervals:
            ohlcv = await get_ohlcv(symbol, interval=interval, limit=5)
            print(f"✅ {symbol} OHLCV ({interval}): {len(ohlcv)} candles")
            
            if ohlcv:
                latest = ohlcv[-1]
                timestamp = datetime.fromtimestamp(latest['timestamp'] / 1000)
                print(f"   📅 Latest: {timestamp.strftime('%Y-%m-%d %H:%M')}")
                print(f"   💹 OHLC: {latest['open']:.2f} | {latest['high']:.2f} | {latest['low']:.2f} | {latest['close']:.2f}")
                print(f"   📊 Volume: {latest['volume']:,.0f}")
        
        return True
    except Exception as e:
        print(f"❌ OHLCV data test failed: {e}")
        return False

async def test_market_info():
    """Test market information and configuration"""
    print("\n🏛️ Testing market information...")
    try:
        # Market status
        market_status = get_market_status()
        print(f"✅ Market Status:")
        print(f"   🕐 Current Time: {market_status['current_time']} ({market_status['timezone']})")
        print(f"   📅 Trading Day: {market_status['is_trading_day']}")
        print(f"   🔓 Market Open: {market_status['is_open']}")
        print(f"   ⏰ Hours: {market_status['market_open']} - {market_status['market_close']}")
        
        # Symbol information
        print(f"\n📈 Symbol Information:")
        for symbol in DEFAULT_TRADING_SYMBOLS[:3]:
            info = get_symbol_info(symbol)
            print(f"   {symbol}: {info.get('name', 'Unknown')} ({info.get('sector', 'Unknown')})")
        
        return True
    except Exception as e:
        print(f"❌ Market info test failed: {e}")
        return False

async def test_error_handling():
    """Test error handling with invalid requests"""
    print("\n⚠️ Testing error handling...")
    try:
        # Test invalid symbol
        try:
            await get_current_price("INVALID_SYMBOL")
            print("❌ Should have failed for invalid symbol")
            return False
        except EGXAPIError:
            print("✅ Correctly handled invalid symbol error")
        
        # Test invalid interval
        try:
            await get_ohlcv("CIB", interval="invalid_interval")
            print("❌ Should have failed for invalid interval")
            return False
        except EGXAPIError:
            print("✅ Correctly handled invalid interval error")
        
        return True
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

async def run_performance_test():
    """Test performance with multiple concurrent requests"""
    print("\n⚡ Testing performance...")
    try:
        start_time = datetime.now()
        
        # Concurrent requests
        tasks = []
        symbols = ["CIB", "ETEL", "HELI", "EGAS", "SWDY"]
        
        for symbol in symbols:
            tasks.append(get_current_price(symbol))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        successful = sum(1 for r in results if not isinstance(r, Exception))
        print(f"✅ Performance Test:")
        print(f"   📊 Requests: {len(tasks)}")
        print(f"   ✅ Successful: {successful}")
        print(f"   ⏱️ Duration: {duration:.2f} seconds")
        print(f"   🚀 Rate: {len(tasks)/duration:.1f} req/sec")
        
        return True
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False

async def main():
    """Run all integration tests"""
    print("🧪 EGX Integration Test Suite")
    print("=" * 50)
    
    # Initialize client
    print("🔧 Initializing EGX client...")
    success = await init_egx_client()
    if not success:
        print("❌ Failed to initialize EGX client. Make sure the Node.js bridge is running!")
        print("💡 Start the bridge with: cd nodejs-bridge && npm start")
        return False
    
    # Run tests
    tests = [
        ("Bridge Connection", test_bridge_connection),
        ("Symbol Search", test_symbol_search),
        ("Price Data", test_price_data),
        ("OHLCV Data", test_ohlcv_data),
        ("Market Info", test_market_info),
        ("Error Handling", test_error_handling),
        ("Performance", run_performance_test),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 All tests passed! EGX integration is working correctly.")
        print("\n🚀 Next steps:")
        print("   1. Update the main bot to use EGX symbols")
        print("   2. Modify trading strategies for stock market")
        print("   3. Test with paper trading first")
    else:
        print("⚠️ Some tests failed. Check the errors above.")
        print("\n🔧 Troubleshooting:")
        print("   1. Ensure Node.js bridge is running (npm start)")
        print("   2. Check bridge logs for errors")
        print("   3. Verify EGX symbols are available on TradingView")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test suite crashed: {e}")
        sys.exit(1)
