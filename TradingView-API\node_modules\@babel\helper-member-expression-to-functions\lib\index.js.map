{"version": 3, "file": "index.js", "sources": ["../src/util.ts", "../src/index.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/traverse\";\n\n/**\n * Test if a NodePath will be cast to boolean when evaluated.\n *\n * @example\n * // returns true\n * const nodePathAQDotB = NodePath(\"if (a?.#b) {}\").get(\"test\"); // a?.#b\n * willPathCastToBoolean(nodePathAQDotB)\n * @example\n * // returns false\n * willPathCastToBoolean(NodePath(\"a?.#b\"))\n * @todo Respect transparent expression wrappers\n * @see {@link packages/babel-plugin-proposal-optional-chaining/src/util.js}\n * @param {NodePath} path\n * @returns {boolean}\n */\nexport function willPathCastToBoolean(path: NodePath): boolean {\n  const maybeWrapped = path;\n  const { node, parentPath } = maybeWrapped;\n  if (parentPath.isLogicalExpression()) {\n    const { operator, right } = parentPath.node;\n    if (\n      operator === \"&&\" ||\n      operator === \"||\" ||\n      (operator === \"??\" && node === right)\n    ) {\n      return willPathCastToBoolean(parentPath);\n    }\n  }\n  if (parentPath.isSequenceExpression()) {\n    const { expressions } = parentPath.node;\n    if (expressions[expressions.length - 1] === node) {\n      return willPathCastToBoolean(parentPath);\n    } else {\n      // if it is in the middle of a sequence expression, we don't\n      // care the return value so just cast to boolean for smaller\n      // output\n      return true;\n    }\n  }\n  return (\n    parentPath.isConditional({ test: node }) ||\n    parentPath.isUnaryExpression({ operator: \"!\" }) ||\n    parentPath.isLoop({ test: node })\n  );\n}\n", "import type { <PERSON><PERSON><PERSON><PERSON>, Visitor } from \"@babel/traverse\";\nimport {\n  LOGICAL_OPERATORS,\n  arrowFunctionExpression,\n  assignmentExpression,\n  binaryExpression,\n  booleanLiteral,\n  callExpression,\n  cloneNode,\n  conditionalExpression,\n  identifier,\n  isMemberExpression,\n  isOptionalCallExpression,\n  isOptionalMemberExpression,\n  isUpdateExpression,\n  logicalExpression,\n  memberExpression,\n  nullLiteral,\n  numericLiteral,\n  optionalCallExpression,\n  optionalMemberExpression,\n  sequenceExpression,\n  unaryExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { willPathCastToBoolean } from \"./util\";\n\nclass AssignmentMemoiser {\n  private _map: WeakMap<t.Expression, { count: number; value: t.LVal }>;\n  constructor() {\n    this._map = new WeakMap();\n  }\n\n  has(key: t.Expression) {\n    return this._map.has(key);\n  }\n\n  get(key: t.Expression) {\n    if (!this.has(key)) return;\n\n    const record = this._map.get(key);\n    const { value } = record;\n\n    record.count--;\n    if (record.count === 0) {\n      // The `count` access is the outermost function call (hopefully), so it\n      // does the assignment.\n      return assignmentExpression(\"=\", value, key);\n    }\n    return value;\n  }\n\n  set(key: t.Expression, value: t.LVal, count: number) {\n    return this._map.set(key, { count, value });\n  }\n}\n\nfunction toNonOptional(\n  path: NodePath<t.Expression>,\n  base: t.Expression,\n): t.Expression {\n  const { node } = path;\n  if (isOptionalMemberExpression(node)) {\n    return memberExpression(base, node.property, node.computed);\n  }\n\n  if (path.isOptionalCallExpression()) {\n    const callee = path.get(\"callee\");\n    if (path.node.optional && callee.isOptionalMemberExpression()) {\n      const { object } = callee.node;\n      const context = path.scope.maybeGenerateMemoised(object) || object;\n      callee\n        .get(\"object\")\n        .replaceWith(assignmentExpression(\"=\", context as t.LVal, object));\n\n      return callExpression(memberExpression(base, identifier(\"call\")), [\n        context,\n        ...path.node.arguments,\n      ]);\n    }\n\n    return callExpression(base, path.node.arguments);\n  }\n\n  return path.node;\n}\n\n// Determines if the current path is in a detached tree. This can happen when\n// we are iterating on a path, and replace an ancestor with a new node. Babel\n// doesn't always stop traversing the old node tree, and that can cause\n// inconsistencies.\nfunction isInDetachedTree(path: NodePath) {\n  while (path) {\n    if (path.isProgram()) break;\n\n    const { parentPath, container, listKey } = path;\n    const parentNode = parentPath.node;\n    if (listKey) {\n      if (container !== parentNode[listKey]) return true;\n    } else {\n      if (container !== parentNode) return true;\n    }\n\n    path = parentPath;\n  }\n\n  return false;\n}\n\ntype Member = NodePath<t.OptionalMemberExpression | t.MemberExpression>;\n\nconst handle = {\n  memoise() {\n    // noop.\n  },\n\n  handle(this: HandlerState, member: Member, noDocumentAll: boolean) {\n    const { node, parent, parentPath, scope } = member;\n\n    if (member.isOptionalMemberExpression()) {\n      // Transforming optional chaining requires we replace ancestors.\n      if (isInDetachedTree(member)) return;\n\n      // We're looking for the end of _this_ optional chain, which is actually\n      // the \"rightmost\" property access of the chain. This is because\n      // everything up to that property access is \"optional\".\n      //\n      // Let's take the case of `FOO?.BAR.baz?.qux`, with `FOO?.BAR` being our\n      // member. The \"end\" to most users would be `qux` property access.\n      // Everything up to it could be skipped if it `FOO` were nullish. But\n      // actually, we can consider the `baz` access to be the end. So we're\n      // looking for the nearest optional chain that is `optional: true`.\n      const endPath = member.find(({ node, parent }) => {\n        if (isOptionalMemberExpression(parent)) {\n          // We need to check `parent.object` since we could be inside the\n          // computed expression of a `bad?.[FOO?.BAR]`. In this case, the\n          // endPath is the `FOO?.BAR` member itself.\n          return parent.optional || parent.object !== node;\n        }\n        if (isOptionalCallExpression(parent)) {\n          // Checking `parent.callee` since we could be in the arguments, eg\n          // `bad?.(FOO?.BAR)`.\n          // Also skip `FOO?.BAR` in `FOO?.BAR?.()` since we need to transform the optional call to ensure proper this\n          return (\n            // In FOO?.#BAR?.(), endPath points the optional call expression so we skip FOO?.#BAR\n            (node !== member.node && parent.optional) || parent.callee !== node\n          );\n        }\n        return true;\n      }) as NodePath<t.OptionalMemberExpression>;\n\n      // Replace `function (a, x = a.b?.#c) {}` to `function (a, x = (() => a.b?.#c)() ){}`\n      // so the temporary variable can be injected in correct scope\n      // This can be further optimized to avoid unecessary IIFE\n      if (scope.path.isPattern()) {\n        endPath.replaceWith(\n          // The injected member will be queued and eventually transformed when visited\n          callExpression(arrowFunctionExpression([], endPath.node), []),\n        );\n        return;\n      }\n\n      const willEndPathCastToBoolean = willPathCastToBoolean(endPath);\n\n      const rootParentPath = endPath.parentPath;\n      if (\n        rootParentPath.isUpdateExpression({ argument: node }) ||\n        rootParentPath.isAssignmentExpression({ left: node })\n      ) {\n        throw member.buildCodeFrameError(`can't handle assignment`);\n      }\n      const isDeleteOperation = rootParentPath.isUnaryExpression({\n        operator: \"delete\",\n      });\n      if (\n        isDeleteOperation &&\n        endPath.isOptionalMemberExpression() &&\n        endPath.get(\"property\").isPrivateName()\n      ) {\n        // @babel/parser will throw error on `delete obj?.#x`.\n        // This error serves as fallback when `delete obj?.#x` is constructed from babel types\n        throw member.buildCodeFrameError(\n          `can't delete a private class element`,\n        );\n      }\n\n      // Now, we're looking for the start of this optional chain, which is\n      // optional to the left of this member.\n      //\n      // Let's take the case of `foo?.bar?.baz.QUX?.BAM`, with `QUX?.BAM` being\n      // our member. The \"start\" to most users would be `foo` object access.\n      // But actually, we can consider the `bar` access to be the start. So\n      // we're looking for the nearest optional chain that is `optional: true`,\n      // which is guaranteed to be somewhere in the object/callee tree.\n      let startingOptional: NodePath<t.Expression> = member;\n      for (;;) {\n        if (startingOptional.isOptionalMemberExpression()) {\n          if (startingOptional.node.optional) break;\n          startingOptional = startingOptional.get(\"object\");\n          continue;\n        } else if (startingOptional.isOptionalCallExpression()) {\n          if (startingOptional.node.optional) break;\n          startingOptional = startingOptional.get(\"callee\");\n          continue;\n        }\n        // prevent infinite loop: unreachable if the AST is well-formed\n        throw new Error(\n          `Internal error: unexpected ${startingOptional.node.type}`,\n        );\n      }\n\n      const startingProp = startingOptional.isOptionalMemberExpression()\n        ? \"object\"\n        : \"callee\";\n      const startingNode = startingOptional.node[startingProp];\n      const baseNeedsMemoised = scope.maybeGenerateMemoised(startingNode);\n      const baseRef = baseNeedsMemoised ?? startingNode;\n\n      // Compute parentIsOptionalCall before `startingOptional` is replaced\n      // as `node` may refer to `startingOptional.node` before replaced.\n      const parentIsOptionalCall = parentPath.isOptionalCallExpression({\n        callee: node,\n      });\n      // here we use a function to wrap `parentIsOptionalCall` to get type\n      // for parent, do not use it anywhere else\n      // See https://github.com/microsoft/TypeScript/issues/10421\n      const isOptionalCall = (\n        parent: t.Node,\n      ): parent is t.OptionalCallExpression => parentIsOptionalCall;\n      // if parentIsCall is true, it implies that node.extra.parenthesized is always true\n      const parentIsCall = parentPath.isCallExpression({ callee: node });\n      startingOptional.replaceWith(toNonOptional(startingOptional, baseRef));\n      if (isOptionalCall(parent)) {\n        if (parent.optional) {\n          parentPath.replaceWith(this.optionalCall(member, parent.arguments));\n        } else {\n          parentPath.replaceWith(this.call(member, parent.arguments));\n        }\n      } else if (parentIsCall) {\n        // `(a?.#b)()` to `(a == null ? void 0 : a.#b.bind(a))()`\n        member.replaceWith(this.boundGet(member));\n      } else {\n        member.replaceWith(this.get(member));\n      }\n\n      let regular: t.Expression = member.node;\n      for (let current: NodePath = member; current !== endPath; ) {\n        const parentPath = current.parentPath as NodePath<t.Expression>;\n        // skip transforming `Foo.#BAR?.call(FOO)`\n        if (\n          parentPath === endPath &&\n          isOptionalCall(parent) &&\n          parent.optional\n        ) {\n          regular = parentPath.node;\n          break;\n        }\n        regular = toNonOptional(parentPath, regular);\n        current = parentPath;\n      }\n\n      let context: t.Identifier;\n      const endParentPath = endPath.parentPath as NodePath<t.Expression>;\n      if (\n        isMemberExpression(regular) &&\n        endParentPath.isOptionalCallExpression({\n          callee: endPath.node,\n          optional: true,\n        })\n      ) {\n        const { object } = regular;\n        context = member.scope.maybeGenerateMemoised(object);\n        if (context) {\n          regular.object = assignmentExpression(\"=\", context, object);\n        }\n      }\n\n      let replacementPath: NodePath = endPath;\n      if (isDeleteOperation) {\n        replacementPath = endParentPath;\n        regular = endParentPath.node;\n      }\n\n      const baseMemoised = baseNeedsMemoised\n        ? assignmentExpression(\"=\", cloneNode(baseRef), cloneNode(startingNode))\n        : cloneNode(baseRef);\n\n      if (willEndPathCastToBoolean) {\n        let nonNullishCheck;\n        if (noDocumentAll) {\n          nonNullishCheck = binaryExpression(\"!=\", baseMemoised, nullLiteral());\n        } else {\n          nonNullishCheck = logicalExpression(\n            \"&&\",\n            binaryExpression(\"!==\", baseMemoised, nullLiteral()),\n            binaryExpression(\n              \"!==\",\n              cloneNode(baseRef),\n              scope.buildUndefinedNode(),\n            ),\n          );\n        }\n        replacementPath.replaceWith(\n          logicalExpression(\"&&\", nonNullishCheck, regular),\n        );\n      } else {\n        let nullishCheck;\n        if (noDocumentAll) {\n          nullishCheck = binaryExpression(\"==\", baseMemoised, nullLiteral());\n        } else {\n          nullishCheck = logicalExpression(\n            \"||\",\n            binaryExpression(\"===\", baseMemoised, nullLiteral()),\n            binaryExpression(\n              \"===\",\n              cloneNode(baseRef),\n              scope.buildUndefinedNode(),\n            ),\n          );\n        }\n\n        replacementPath.replaceWith(\n          conditionalExpression(\n            nullishCheck,\n            isDeleteOperation\n              ? booleanLiteral(true)\n              : scope.buildUndefinedNode(),\n            regular,\n          ),\n        );\n      }\n\n      // context and isDeleteOperation can not be both truthy\n      if (context) {\n        const endParent = endParentPath.node as t.OptionalCallExpression;\n        endParentPath.replaceWith(\n          optionalCallExpression(\n            optionalMemberExpression(\n              endParent.callee,\n              identifier(\"call\"),\n              false,\n              true,\n            ),\n            [cloneNode(context), ...endParent.arguments],\n            false,\n          ),\n        );\n      }\n\n      return;\n    }\n\n    // MEMBER++   ->   _set(MEMBER, (_ref = (+_get(MEMBER))) + 1), _ref\n    // ++MEMBER   ->   _set(MEMBER, (+_get(MEMBER)) + 1)\n    if (isUpdateExpression(parent, { argument: node })) {\n      if (this.simpleSet) {\n        member.replaceWith(this.simpleSet(member));\n        return;\n      }\n\n      const { operator, prefix } = parent;\n\n      // Give the state handler a chance to memoise the member, since we'll\n      // reference it twice. The second access (the set) should do the memo\n      // assignment.\n      this.memoise(member, 2);\n\n      const value = binaryExpression(\n        operator[0] as \"+\" | \"-\",\n        unaryExpression(\"+\", this.get(member)),\n        numericLiteral(1),\n      );\n\n      if (prefix) {\n        parentPath.replaceWith(this.set(member, value));\n      } else {\n        const { scope } = member;\n        const ref = scope.generateUidIdentifierBasedOnNode(node);\n        scope.push({ id: ref });\n\n        value.left = assignmentExpression(\n          \"=\",\n          cloneNode(ref),\n          // @ts-expect-error todo(flow->ts) value.left is possibly PrivateName, which is not usable here\n          value.left,\n        );\n\n        parentPath.replaceWith(\n          sequenceExpression([this.set(member, value), cloneNode(ref)]),\n        );\n      }\n      return;\n    }\n\n    // MEMBER = VALUE   ->   _set(MEMBER, VALUE)\n    // MEMBER += VALUE   ->   _set(MEMBER, _get(MEMBER) + VALUE)\n    // MEMBER ??= VALUE   ->   _get(MEMBER) ?? _set(MEMBER, VALUE)\n    if (parentPath.isAssignmentExpression({ left: node })) {\n      if (this.simpleSet) {\n        member.replaceWith(this.simpleSet(member));\n        return;\n      }\n\n      const { operator, right: value } = parentPath.node;\n\n      if (operator === \"=\") {\n        parentPath.replaceWith(this.set(member, value));\n      } else {\n        const operatorTrunc = operator.slice(0, -1);\n        if (LOGICAL_OPERATORS.includes(operatorTrunc)) {\n          // Give the state handler a chance to memoise the member, since we'll\n          // reference it twice. The first access (the get) should do the memo\n          // assignment.\n          this.memoise(member, 1);\n          parentPath.replaceWith(\n            logicalExpression(\n              operatorTrunc as t.LogicalExpression[\"operator\"],\n              this.get(member),\n              this.set(member, value),\n            ),\n          );\n        } else {\n          // Here, the second access (the set) is evaluated first.\n          this.memoise(member, 2);\n          parentPath.replaceWith(\n            this.set(\n              member,\n              binaryExpression(\n                operatorTrunc as t.BinaryExpression[\"operator\"],\n                this.get(member),\n                value,\n              ),\n            ),\n          );\n        }\n      }\n      return;\n    }\n\n    // MEMBER(ARGS) -> _call(MEMBER, ARGS)\n    if (parentPath.isCallExpression({ callee: node })) {\n      parentPath.replaceWith(this.call(member, parentPath.node.arguments));\n      return;\n    }\n\n    // MEMBER?.(ARGS) -> _optionalCall(MEMBER, ARGS)\n    if (parentPath.isOptionalCallExpression({ callee: node })) {\n      // Replace `function (a, x = a.b.#c?.()) {}` to `function (a, x = (() => a.b.#c?.())() ){}`\n      // so the temporary variable can be injected in correct scope\n      // This can be further optimized to avoid unecessary IIFE\n      if (scope.path.isPattern()) {\n        parentPath.replaceWith(\n          // The injected member will be queued and eventually transformed when visited\n          callExpression(arrowFunctionExpression([], parentPath.node), []),\n        );\n        return;\n      }\n      parentPath.replaceWith(\n        this.optionalCall(member, parentPath.node.arguments),\n      );\n      return;\n    }\n\n    // for (MEMBER of ARR)\n    // for (MEMBER in ARR)\n    // { KEY: MEMBER } = OBJ -> { KEY: _destructureSet(MEMBER) } = OBJ\n    // { KEY: MEMBER = _VALUE } = OBJ -> { KEY: _destructureSet(MEMBER) = _VALUE } = OBJ\n    // {...MEMBER} -> {..._destructureSet(MEMBER)}\n    //\n    // [MEMBER] = ARR -> [_destructureSet(MEMBER)] = ARR\n    // [MEMBER = _VALUE] = ARR -> [_destructureSet(MEMBER) = _VALUE] = ARR\n    // [...MEMBER] -> [..._destructureSet(MEMBER)]\n    if (\n      // for (MEMBER of ARR)\n      // for (MEMBER in ARR)\n      parentPath.isForXStatement({ left: node }) ||\n      // { KEY: MEMBER } = OBJ\n      (parentPath.isObjectProperty({ value: node }) &&\n        parentPath.parentPath.isObjectPattern()) ||\n      // { KEY: MEMBER = _VALUE } = OBJ\n      (parentPath.isAssignmentPattern({ left: node }) &&\n        parentPath.parentPath.isObjectProperty({ value: parent }) &&\n        parentPath.parentPath.parentPath.isObjectPattern()) ||\n      // [MEMBER] = ARR\n      parentPath.isArrayPattern() ||\n      // [MEMBER = _VALUE] = ARR\n      (parentPath.isAssignmentPattern({ left: node }) &&\n        parentPath.parentPath.isArrayPattern()) ||\n      // {...MEMBER}\n      // [...MEMBER]\n      parentPath.isRestElement()\n    ) {\n      member.replaceWith(this.destructureSet(member));\n      return;\n    }\n\n    if (parentPath.isTaggedTemplateExpression()) {\n      // MEMBER   ->   _get(MEMBER).bind(this)\n      member.replaceWith(this.boundGet(member));\n    } else {\n      // MEMBER   ->   _get(MEMBER)\n      member.replaceWith(this.get(member));\n    }\n  },\n};\n\nexport interface Handler<State> {\n  memoise?(\n    this: HandlerState<State> & State,\n    member: Member,\n    count: number,\n  ): void;\n  destructureSet(\n    this: HandlerState<State> & State,\n    member: Member,\n  ): t.Expression;\n  boundGet(this: HandlerState<State> & State, member: Member): t.Expression;\n  simpleSet?(this: HandlerState<State> & State, member: Member): t.Expression;\n  get(this: HandlerState<State> & State, member: Member): t.Expression;\n  set(\n    this: HandlerState<State> & State,\n    member: Member,\n    value: t.Expression,\n  ): t.Expression;\n  call(\n    this: HandlerState<State> & State,\n    member: Member,\n    args: t.CallExpression[\"arguments\"],\n  ): t.Expression;\n  optionalCall(\n    this: HandlerState<State> & State,\n    member: Member,\n    args: t.OptionalCallExpression[\"arguments\"],\n  ): t.Expression;\n}\n\nexport interface HandlerState<State = {}> extends Handler<State> {\n  handle(\n    this: HandlerState<State> & State,\n    member: Member,\n    noDocumentAll: boolean,\n  ): void;\n  memoiser: AssignmentMemoiser;\n}\n\n// We do not provide a default traversal visitor\n// Instead, caller passes one, and must call `state.handle` on the members\n// it wishes to be transformed.\n// Additionally, the caller must pass in a state object with at least\n// get, set, and call methods.\n// Optionally, a memoise method may be defined on the state, which will be\n// called when the member is a self-referential update.\nexport default function memberExpressionToFunctions<CustomState = {}>(\n  path: NodePath,\n  visitor: Visitor<HandlerState<CustomState>>,\n  state: Handler<CustomState> & CustomState,\n) {\n  path.traverse(visitor, {\n    ...handle,\n    ...state,\n    memoiser: new AssignmentMemoiser(),\n  });\n}\n"], "names": ["willPathCastToBoolean", "path", "maybeWrapped", "node", "parentPath", "isLogicalExpression", "operator", "right", "isSequenceExpression", "expressions", "length", "isConditional", "test", "isUnaryExpression", "isLoop", "LOGICAL_OPERATORS", "arrowFunctionExpression", "assignmentExpression", "binaryExpression", "booleanLiteral", "callExpression", "cloneNode", "conditionalExpression", "identifier", "isMemberExpression", "isOptionalCallExpression", "isOptionalMemberExpression", "isUpdateExpression", "logicalExpression", "memberExpression", "nullLiteral", "numericLiteral", "optionalCallExpression", "optionalMemberExpression", "sequenceExpression", "unaryExpression", "AssignmentMemoiser", "constructor", "_map", "WeakMap", "has", "key", "get", "record", "value", "count", "set", "toNonOptional", "base", "property", "computed", "callee", "optional", "object", "context", "scope", "maybeGenerateMemoised", "replaceWith", "arguments", "isInDetachedTree", "isProgram", "container", "<PERSON><PERSON><PERSON>", "parentNode", "handle", "memoise", "member", "noDocumentAll", "parent", "endPath", "find", "isPattern", "willEndPathCastToBoolean", "rootParentPath", "argument", "isAssignmentExpression", "left", "buildCodeFrameError", "isDeleteOperation", "isPrivateName", "startingOptional", "Error", "type", "startingProp", "startingNode", "baseNeedsMemoised", "baseRef", "parentIsOptionalCall", "isOptionalCall", "parentIsCall", "isCallExpression", "optionalCall", "call", "boundGet", "regular", "current", "endParentPath", "<PERSON><PERSON><PERSON>", "baseMemoised", "nonNullish<PERSON><PERSON>ck", "buildUndefinedNode", "nullish<PERSON><PERSON><PERSON>", "endParent", "simpleSet", "prefix", "ref", "generateUidIdentifierBasedOnNode", "push", "id", "operatorTrunc", "slice", "includes", "isForXStatement", "isObjectProperty", "isObjectPattern", "isAssignmentPattern", "isArrayPattern", "isRestElement", "destructureSet", "isTaggedTemplateExpression", "memberExpressionToFunctions", "visitor", "state", "traverse", "memoiser"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBO,SAASA,qBAAT,CAA+BC,IAA/B,EAAwD;AAC7D,QAAMC,YAAY,GAAGD,IAArB;AACA,QAAM;AAAEE,IAAAA,IAAF;AAAQC,IAAAA;AAAR,MAAuBF,YAA7B;;AACA,MAAIE,UAAU,CAACC,mBAAX,EAAJ,EAAsC;AACpC,UAAM;AAAEC,MAAAA,QAAF;AAAYC,MAAAA;AAAZ,QAAsBH,UAAU,CAACD,IAAvC;;AACA,QACEG,QAAQ,KAAK,IAAb,IACAA,QAAQ,KAAK,IADb,IAECA,QAAQ,KAAK,IAAb,IAAqBH,IAAI,KAAKI,KAHjC,EAIE;AACA,aAAOP,qBAAqB,CAACI,UAAD,CAA5B;AACD;AACF;;AACD,MAAIA,UAAU,CAACI,oBAAX,EAAJ,EAAuC;AACrC,UAAM;AAAEC,MAAAA;AAAF,QAAkBL,UAAU,CAACD,IAAnC;;AACA,QAAIM,WAAW,CAACA,WAAW,CAACC,MAAZ,GAAqB,CAAtB,CAAX,KAAwCP,IAA5C,EAAkD;AAChD,aAAOH,qBAAqB,CAACI,UAAD,CAA5B;AACD,KAFD,MAEO;AAIL,aAAO,IAAP;AACD;AACF;;AACD,SACEA,UAAU,CAACO,aAAX,CAAyB;AAAEC,IAAAA,IAAI,EAAET;AAAR,GAAzB,KACAC,UAAU,CAACS,iBAAX,CAA6B;AAAEP,IAAAA,QAAQ,EAAE;AAAZ,GAA7B,CADA,IAEAF,UAAU,CAACU,MAAX,CAAkB;AAAEF,IAAAA,IAAI,EAAET;AAAR,GAAlB,CAHF;AAKD;;;AC5CCY,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;AACAC,EAAAA;;;AAKF,MAAMC,kBAAN,CAAyB;AAEvBC,EAAAA,WAAW,GAAG;AAAA,SADNC,IACM;AACZ,SAAKA,IAAL,GAAY,IAAIC,OAAJ,EAAZ;AACD;;AAEDC,EAAAA,GAAG,CAACC,GAAD,EAAoB;AACrB,WAAO,KAAKH,IAAL,CAAUE,GAAV,CAAcC,GAAd,CAAP;AACD;;AAEDC,EAAAA,GAAG,CAACD,GAAD,EAAoB;AACrB,QAAI,CAAC,KAAKD,GAAL,CAASC,GAAT,CAAL,EAAoB;;AAEpB,UAAME,MAAM,GAAG,KAAKL,IAAL,CAAUI,GAAV,CAAcD,GAAd,CAAf;;AACA,UAAM;AAAEG,MAAAA;AAAF,QAAYD,MAAlB;AAEAA,IAAAA,MAAM,CAACE,KAAP;;AACA,QAAIF,MAAM,CAACE,KAAP,KAAiB,CAArB,EAAwB;AAGtB,aAAO5B,oBAAoB,CAAC,GAAD,EAAM2B,KAAN,EAAaH,GAAb,CAA3B;AACD;;AACD,WAAOG,KAAP;AACD;;AAEDE,EAAAA,GAAG,CAACL,GAAD,EAAoBG,KAApB,EAAmCC,KAAnC,EAAkD;AACnD,WAAO,KAAKP,IAAL,CAAUQ,GAAV,CAAcL,GAAd,EAAmB;AAAEI,MAAAA,KAAF;AAASD,MAAAA;AAAT,KAAnB,CAAP;AACD;;AA3BsB;;AA8BzB,SAASG,aAAT,CACE9C,IADF,EAEE+C,IAFF,EAGgB;AACd,QAAM;AAAE7C,IAAAA;AAAF,MAAWF,IAAjB;;AACA,MAAIyB,0BAA0B,CAACvB,IAAD,CAA9B,EAAsC;AACpC,WAAO0B,gBAAgB,CAACmB,IAAD,EAAO7C,IAAI,CAAC8C,QAAZ,EAAsB9C,IAAI,CAAC+C,QAA3B,CAAvB;AACD;;AAED,MAAIjD,IAAI,CAACwB,wBAAL,EAAJ,EAAqC;AACnC,UAAM0B,MAAM,GAAGlD,IAAI,CAACyC,GAAL,CAAS,QAAT,CAAf;;AACA,QAAIzC,IAAI,CAACE,IAAL,CAAUiD,QAAV,IAAsBD,MAAM,CAACzB,0BAAP,EAA1B,EAA+D;AAC7D,YAAM;AAAE2B,QAAAA;AAAF,UAAaF,MAAM,CAAChD,IAA1B;AACA,YAAMmD,OAAO,GAAGrD,IAAI,CAACsD,KAAL,CAAWC,qBAAX,CAAiCH,MAAjC,KAA4CA,MAA5D;AACAF,MAAAA,MAAM,CACHT,GADH,CACO,QADP,EAEGe,WAFH,CAEexC,oBAAoB,CAAC,GAAD,EAAMqC,OAAN,EAAyBD,MAAzB,CAFnC;AAIA,aAAOjC,cAAc,CAACS,gBAAgB,CAACmB,IAAD,EAAOzB,UAAU,CAAC,MAAD,CAAjB,CAAjB,EAA6C,CAChE+B,OADgE,EAEhE,GAAGrD,IAAI,CAACE,IAAL,CAAUuD,SAFmD,CAA7C,CAArB;AAID;;AAED,WAAOtC,cAAc,CAAC4B,IAAD,EAAO/C,IAAI,CAACE,IAAL,CAAUuD,SAAjB,CAArB;AACD;;AAED,SAAOzD,IAAI,CAACE,IAAZ;AACD;;AAMD,SAASwD,gBAAT,CAA0B1D,IAA1B,EAA0C;AACxC,SAAOA,IAAP,EAAa;AACX,QAAIA,IAAI,CAAC2D,SAAL,EAAJ,EAAsB;AAEtB,UAAM;AAAExD,MAAAA,UAAF;AAAcyD,MAAAA,SAAd;AAAyBC,MAAAA;AAAzB,QAAqC7D,IAA3C;AACA,UAAM8D,UAAU,GAAG3D,UAAU,CAACD,IAA9B;;AACA,QAAI2D,OAAJ,EAAa;AACX,UAAID,SAAS,KAAKE,UAAU,CAACD,OAAD,CAA5B,EAAuC,OAAO,IAAP;AACxC,KAFD,MAEO;AACL,UAAID,SAAS,KAAKE,UAAlB,EAA8B,OAAO,IAAP;AAC/B;;AAED9D,IAAAA,IAAI,GAAGG,UAAP;AACD;;AAED,SAAO,KAAP;AACD;;AAID,MAAM4D,MAAM,GAAG;AACbC,EAAAA,OAAO,GAAG,EADG;;AAKbD,EAAAA,MAAM,CAAqBE,MAArB,EAAqCC,aAArC,EAA6D;AACjE,UAAM;AAAEhE,MAAAA,IAAF;AAAQiE,MAAAA,MAAR;AAAgBhE,MAAAA,UAAhB;AAA4BmD,MAAAA;AAA5B,QAAsCW,MAA5C;;AAEA,QAAIA,MAAM,CAACxC,0BAAP,EAAJ,EAAyC;AAEvC,UAAIiC,gBAAgB,CAACO,MAAD,CAApB,EAA8B;AAW9B,YAAMG,OAAO,GAAGH,MAAM,CAACI,IAAP,CAAY,CAAC;AAAEnE,QAAAA,IAAF;AAAQiE,QAAAA;AAAR,OAAD,KAAsB;AAChD,YAAI1C,0BAA0B,CAAC0C,MAAD,CAA9B,EAAwC;AAItC,iBAAOA,MAAM,CAAChB,QAAP,IAAmBgB,MAAM,CAACf,MAAP,KAAkBlD,IAA5C;AACD;;AACD,YAAIsB,wBAAwB,CAAC2C,MAAD,CAA5B,EAAsC;AAIpC,iBAEGjE,IAAI,KAAK+D,MAAM,CAAC/D,IAAhB,IAAwBiE,MAAM,CAAChB,QAAhC,IAA6CgB,MAAM,CAACjB,MAAP,KAAkBhD,IAFjE;AAID;;AACD,eAAO,IAAP;AACD,OAjBe,CAAhB;;AAsBA,UAAIoD,KAAK,CAACtD,IAAN,CAAWsE,SAAX,EAAJ,EAA4B;AAC1BF,QAAAA,OAAO,CAACZ,WAAR,CAEErC,cAAc,CAACJ,uBAAuB,CAAC,EAAD,EAAKqD,OAAO,CAAClE,IAAb,CAAxB,EAA4C,EAA5C,CAFhB;AAIA;AACD;;AAED,YAAMqE,wBAAwB,GAAGxE,qBAAqB,CAACqE,OAAD,CAAtD;AAEA,YAAMI,cAAc,GAAGJ,OAAO,CAACjE,UAA/B;;AACA,UACEqE,cAAc,CAAC9C,kBAAf,CAAkC;AAAE+C,QAAAA,QAAQ,EAAEvE;AAAZ,OAAlC,KACAsE,cAAc,CAACE,sBAAf,CAAsC;AAAEC,QAAAA,IAAI,EAAEzE;AAAR,OAAtC,CAFF,EAGE;AACA,cAAM+D,MAAM,CAACW,mBAAP,CAA4B,yBAA5B,CAAN;AACD;;AACD,YAAMC,iBAAiB,GAAGL,cAAc,CAAC5D,iBAAf,CAAiC;AACzDP,QAAAA,QAAQ,EAAE;AAD+C,OAAjC,CAA1B;;AAGA,UACEwE,iBAAiB,IACjBT,OAAO,CAAC3C,0BAAR,EADA,IAEA2C,OAAO,CAAC3B,GAAR,CAAY,UAAZ,EAAwBqC,aAAxB,EAHF,EAIE;AAGA,cAAMb,MAAM,CAACW,mBAAP,CACH,sCADG,CAAN;AAGD;;AAUD,UAAIG,gBAAwC,GAAGd,MAA/C;;AACA,eAAS;AACP,YAAIc,gBAAgB,CAACtD,0BAAjB,EAAJ,EAAmD;AACjD,cAAIsD,gBAAgB,CAAC7E,IAAjB,CAAsBiD,QAA1B,EAAoC;AACpC4B,UAAAA,gBAAgB,GAAGA,gBAAgB,CAACtC,GAAjB,CAAqB,QAArB,CAAnB;AACA;AACD,SAJD,MAIO,IAAIsC,gBAAgB,CAACvD,wBAAjB,EAAJ,EAAiD;AACtD,cAAIuD,gBAAgB,CAAC7E,IAAjB,CAAsBiD,QAA1B,EAAoC;AACpC4B,UAAAA,gBAAgB,GAAGA,gBAAgB,CAACtC,GAAjB,CAAqB,QAArB,CAAnB;AACA;AACD;;AAED,cAAM,IAAIuC,KAAJ,CACH,8BAA6BD,gBAAgB,CAAC7E,IAAjB,CAAsB+E,IAAK,EADrD,CAAN;AAGD;;AAED,YAAMC,YAAY,GAAGH,gBAAgB,CAACtD,0BAAjB,KACjB,QADiB,GAEjB,QAFJ;AAGA,YAAM0D,YAAY,GAAGJ,gBAAgB,CAAC7E,IAAjB,CAAsBgF,YAAtB,CAArB;AACA,YAAME,iBAAiB,GAAG9B,KAAK,CAACC,qBAAN,CAA4B4B,YAA5B,CAA1B;AACA,YAAME,OAAO,GAAGD,iBAAH,WAAGA,iBAAH,GAAwBD,YAArC;AAIA,YAAMG,oBAAoB,GAAGnF,UAAU,CAACqB,wBAAX,CAAoC;AAC/D0B,QAAAA,MAAM,EAAEhD;AADuD,OAApC,CAA7B;;AAMA,YAAMqF,cAAc,GAClBpB,MADqB,IAEkBmB,oBAFzC;;AAIA,YAAME,YAAY,GAAGrF,UAAU,CAACsF,gBAAX,CAA4B;AAAEvC,QAAAA,MAAM,EAAEhD;AAAV,OAA5B,CAArB;AACA6E,MAAAA,gBAAgB,CAACvB,WAAjB,CAA6BV,aAAa,CAACiC,gBAAD,EAAmBM,OAAnB,CAA1C;;AACA,UAAIE,cAAc,CAAA,CAAlB,EAA4B;AAC1B,YAAIpB,MAAM,CAAChB,QAAX,EAAqB;AACnBhD,UAAAA,UAAU,CAACqD,WAAX,CAAuB,KAAKkC,YAAL,CAAkBzB,MAAlB,EAA0BE,MAAM,CAACV,SAAjC,CAAvB;AACD,SAFD,MAEO;AACLtD,UAAAA,UAAU,CAACqD,WAAX,CAAuB,KAAKmC,IAAL,CAAU1B,MAAV,EAAkBE,MAAM,CAACV,SAAzB,CAAvB;AACD;AACF,OAND,MAMO,IAAI+B,YAAJ,EAAkB;AAEvBvB,QAAAA,MAAM,CAACT,WAAP,CAAmB,KAAKoC,QAAL,CAAc3B,MAAd,CAAnB;AACD,OAHM,MAGA;AACLA,QAAAA,MAAM,CAACT,WAAP,CAAmB,KAAKf,GAAL,CAASwB,MAAT,CAAnB;AACD;;AAED,UAAI4B,OAAqB,GAAG5B,MAAM,CAAC/D,IAAnC;;AACA,WAAK,IAAI4F,OAAiB,GAAG7B,MAA7B,EAAqC6B,OAAO,KAAK1B,OAAjD,GAA4D;AAC1D,cAAMjE,UAAU,GAAG2F,OAAO,CAAC3F,UAA3B;;AAEA,YACEA,UAAU,KAAKiE,OAAf,IACAmB,cAAc,CAAA,CADd,IAEApB,MAAM,CAAChB,QAHT,EAIE;AACA0C,UAAAA,OAAO,GAAG1F,UAAU,CAACD,IAArB;AACA;AACD;;AACD2F,QAAAA,OAAO,GAAG/C,aAAa,CAAC3C,UAAD,EAAa0F,OAAb,CAAvB;AACAC,QAAAA,OAAO,GAAG3F,UAAV;AACD;;AAED,UAAIkD,OAAJ;AACA,YAAM0C,aAAa,GAAG3B,OAAO,CAACjE,UAA9B;;AACA,UACEoB,kBAAkB,CAACsE,OAAD,CAAlB,IACAE,aAAa,CAACvE,wBAAd,CAAuC;AACrC0B,QAAAA,MAAM,EAAEkB,OAAO,CAAClE,IADqB;AAErCiD,QAAAA,QAAQ,EAAE;AAF2B,OAAvC,CAFF,EAME;AACA,cAAM;AAAEC,UAAAA;AAAF,YAAayC,OAAnB;AACAxC,QAAAA,OAAO,GAAGY,MAAM,CAACX,KAAP,CAAaC,qBAAb,CAAmCH,MAAnC,CAAV;;AACA,YAAIC,OAAJ,EAAa;AACXwC,UAAAA,OAAO,CAACzC,MAAR,GAAiBpC,oBAAoB,CAAC,GAAD,EAAMqC,OAAN,EAAeD,MAAf,CAArC;AACD;AACF;;AAED,UAAI4C,eAAyB,GAAG5B,OAAhC;;AACA,UAAIS,iBAAJ,EAAuB;AACrBmB,QAAAA,eAAe,GAAGD,aAAlB;AACAF,QAAAA,OAAO,GAAGE,aAAa,CAAC7F,IAAxB;AACD;;AAED,YAAM+F,YAAY,GAAGb,iBAAiB,GAClCpE,oBAAoB,CAAC,GAAD,EAAMI,SAAS,CAACiE,OAAD,CAAf,EAA0BjE,SAAS,CAAC+D,YAAD,CAAnC,CADc,GAElC/D,SAAS,CAACiE,OAAD,CAFb;;AAIA,UAAId,wBAAJ,EAA8B;AAC5B,YAAI2B,eAAJ;;AACA,YAAIhC,aAAJ,EAAmB;AACjBgC,UAAAA,eAAe,GAAGjF,gBAAgB,CAAC,IAAD,EAAOgF,YAAP,EAAqBpE,WAAW,EAAhC,CAAlC;AACD,SAFD,MAEO;AACLqE,UAAAA,eAAe,GAAGvE,iBAAiB,CACjC,IADiC,EAEjCV,gBAAgB,CAAC,KAAD,EAAQgF,YAAR,EAAsBpE,WAAW,EAAjC,CAFiB,EAGjCZ,gBAAgB,CACd,KADc,EAEdG,SAAS,CAACiE,OAAD,CAFK,EAGd/B,KAAK,CAAC6C,kBAAN,EAHc,CAHiB,CAAnC;AASD;;AACDH,QAAAA,eAAe,CAACxC,WAAhB,CACE7B,iBAAiB,CAAC,IAAD,EAAOuE,eAAP,EAAwBL,OAAxB,CADnB;AAGD,OAlBD,MAkBO;AACL,YAAIO,YAAJ;;AACA,YAAIlC,aAAJ,EAAmB;AACjBkC,UAAAA,YAAY,GAAGnF,gBAAgB,CAAC,IAAD,EAAOgF,YAAP,EAAqBpE,WAAW,EAAhC,CAA/B;AACD,SAFD,MAEO;AACLuE,UAAAA,YAAY,GAAGzE,iBAAiB,CAC9B,IAD8B,EAE9BV,gBAAgB,CAAC,KAAD,EAAQgF,YAAR,EAAsBpE,WAAW,EAAjC,CAFc,EAG9BZ,gBAAgB,CACd,KADc,EAEdG,SAAS,CAACiE,OAAD,CAFK,EAGd/B,KAAK,CAAC6C,kBAAN,EAHc,CAHc,CAAhC;AASD;;AAEDH,QAAAA,eAAe,CAACxC,WAAhB,CACEnC,qBAAqB,CACnB+E,YADmB,EAEnBvB,iBAAiB,GACb3D,cAAc,CAAC,IAAD,CADD,GAEboC,KAAK,CAAC6C,kBAAN,EAJe,EAKnBN,OALmB,CADvB;AASD;;AAGD,UAAIxC,OAAJ,EAAa;AACX,cAAMgD,SAAS,GAAGN,aAAa,CAAC7F,IAAhC;AACA6F,QAAAA,aAAa,CAACvC,WAAd,CACEzB,sBAAsB,CACpBC,wBAAwB,CACtBqE,SAAS,CAACnD,MADY,EAEtB5B,UAAU,CAAC,MAAD,CAFY,EAGtB,KAHsB,EAItB,IAJsB,CADJ,EAOpB,CAACF,SAAS,CAACiC,OAAD,CAAV,EAAqB,GAAGgD,SAAS,CAAC5C,SAAlC,CAPoB,EAQpB,KARoB,CADxB;AAYD;;AAED;AACD;;AAID,QAAI/B,kBAAkB,CAACyC,MAAD,EAAS;AAAEM,MAAAA,QAAQ,EAAEvE;AAAZ,KAAT,CAAtB,EAAoD;AAClD,UAAI,KAAKoG,SAAT,EAAoB;AAClBrC,QAAAA,MAAM,CAACT,WAAP,CAAmB,KAAK8C,SAAL,CAAerC,MAAf,CAAnB;AACA;AACD;;AAED,YAAM;AAAE5D,QAAAA,QAAF;AAAYkG,QAAAA;AAAZ,UAAuBpC,MAA7B;AAKA,WAAKH,OAAL,CAAaC,MAAb,EAAqB,CAArB;AAEA,YAAMtB,KAAK,GAAG1B,gBAAgB,CAC5BZ,QAAQ,CAAC,CAAD,CADoB,EAE5B6B,eAAe,CAAC,GAAD,EAAM,KAAKO,GAAL,CAASwB,MAAT,CAAN,CAFa,EAG5BnC,cAAc,CAAC,CAAD,CAHc,CAA9B;;AAMA,UAAIyE,MAAJ,EAAY;AACVpG,QAAAA,UAAU,CAACqD,WAAX,CAAuB,KAAKX,GAAL,CAASoB,MAAT,EAAiBtB,KAAjB,CAAvB;AACD,OAFD,MAEO;AACL,cAAM;AAAEW,UAAAA;AAAF,YAAYW,MAAlB;AACA,cAAMuC,GAAG,GAAGlD,KAAK,CAACmD,gCAAN,CAAuCvG,IAAvC,CAAZ;AACAoD,QAAAA,KAAK,CAACoD,IAAN,CAAW;AAAEC,UAAAA,EAAE,EAAEH;AAAN,SAAX;AAEA7D,QAAAA,KAAK,CAACgC,IAAN,GAAa3D,oBAAoB,CAC/B,GAD+B,EAE/BI,SAAS,CAACoF,GAAD,CAFsB,EAI/B7D,KAAK,CAACgC,IAJyB,CAAjC;AAOAxE,QAAAA,UAAU,CAACqD,WAAX,CACEvB,kBAAkB,CAAC,CAAC,KAAKY,GAAL,CAASoB,MAAT,EAAiBtB,KAAjB,CAAD,EAA0BvB,SAAS,CAACoF,GAAD,CAAnC,CAAD,CADpB;AAGD;;AACD;AACD;;AAKD,QAAIrG,UAAU,CAACuE,sBAAX,CAAkC;AAAEC,MAAAA,IAAI,EAAEzE;AAAR,KAAlC,CAAJ,EAAuD;AACrD,UAAI,KAAKoG,SAAT,EAAoB;AAClBrC,QAAAA,MAAM,CAACT,WAAP,CAAmB,KAAK8C,SAAL,CAAerC,MAAf,CAAnB;AACA;AACD;;AAED,YAAM;AAAE5D,QAAAA,QAAF;AAAYC,QAAAA,KAAK,EAAEqC;AAAnB,UAA6BxC,UAAU,CAACD,IAA9C;;AAEA,UAAIG,QAAQ,KAAK,GAAjB,EAAsB;AACpBF,QAAAA,UAAU,CAACqD,WAAX,CAAuB,KAAKX,GAAL,CAASoB,MAAT,EAAiBtB,KAAjB,CAAvB;AACD,OAFD,MAEO;AACL,cAAMiE,aAAa,GAAGvG,QAAQ,CAACwG,KAAT,CAAe,CAAf,EAAkB,CAAC,CAAnB,CAAtB;;AACA,YAAI/F,iBAAiB,CAACgG,QAAlB,CAA2BF,aAA3B,CAAJ,EAA+C;AAI7C,eAAK5C,OAAL,CAAaC,MAAb,EAAqB,CAArB;AACA9D,UAAAA,UAAU,CAACqD,WAAX,CACE7B,iBAAiB,CACfiF,aADe,EAEf,KAAKnE,GAAL,CAASwB,MAAT,CAFe,EAGf,KAAKpB,GAAL,CAASoB,MAAT,EAAiBtB,KAAjB,CAHe,CADnB;AAOD,SAZD,MAYO;AAEL,eAAKqB,OAAL,CAAaC,MAAb,EAAqB,CAArB;AACA9D,UAAAA,UAAU,CAACqD,WAAX,CACE,KAAKX,GAAL,CACEoB,MADF,EAEEhD,gBAAgB,CACd2F,aADc,EAEd,KAAKnE,GAAL,CAASwB,MAAT,CAFc,EAGdtB,KAHc,CAFlB,CADF;AAUD;AACF;;AACD;AACD;;AAGD,QAAIxC,UAAU,CAACsF,gBAAX,CAA4B;AAAEvC,MAAAA,MAAM,EAAEhD;AAAV,KAA5B,CAAJ,EAAmD;AACjDC,MAAAA,UAAU,CAACqD,WAAX,CAAuB,KAAKmC,IAAL,CAAU1B,MAAV,EAAkB9D,UAAU,CAACD,IAAX,CAAgBuD,SAAlC,CAAvB;AACA;AACD;;AAGD,QAAItD,UAAU,CAACqB,wBAAX,CAAoC;AAAE0B,MAAAA,MAAM,EAAEhD;AAAV,KAApC,CAAJ,EAA2D;AAIzD,UAAIoD,KAAK,CAACtD,IAAN,CAAWsE,SAAX,EAAJ,EAA4B;AAC1BnE,QAAAA,UAAU,CAACqD,WAAX,CAEErC,cAAc,CAACJ,uBAAuB,CAAC,EAAD,EAAKZ,UAAU,CAACD,IAAhB,CAAxB,EAA+C,EAA/C,CAFhB;AAIA;AACD;;AACDC,MAAAA,UAAU,CAACqD,WAAX,CACE,KAAKkC,YAAL,CAAkBzB,MAAlB,EAA0B9D,UAAU,CAACD,IAAX,CAAgBuD,SAA1C,CADF;AAGA;AACD;;AAWD,QAGEtD,UAAU,CAAC4G,eAAX,CAA2B;AAAEpC,MAAAA,IAAI,EAAEzE;AAAR,KAA3B,KAECC,UAAU,CAAC6G,gBAAX,CAA4B;AAAErE,MAAAA,KAAK,EAAEzC;AAAT,KAA5B,KACCC,UAAU,CAACA,UAAX,CAAsB8G,eAAtB,EAHF,IAKC9G,UAAU,CAAC+G,mBAAX,CAA+B;AAAEvC,MAAAA,IAAI,EAAEzE;AAAR,KAA/B,KACCC,UAAU,CAACA,UAAX,CAAsB6G,gBAAtB,CAAuC;AAAErE,MAAAA,KAAK,EAAEwB;AAAT,KAAvC,CADD,IAEChE,UAAU,CAACA,UAAX,CAAsBA,UAAtB,CAAiC8G,eAAjC,EAPF,IASA9G,UAAU,CAACgH,cAAX,EATA,IAWChH,UAAU,CAAC+G,mBAAX,CAA+B;AAAEvC,MAAAA,IAAI,EAAEzE;AAAR,KAA/B,KACCC,UAAU,CAACA,UAAX,CAAsBgH,cAAtB,EAZF,IAeAhH,UAAU,CAACiH,aAAX,EAlBF,EAmBE;AACAnD,MAAAA,MAAM,CAACT,WAAP,CAAmB,KAAK6D,cAAL,CAAoBpD,MAApB,CAAnB;AACA;AACD;;AAED,QAAI9D,UAAU,CAACmH,0BAAX,EAAJ,EAA6C;AAE3CrD,MAAAA,MAAM,CAACT,WAAP,CAAmB,KAAKoC,QAAL,CAAc3B,MAAd,CAAnB;AACD,KAHD,MAGO;AAELA,MAAAA,MAAM,CAACT,WAAP,CAAmB,KAAKf,GAAL,CAASwB,MAAT,CAAnB;AACD;AACF;;AAxYY,CAAf;AAybe,SAASsD,2BAAT,CACbvH,IADa,EAEbwH,OAFa,EAGbC,KAHa,EAIb;AACAzH,EAAAA,IAAI,CAAC0H,QAAL,CAAcF,OAAd,oBACKzD,MADL,EAEK0D,KAFL;AAGEE,IAAAA,QAAQ,EAAE,IAAIxF,kBAAJ;AAHZ;AAKD;;;;"}