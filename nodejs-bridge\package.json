{"name": "tradingview-egx-bridge", "version": "1.0.0", "description": "Node.js bridge to fetch EGX stock data from TradingView for Python trading bot", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test.js"}, "dependencies": {"@mathieuc/tradingview": "^3.5.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "winston": "^3.11.0", "axios": "^1.6.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["tradingview", "egx", "stocks", "api", "bridge", "python", "trading"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=16.0.0"}}