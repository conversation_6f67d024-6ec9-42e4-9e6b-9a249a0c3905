{"name": "@vitest/utils", "type": "module", "version": "0.31.4", "description": "Shared Vitest utility functions", "license": "MIT", "funding": "https://opencollective.com/vitest", "homepage": "https://github.com/vitest-dev/vitest/tree/main/packages/utils#readme", "repository": {"type": "git", "url": "git+https://github.com/vitest-dev/vitest.git", "directory": "packages/utils"}, "bugs": {"url": "https://github.com/vitest-dev/vitest/issues"}, "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./diff": {"types": "./dist/diff.d.ts", "import": "./dist/diff.js"}, "./helpers": {"types": "./dist/helpers.d.ts", "import": "./dist/helpers.js"}, "./*": "./*"}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist", "*.d.ts"], "dependencies": {"concordance": "^5.0.4", "loupe": "^2.3.6", "pretty-format": "^27.5.1"}, "scripts": {"build": "rimraf dist && rollup -c", "dev": "rollup -c --watch"}}