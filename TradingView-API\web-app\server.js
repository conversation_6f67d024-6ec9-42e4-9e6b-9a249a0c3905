const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const TradingView = require('../main');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// EGX Stock configuration
const egxStocks = [
  { symbol: 'EGX:EGX30', name: 'EGX 30 Index', type: 'index', color: '#1f77b4' },
  { symbol: 'EGX:COMI', name: 'Commercial International Bank', type: 'bank', color: '#ff7f0e' },
  { symbol: 'EGX:SWDY', name: 'El Sewedy Electric', type: 'industrial', color: '#2ca02c' },
  { symbol: 'EGX:FWRY', name: 'Fawry Banking Technology', type: 'fintech', color: '#d62728' },
  { symbol: 'EGX:PHDC', name: 'Palm Hills Development', type: 'real_estate', color: '#9467bd' },
  { symbol: 'EGX:ORAS', name: 'Orascom Construction', type: 'construction', color: '#8c564b' },
  { symbol: 'EGX:ISPH', name: 'Ibn Sina Pharma', type: 'pharma', color: '#e377c2' },
  { symbol: 'EGX:HRHO', name: 'EFG Holding', type: 'financial', color: '#7f7f7f' }
];

let stockData = {};
let charts = [];
let tvClient = null;

// Initialize TradingView connection
function initializeTradingView() {
  console.log('🇪🇬 Initializing EGX Stock Monitor Web Server...');
  
  tvClient = new TradingView.Client();
  
  egxStocks.forEach((stock, index) => {
    const chart = new tvClient.Session.Chart();
    charts.push(chart);
    
    chart.setMarket(stock.symbol, {
      timeframe: '1D',
    });

    chart.onSymbolLoaded(() => {
      console.log(`✅ ${stock.name} connected`);
      
      // Send connection status to clients
      io.emit('stockConnected', {
        symbol: stock.symbol,
        name: stock.name,
        status: 'connected'
      });
    });

    chart.onUpdate(() => {
      if (!chart.periods[0]) return;
      
      const currentPrice = chart.periods[0].close;
      const openPrice = chart.periods[0].open;
      const highPrice = chart.periods[0].high;
      const lowPrice = chart.periods[0].low;
      const volume = chart.periods[0].volume || 0;
      
      const dailyChange = currentPrice - openPrice;
      const dailyChangePercent = ((dailyChange / openPrice) * 100);
      
      const stockInfo = {
        symbol: stock.symbol,
        name: stock.name,
        type: stock.type,
        color: stock.color,
        price: currentPrice,
        open: openPrice,
        high: highPrice,
        low: lowPrice,
        volume: volume,
        change: dailyChange,
        changePercent: dailyChangePercent,
        timestamp: new Date().toISOString()
      };
      
      stockData[stock.symbol] = stockInfo;
      
      // Broadcast to all connected clients
      io.emit('stockUpdate', stockInfo);
    });

    chart.onError((...err) => {
      console.error(`❌ Error with ${stock.name}:`, ...err);
      io.emit('stockError', {
        symbol: stock.symbol,
        name: stock.name,
        error: err.join(' ')
      });
    });
  });
}

// API Routes
app.get('/api/stocks', (req, res) => {
  res.json(Object.values(stockData));
});

app.get('/api/stocks/:symbol', (req, res) => {
  const symbol = req.params.symbol;
  const stock = stockData[symbol];
  
  if (stock) {
    res.json(stock);
  } else {
    res.status(404).json({ error: 'Stock not found' });
  }
});

// Technical Analysis endpoint
app.get('/api/technical-analysis/:symbol', async (req, res) => {
  try {
    const symbol = req.params.symbol;
    const markets = await TradingView.searchMarketV3(symbol);
    
    if (markets.length === 0) {
      return res.status(404).json({ error: 'Market not found' });
    }
    
    const market = markets[0];
    const ta = await market.getTA();
    
    res.json({
      symbol: symbol,
      description: market.description,
      recommendation: ta.summary.RECOMMENDATION,
      buy: ta.summary.BUY,
      sell: ta.summary.SELL,
      neutral: ta.summary.NEUTRAL,
      indicators: {
        rsi: ta.indicators.RSI,
        macd: ta.indicators.MACD,
        stoch: ta.indicators.Stoch,
        cci: ta.indicators.CCI
      },
      movingAverages: ta.summary.MA,
      oscillators: ta.summary.OSCILLATORS
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('👤 Client connected:', socket.id);
  
  // Send current stock data to new client
  socket.emit('initialData', Object.values(stockData));
  
  // Handle client requests for technical analysis
  socket.on('requestTechnicalAnalysis', async (symbol) => {
    try {
      const markets = await TradingView.searchMarketV3(symbol);
      if (markets.length > 0) {
        const market = markets[0];
        const ta = await market.getTA();
        
        socket.emit('technicalAnalysis', {
          symbol: symbol,
          data: ta
        });
      }
    } catch (error) {
      socket.emit('error', { message: error.message });
    }
  });
  
  socket.on('disconnect', () => {
    console.log('👤 Client disconnected:', socket.id);
  });
});

// Start server
const PORT = process.env.PORT || 3000;

server.listen(PORT, () => {
  console.log(`🌐 EGX Stock Monitor Web Server running on http://localhost:${PORT}`);
  console.log('📊 Dashboard available at: http://localhost:3000');
  
  // Initialize TradingView after server starts
  initializeTradingView();
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  if (tvClient) {
    tvClient.end();
  }
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
