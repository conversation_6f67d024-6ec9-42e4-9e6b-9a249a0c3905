#!/usr/bin/env python3
"""
🇪🇬 EGX Smart Money Concepts Trading Platform
Multi-page application for comprehensive SMC analysis
"""

import streamlit as st

st.set_page_config(
    page_title="🇪🇬 EGX SMC Trading Platform",
    page_icon="🇪🇬",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 2rem;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .feature-card {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid #007bff;
        margin: 1rem 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .page-card {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        margin: 1rem 0;
        border: 1px solid #e0e0e0;
    }
    .csv-card { border-left: 4px solid #28a745; }
    .live-card { border-left: 4px solid #007bff; }
</style>
""", unsafe_allow_html=True)

def main():
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🇪🇬 EGX Smart Money Concepts Trading Platform</h1>
        <p>Professional SMC Analysis for Egyptian Exchange</p>
        <p><strong>Choose your analysis method below</strong></p>
    </div>
    """, unsafe_allow_html=True)

    # Introduction
    st.markdown("""
    ## 🎯 Welcome to the Ultimate EGX SMC Platform
    
    This comprehensive platform provides **two powerful analysis methods** for Egyptian Exchange stocks using Smart Money Concepts.
    """)

    # Page selection cards
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        <div class="page-card csv-card">
            <h3>📁 Historical CSV Analysis</h3>
            <p><strong>Perfect for backtesting and historical analysis</strong></p>
            <ul>
                <li>📊 Analyze historical CSV data files</li>
                <li>🔍 Complete SMC analysis suite</li>
                <li>📈 Professional charts and visualizations</li>
                <li>⚡ Trading signals and confluence analysis</li>
                <li>🇪🇬 EGX-optimized parameters</li>
            </ul>
            <p><em>Use this for analyzing historical patterns and backtesting strategies</em></p>
        </div>
        """, unsafe_allow_html=True)
        
        if st.button("📁 Go to CSV Analysis", type="primary", use_container_width=True):
            st.switch_page("pages/2_📁_CSV_Historical_Analysis.py")
    
    with col2:
        st.markdown("""
        <div class="page-card live-card">
            <h3>📡 TradingView Live Analysis</h3>
            <p><strong>Real-time data for live trading decisions</strong></p>
            <ul>
                <li>📡 Live TradingView data integration</li>
                <li>⏰ Multiple timeframes (1D, 4H, 1H, 30M, 15M)</li>
                <li>🔄 Synchronized chart and analysis data</li>
                <li>⚡ Real-time SMC signals</li>
                <li>📈 Professional TradingView-style charts</li>
            </ul>
            <p><em>Use this for live trading with real-time market data</em></p>
        </div>
        """, unsafe_allow_html=True)
        
        if st.button("📡 Go to Live Analysis", type="primary", use_container_width=True):
            st.switch_page("pages/1_📡_TradingView_Live_Analysis.py")

    # Features overview
    st.markdown("---")
    st.markdown("## 🚀 Platform Features")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        <div class="feature-card">
            <h4>🔍 Complete SMC Suite</h4>
            <ul>
                <li>Order Blocks detection</li>
                <li>Fair Value Gaps analysis</li>
                <li>Liquidity Zones mapping</li>
                <li>Displacement identification</li>
                <li>Equal Highs/Lows detection</li>
                <li>Candlestick patterns</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="feature-card">
            <h4>📊 Advanced Analysis</h4>
            <ul>
                <li>Confluence analysis</li>
                <li>Signal strength calculation</li>
                <li>Risk/reward ratios</li>
                <li>Support/resistance levels</li>
                <li>Market structure analysis</li>
                <li>Trading recommendations</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown("""
        <div class="feature-card">
            <h4>🇪🇬 EGX Optimized</h4>
            <ul>
                <li>Egyptian Exchange focus</li>
                <li>EGX-specific parameters</li>
                <li>Local market conditions</li>
                <li>Currency in EGP</li>
                <li>Major EGX stocks supported</li>
                <li>Professional-grade analysis</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

    # Getting started
    st.markdown("---")
    st.markdown("""
    ## 🚀 Getting Started
    
    ### For Historical Analysis:
    1. **Click "CSV Analysis"** above
    2. **Select your CSV file** from the data directory
    3. **Run SMC analysis** on historical data
    4. **Review results** and backtest strategies
    
    ### For Live Trading:
    1. **Click "Live Analysis"** above  
    2. **Select EGX stock** and timeframe
    3. **Fetch live TradingView data**
    4. **Get real-time SMC signals**
    
    ### 📂 Data Requirements:
    - **CSV files**: Should contain `open`, `high`, `low`, `close`, `volume` columns
    - **Minimum**: 50 data points for reliable analysis
    - **Location**: Place CSV files in `data/historical/` directory
    
    ### 🎯 Pro Tips:
    - Use **CSV analysis** for backtesting and strategy development
    - Use **Live analysis** for real-time trading decisions
    - Combine both methods for comprehensive market understanding
    - Always consider risk management in your trading decisions
    """)

    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; padding: 1rem;">
        <p>🇪🇬 <strong>EGX Smart Money Concepts Trading Platform</strong> 📈</p>
        <p>Professional SMC Analysis • Egyptian Exchange • Institutional-Grade Tools</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
