<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EGX Stock Monitor - Real-time Egyptian Exchange</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .status-bar {
            background: rgba(255, 255, 255, 0.9);
            padding: 0.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .market-overview {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .overview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .overview-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #3498db;
        }

        .overview-card h3 {
            color: #2c3e50;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .overview-card .value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #27ae60;
        }

        .stocks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .stock-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-left: 5px solid #3498db;
        }

        .stock-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .stock-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .stock-name {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .stock-symbol {
            font-size: 0.8rem;
            color: #7f8c8d;
            background: #ecf0f1;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
        }

        .stock-type {
            font-size: 0.75rem;
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            color: white;
            background: #95a5a6;
        }

        .stock-price {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin: 0.5rem 0;
        }

        .stock-change {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .change-positive {
            color: #27ae60;
        }

        .change-negative {
            color: #e74c3c;
        }

        .stock-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.5rem;
            font-size: 0.9rem;
            color: #7f8c8d;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
        }

        .loading {
            text-align: center;
            padding: 3rem;
            color: #7f8c8d;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: #e74c3c;
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }

        .last-update {
            text-align: center;
            color: #7f8c8d;
            font-size: 0.8rem;
            margin-top: 2rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .stocks-grid {
                grid-template-columns: 1fr;
            }
            
            .header {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <i class="fas fa-chart-line"></i>
            EGX Stock Monitor
        </h1>
        <div class="subtitle">Real-time Egyptian Exchange Market Data</div>
    </div>

    <div class="status-bar">
        <div class="connection-status">
            <div class="status-dot"></div>
            <span id="connectionStatus">Connecting...</span>
        </div>
        <div id="lastUpdate">Last Update: --</div>
    </div>

    <div class="container">
        <div class="market-overview">
            <h2><i class="fas fa-chart-area"></i> Market Overview</h2>
            <div class="overview-grid">
                <div class="overview-card">
                    <h3>Connected Stocks</h3>
                    <div class="value" id="connectedCount">0</div>
                </div>
                <div class="overview-card">
                    <h3>Market Gainers</h3>
                    <div class="value" id="gainersCount">0</div>
                </div>
                <div class="overview-card">
                    <h3>Market Losers</h3>
                    <div class="value" id="losersCount">0</div>
                </div>
                <div class="overview-card">
                    <h3>Total Volume</h3>
                    <div class="value" id="totalVolume">0</div>
                </div>
            </div>
        </div>

        <div id="stocksContainer">
            <div class="loading">
                <div class="spinner"></div>
                <p>Loading EGX stock data...</p>
            </div>
        </div>

        <div class="last-update" id="footerUpdate">
            Powered by TradingView API | Real-time Egyptian Exchange Data
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        // Initialize Socket.IO connection
        const socket = io();

        let stocksData = {};
        let connectedStocks = 0;

        // DOM elements
        const stocksContainer = document.getElementById('stocksContainer');
        const connectionStatus = document.getElementById('connectionStatus');
        const lastUpdate = document.getElementById('lastUpdate');
        const connectedCount = document.getElementById('connectedCount');
        const gainersCount = document.getElementById('gainersCount');
        const losersCount = document.getElementById('losersCount');
        const totalVolume = document.getElementById('totalVolume');

        // Socket event handlers
        socket.on('connect', () => {
            connectionStatus.textContent = 'Connected to EGX Monitor';
            connectionStatus.style.color = '#27ae60';
        });

        socket.on('disconnect', () => {
            connectionStatus.textContent = 'Disconnected';
            connectionStatus.style.color = '#e74c3c';
        });

        socket.on('initialData', (stocks) => {
            stocks.forEach(stock => {
                stocksData[stock.symbol] = stock;
            });
            renderStocks();
            updateOverview();
        });

        socket.on('stockConnected', (data) => {
            connectedStocks++;
            updateOverview();
        });

        socket.on('stockUpdate', (stock) => {
            stocksData[stock.symbol] = stock;
            updateStockCard(stock);
            updateOverview();
            lastUpdate.textContent = `Last Update: ${new Date().toLocaleTimeString()}`;
        });

        socket.on('stockError', (error) => {
            console.error('Stock error:', error);
        });

        // Render all stocks
        function renderStocks() {
            const stocks = Object.values(stocksData);

            if (stocks.length === 0) {
                stocksContainer.innerHTML = `
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>Loading EGX stock data...</p>
                    </div>
                `;
                return;
            }

            stocksContainer.innerHTML = `
                <div class="stocks-grid">
                    ${stocks.map(stock => createStockCard(stock)).join('')}
                </div>
            `;
        }

        // Create stock card HTML
        function createStockCard(stock) {
            const changeClass = stock.change >= 0 ? 'change-positive' : 'change-negative';
            const changeIcon = stock.change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
            const changeSign = stock.change >= 0 ? '+' : '';

            const typeColors = {
                'index': '#3498db',
                'bank': '#e67e22',
                'industrial': '#27ae60',
                'fintech': '#9b59b6',
                'real_estate': '#f39c12',
                'construction': '#34495e',
                'pharma': '#e91e63',
                'financial': '#16a085'
            };

            return `
                <div class="stock-card animate__animated animate__fadeInUp" id="card-${stock.symbol}" style="border-left-color: ${typeColors[stock.type] || '#3498db'}">
                    <div class="stock-header">
                        <div>
                            <div class="stock-name">${stock.name}</div>
                            <div class="stock-symbol">${stock.symbol}</div>
                        </div>
                        <div class="stock-type" style="background: ${typeColors[stock.type] || '#95a5a6'}">${stock.type.replace('_', ' ').toUpperCase()}</div>
                    </div>

                    <div class="stock-price">${stock.price ? stock.price.toFixed(2) : '--'} EGP</div>

                    <div class="stock-change ${changeClass}">
                        <i class="fas ${changeIcon}"></i>
                        <span>${changeSign}${stock.change ? stock.change.toFixed(2) : '--'} EGP</span>
                        <span>(${changeSign}${stock.changePercent ? stock.changePercent.toFixed(2) : '--'}%)</span>
                    </div>

                    <div class="stock-details">
                        <div class="detail-item">
                            <span>Open:</span>
                            <span>${stock.open ? stock.open.toFixed(2) : '--'} EGP</span>
                        </div>
                        <div class="detail-item">
                            <span>High:</span>
                            <span>${stock.high ? stock.high.toFixed(2) : '--'} EGP</span>
                        </div>
                        <div class="detail-item">
                            <span>Low:</span>
                            <span>${stock.low ? stock.low.toFixed(2) : '--'} EGP</span>
                        </div>
                        <div class="detail-item">
                            <span>Volume:</span>
                            <span>${stock.volume ? stock.volume.toLocaleString() : '--'}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        // Update individual stock card
        function updateStockCard(stock) {
            const card = document.getElementById(`card-${stock.symbol}`);
            if (card) {
                // Add flash animation
                card.classList.add('animate__flash');
                setTimeout(() => {
                    card.classList.remove('animate__flash');
                }, 1000);

                // Update the card content
                const newCard = createStockCard(stock);
                card.outerHTML = newCard;
            } else {
                // If card doesn't exist, re-render all stocks
                renderStocks();
            }
        }

        // Update market overview
        function updateOverview() {
            const stocks = Object.values(stocksData);

            connectedCount.textContent = stocks.length;

            const gainers = stocks.filter(s => s.change > 0).length;
            const losers = stocks.filter(s => s.change < 0).length;
            const volume = stocks.reduce((sum, s) => sum + (s.volume || 0), 0);

            gainersCount.textContent = gainers;
            losersCount.textContent = losers;
            totalVolume.textContent = volume.toLocaleString();
        }

        // Format numbers
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        // Initialize the app
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🇪🇬 EGX Stock Monitor initialized');
        });
    </script>
</body>
</html>
