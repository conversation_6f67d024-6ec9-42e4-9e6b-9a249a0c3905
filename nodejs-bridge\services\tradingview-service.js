const logger = require('../utils/logger');

class TradingViewService {
    constructor() {
        this.client = null;
        this.charts = new Map();
        this.cache = new Map();
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
    }

    async initialize() {
        try {
            logger.info('🔌 Initializing TradingView connection...');

            // For now, use mock data to test the integration
            // TODO: Implement real TradingView connection later
            this.isConnected = true;
            this.reconnectAttempts = 0;
            logger.info('✅ TradingView connection established (Mock Mode)');

        } catch (error) {
            logger.error('❌ Failed to initialize TradingView:', error);
            throw error;
        }
    }

    async handleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            logger.error('Max reconnection attempts reached');
            return;
        }

        this.reconnectAttempts++;
        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
        
        logger.info(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
        
        setTimeout(() => {
            this.initialize().catch(err => {
                logger.error('Reconnection failed:', err);
            });
        }, delay);
    }

    async getOHLCV(symbol, exchange = 'EGX', interval = '1h', limit = 150) {
        try {
            const cacheKey = `${exchange}:${symbol}:${interval}:${limit}`;
            const cached = this.cache.get(cacheKey);
            
            if (cached && Date.now() - cached.timestamp < 5000) {
                return cached.data;
            }

            logger.info(`📊 Fetching OHLCV data for ${exchange}:${symbol}`);

            const chart = new this.client.Session.Chart();
            chart.setMarket(`${exchange}:${symbol}`, {
                timeframe: this.convertInterval(interval),
            });

            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Request timeout'));
                }, 10000);

                chart.onUpdate(() => {
                    clearTimeout(timeout);
                    
                    const periods = chart.periods.slice(-limit);
                    const ohlcv = periods.map(period => ({
                        timestamp: period.time * 1000,
                        open: period.open,
                        high: period.high,
                        low: period.low,
                        close: period.close,
                        volume: period.volume || 0
                    }));

                    // Cache the result
                    this.cache.set(cacheKey, {
                        data: ohlcv,
                        timestamp: Date.now()
                    });

                    resolve(ohlcv);
                });

                chart.onError((error) => {
                    clearTimeout(timeout);
                    reject(error);
                });
            });

        } catch (error) {
            logger.error(`Error fetching OHLCV for ${symbol}:`, error);
            throw error;
        }
    }

    async getCurrentPrice(symbol, exchange = 'EGX') {
        try {
            const cacheKey = `price:${exchange}:${symbol}`;
            const cached = this.cache.get(cacheKey);
            
            if (cached && Date.now() - cached.timestamp < 1000) {
                return cached.data;
            }

            logger.info(`💰 Fetching current price for ${exchange}:${symbol}`);

            const chart = new this.client.Session.Chart();
            chart.setMarket(`${exchange}:${symbol}`, {
                timeframe: '1',
            });

            return new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Request timeout'));
                }, 5000);

                chart.onUpdate(() => {
                    clearTimeout(timeout);
                    
                    const lastPeriod = chart.periods[chart.periods.length - 1];
                    if (lastPeriod) {
                        const priceData = {
                            symbol: `${exchange}:${symbol}`,
                            price: lastPeriod.close,
                            open: lastPeriod.open,
                            high: lastPeriod.high,
                            low: lastPeriod.low,
                            volume: lastPeriod.volume || 0,
                            timestamp: lastPeriod.time * 1000,
                            change: lastPeriod.close - lastPeriod.open,
                            changePercent: ((lastPeriod.close - lastPeriod.open) / lastPeriod.open) * 100
                        };

                        // Cache the result
                        this.cache.set(cacheKey, {
                            data: priceData,
                            timestamp: Date.now()
                        });

                        resolve(priceData);
                    } else {
                        reject(new Error('No price data available'));
                    }
                });

                chart.onError((error) => {
                    clearTimeout(timeout);
                    reject(error);
                });
            });

        } catch (error) {
            logger.error(`Error fetching price for ${symbol}:`, error);
            throw error;
        }
    }

    async searchSymbols(query, exchange = 'EGX') {
        try {
            logger.info(`🔍 Searching symbols for: ${query}`);

            // For now, return common EGX symbols as the search API might need adjustment
            const commonEGXSymbols = [
                { symbol: 'CIB', description: 'Commercial International Bank', exchange: 'EGX', type: 'stock' },
                { symbol: 'ETEL', description: 'Egyptian Company for Mobile Services', exchange: 'EGX', type: 'stock' },
                { symbol: 'HELI', description: 'Heliopolis Housing', exchange: 'EGX', type: 'stock' },
                { symbol: 'EGAS', description: 'Egyptian Natural Gas Company', exchange: 'EGX', type: 'stock' },
                { symbol: 'SWDY', description: 'El Sewedy Electric Company', exchange: 'EGX', type: 'stock' }
            ];

            const filtered = commonEGXSymbols.filter(s =>
                s.symbol.toLowerCase().includes(query.toLowerCase()) ||
                s.description.toLowerCase().includes(query.toLowerCase())
            );

            return filtered.map(result => ({
                symbol: result.symbol,
                description: result.description,
                exchange: result.exchange,
                type: result.type,
                fullName: `${result.exchange}:${result.symbol}`
            }));

        } catch (error) {
            logger.error(`Error searching symbols for ${query}:`, error);
            throw error;
        }
    }

    convertInterval(interval) {
        const intervalMap = {
            '1m': '1',
            '3m': '3',
            '5m': '5',
            '15m': '15',
            '30m': '30',
            '1h': '60',
            '2h': '120',
            '4h': '240',
            '1d': 'D',
            '1w': 'W',
            '1M': 'M'
        };
        
        return intervalMap[interval] || '60';
    }

    async disconnect() {
        try {
            if (this.client) {
                await this.client.end();
                this.isConnected = false;
                logger.info('TradingView connection closed');
            }
        } catch (error) {
            logger.error('Error disconnecting from TradingView:', error);
        }
    }

    getConnectionStatus() {
        return {
            connected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            cacheSize: this.cache.size
        };
    }
}

module.exports = TradingViewService;
