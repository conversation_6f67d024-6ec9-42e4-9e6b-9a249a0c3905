const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api/v1';

async function testAPI() {
    console.log('🧪 Testing TradingView EGX Bridge API...\n');

    try {
        // Test 1: Health check
        console.log('1️⃣ Testing health check...');
        const health = await axios.get('http://localhost:3001/health');
        console.log('✅ Health check:', health.data.status);

        // Test 2: Get EGX symbols
        console.log('\n2️⃣ Testing EGX symbols...');
        const symbols = await axios.get(`${BASE_URL}/symbols/egx`);
        console.log('✅ EGX symbols count:', symbols.data.data.length);
        console.log('📊 Sample symbols:', symbols.data.data.slice(0, 3).map(s => s.symbol));

        // Test 3: Search symbols
        console.log('\n3️⃣ Testing symbol search...');
        const search = await axios.get(`${BASE_URL}/symbols/search?q=CIB`);
        console.log('✅ Search results:', search.data.data.length);

        // Test 4: Get current price
        console.log('\n4️⃣ Testing current price...');
        const price = await axios.get(`${BASE_URL}/price/CIB?exchange=EGX`);
        console.log('✅ CIB price:', price.data.data.price);

        // Test 5: Get OHLCV data
        console.log('\n5️⃣ Testing OHLCV data...');
        const ohlcv = await axios.get(`${BASE_URL}/ohlcv/CIB?exchange=EGX&interval=1h&limit=10`);
        console.log('✅ OHLCV data points:', ohlcv.data.data.length);
        console.log('📈 Latest close:', ohlcv.data.data[ohlcv.data.data.length - 1]?.close);

        // Test 6: Batch prices
        console.log('\n6️⃣ Testing batch prices...');
        const batch = await axios.post(`${BASE_URL}/batch/prices`, {
            symbols: ['CIB', 'ETEL', 'HELI'],
            exchange: 'EGX'
        });
        console.log('✅ Batch results:', batch.data.meta);

        // Test 7: Service status
        console.log('\n7️⃣ Testing service status...');
        const status = await axios.get(`${BASE_URL}/status`);
        console.log('✅ Service connected:', status.data.data.connected);

        console.log('\n🎉 All tests passed! API is working correctly.');

    } catch (error) {
        console.error('❌ Test failed:', error.response?.data || error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Make sure the server is running: npm start');
        }
    }
}

// Run tests if this file is executed directly
if (require.main === module) {
    testAPI();
}

module.exports = testAPI;
