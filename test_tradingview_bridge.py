#!/usr/bin/env python3
"""
Test script to debug TradingView bridge data format
"""

import asyncio
import aiohttp
import json
from log_setup import logger

async def test_tradingview_bridge():
    """Test what the TradingView bridge actually returns"""
    
    bridge_url = "http://localhost:3001/api/v1"  # Correct TradingView bridge URL
    
    print("🔍 Testing TradingView Bridge Connection...")
    
    async with aiohttp.ClientSession() as session:
        
        # Test 1: Check bridge status
        try:
            print("\n1️⃣ Testing bridge status...")
            async with session.get(f"{bridge_url}/status") as response:
                if response.status == 200:
                    status_data = await response.json()
                    print(f"✅ Bridge Status: {json.dumps(status_data, indent=2)}")
                else:
                    print(f"❌ Bridge status failed: {response.status}")
                    return
        except Exception as e:
            print(f"❌ Bridge connection failed: {e}")
            return
        
        # Test 2: Test current price (this works)
        try:
            print("\n2️⃣ Testing current price...")
            async with session.get(f"{bridge_url}/price/COMI?exchange=EGX") as response:
                if response.status == 200:
                    price_data = await response.json()
                    print(f"✅ Price Data: {json.dumps(price_data, indent=2)}")
                else:
                    print(f"❌ Price request failed: {response.status}")
                    text = await response.text()
                    print(f"Response: {text}")
        except Exception as e:
            print(f"❌ Price request error: {e}")
        
        # Test 3: Test OHLCV data (this fails)
        try:
            print("\n3️⃣ Testing OHLCV data...")
            params = {
                "exchange": "EGX",
                "interval": "1h",
                "limit": 5  # Just get 5 candles for testing
            }
            
            async with session.get(f"{bridge_url}/ohlcv/COMI", params=params) as response:
                print(f"📊 OHLCV Response Status: {response.status}")
                
                if response.status == 200:
                    ohlcv_data = await response.json()
                    print(f"✅ OHLCV Data Type: {type(ohlcv_data)}")
                    print(f"✅ OHLCV Data Length: {len(ohlcv_data) if isinstance(ohlcv_data, (list, dict)) else 'N/A'}")
                    print(f"✅ OHLCV Raw Data: {json.dumps(ohlcv_data, indent=2)}")

                    # Test the data processing logic
                    if "data" in ohlcv_data and isinstance(ohlcv_data["data"], list):
                        print(f"\n🔧 Testing data processing...")
                        raw_data = ohlcv_data["data"]

                        processed_data = []
                        for candle in raw_data:
                            print(f"🔍 Processing candle: {candle}")

                            if "high" in candle and "low" in candle:
                                print("✅ Standard format (has high/low)")
                                processed_data.append({
                                    "timestamp": candle["timestamp"],
                                    "open": float(candle["open"]),
                                    "high": float(candle["high"]),
                                    "low": float(candle["low"]),
                                    "close": float(candle["close"]),
                                    "volume": float(candle.get("volume", 0))
                                })
                            elif "open" in candle and "close" in candle:
                                print("⚠️ Missing high/low - estimating from open/close")
                                open_price = float(candle["open"])
                                close_price = float(candle["close"])
                                high_price = max(open_price, close_price)
                                low_price = min(open_price, close_price)

                                processed_data.append({
                                    "timestamp": candle["timestamp"],
                                    "open": open_price,
                                    "high": high_price,
                                    "low": low_price,
                                    "close": close_price,
                                    "volume": float(candle.get("volume", 0))
                                })
                            else:
                                print("❌ Unknown format")

                        print(f"\n✅ Processed {len(processed_data)} candles:")
                        for i, candle in enumerate(processed_data[:2]):
                            print(f"  Candle {i+1}: {candle}")

                        if len(processed_data) > 2:
                            print(f"  ... and {len(processed_data)-2} more")
                    
                    if isinstance(ohlcv_data, list) and len(ohlcv_data) > 0:
                        first_candle = ohlcv_data[0]
                        print(f"🔍 First Candle Type: {type(first_candle)}")
                        print(f"🔍 First Candle: {first_candle}")
                        
                        if isinstance(first_candle, dict):
                            print(f"🔍 Available Keys: {list(first_candle.keys())}")
                        elif isinstance(first_candle, list):
                            print(f"🔍 Array Length: {len(first_candle)}")
                            print(f"🔍 Array Values: {first_candle}")
                        
                else:
                    text = await response.text()
                    print(f"❌ OHLCV request failed: {response.status}")
                    print(f"Response: {text}")
                    
        except Exception as e:
            print(f"❌ OHLCV request error: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 4: Try different symbols
        print("\n4️⃣ Testing different symbols...")
        for symbol in ["ETEL", "HELI"]:
            try:
                params = {
                    "exchange": "EGX",
                    "interval": "1h",
                    "limit": 1
                }
                
                async with session.get(f"{bridge_url}/ohlcv/{symbol}", params=params) as response:
                    print(f"📊 {symbol} OHLCV Status: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ {symbol} Data: {data}")
                    else:
                        text = await response.text()
                        print(f"❌ {symbol} Failed: {text}")
                        
            except Exception as e:
                print(f"❌ {symbol} Error: {e}")

async def test_alternative_endpoints():
    """Test if there are alternative endpoints"""
    
    bridge_url = "http://localhost:3001/api/v1"
    
    print("\n🔍 Testing Alternative Endpoints...")
    
    async with aiohttp.ClientSession() as session:
        
        # Test different endpoint variations
        endpoints_to_test = [
            "/candles/COMI",
            "/klines/COMI", 
            "/bars/COMI",
            "/history/COMI",
            "/chart/COMI"
        ]
        
        for endpoint in endpoints_to_test:
            try:
                params = {
                    "exchange": "EGX",
                    "interval": "1h",
                    "limit": 1
                }
                
                async with session.get(f"{bridge_url}{endpoint}", params=params) as response:
                    print(f"📊 {endpoint}: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ {endpoint} Data: {data}")
                        
            except Exception as e:
                print(f"❌ {endpoint} Error: {e}")

if __name__ == "__main__":
    print("🚀 TradingView Bridge Debug Test")
    print("=" * 50)
    
    asyncio.run(test_tradingview_bridge())
    asyncio.run(test_alternative_endpoints())
    
    print("\n" + "=" * 50)
    print("✅ Test completed!")
