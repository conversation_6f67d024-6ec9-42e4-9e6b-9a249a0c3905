import time
import os
import asyncio
from typing import List, Dict, Any

from telegram import Update
from telegram.ext import ContextTypes

from api.bybit_async import fetch_balance as get_balance, get_open_positions
from core.state import strategy_stop_event
from config.language import STATUS, INFO, ERRORS, HEARTBEAT
from telegram_bot.monitor_strategies_status import restart_strategies
from utils.equity_plot import (
    plot_equity_curve,
    plot_drawdown_curve,
    get_equity_balance
)

BOT_START_TIME = time.time()

# ▶️ Strategy Management
async def cmd_start_strategies(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if not strategy_stop_event.is_set():
        if update.message:
            await update.message.reply_text(STATUS["strategies_running"])
        return
    strategy_stop_event.clear()
    if update.message:
        await update.message.reply_text(STATUS["strategies_started"])

async def cmd_stop_strategies(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if strategy_stop_event.is_set():
        if update.message:
            await update.message.reply_text(STATUS["already_stopped"])
        return
    strategy_stop_event.set()
    if update.message:
        await update.message.reply_text(STATUS["strategies_stopped"])

async def cmd_restart_bot(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    try:
        if update.message:
            await update.message.reply_text(STATUS["restarting_strategies"])
        strategy_stop_event.set()
        await asyncio.sleep(3)
        await restart_strategies()
    except Exception as e:
        if update.message:
            await update.message.reply_text(STATUS["restart_error"].format(e))

async def cmd_status(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    status = STATUS["active_hourly"] if not strategy_stop_event.is_set() else STATUS["not_started"]
    if update.message:
        await update.message.reply_text(STATUS["strategy_status"].format(status))

# 💰 Balance and Positions
async def cmd_balance(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    balance = await get_balance()
    usdt, free, used = balance if balance else (0.0, 0.0, 0.0)
    if update.message:
        await update.message.reply_text(
            f"💰 Balance:\n• Total: {usdt:.2f} USDT\n• Free: {free:.2f} USDT\n• Used: {used:.2f} USDT"
        )

async def cmd_positions(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    positions = await get_open_positions() or []
    if update.message:
        if not positions:
            await update.message.reply_text("📉 No open positions.")
            return
        lines = [
            f"• {p['symbol']}: {p['side']} {p['size']} @ {p['entry']} | PnL: {p.get('pnl', 0.0):.2f}"
            for p in positions
        ]
        await update.message.reply_text("📉 Open Positions:\n" + "\n".join(lines))

async def cmd_pnl(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    positions = await get_open_positions() or []
    if update.message:
        if not positions:
            await update.message.reply_text("📉 No open positions.")
            return
        total_pnl = sum(p.get("pnl", 0.0) for p in positions)
        lines = [f"• {p['symbol']}: {p.get('pnl', 0.0):+.2f}" for p in positions]
        emoji = "🟢" if total_pnl >= 0 else "🔴"
        text = "💹 Current PnL:\n" + "\n".join(lines) + f"\n🔽 Total PnL: {total_pnl:+.2f} USDT {emoji}"
        await update.message.reply_text(text)

# 📈 Equity and Drawdown
async def cmd_equity(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    path = plot_equity_curve()
    if update.message:
        if path:
            with open(path, "rb") as img:
                await update.message.reply_photo(photo=img, caption="📈 Equity Curve")
        else:
            await update.message.reply_text("❌ Failed to build equity curve.")

async def cmd_drawdown_plot(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    path = plot_drawdown_curve()
    if update.message:
        if path:
            with open(path, "rb") as img:
                await update.message.reply_photo(photo=img, caption="📉 Drawdown Curve")
        else:
            await update.message.reply_text("❌ Failed to build drawdown chart.")

async def cmd_equity_balance(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    equity = get_equity_balance()
    if update.message:
        await update.message.reply_text(f"💼 Current equity: <b>{equity:.2f} USDT</b>", parse_mode="HTML")

# 🛡 Heartbeat
async def cmd_heartbeat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    try:
        balance = await get_balance()
        usdt, _, _ = balance if balance else (0.0, 0.0, 0.0)
        positions = await get_open_positions() or []
        total_pnl = sum(p.get("pnl", 0.0) for p in positions)

        uptime_seconds = int(time.time() - BOT_START_TIME)
        uptime_minutes = uptime_seconds // 60
        uptime_hours = uptime_minutes // 60
        uptime_formatted = f"{uptime_hours}h {uptime_minutes % 60}m"
        strategy_status = "✅ Active" if not strategy_stop_event.is_set() else "⛔ Stopped"

        message = (
            HEARTBEAT["report_title"] +
            HEARTBEAT["balance"].format(usdt) +
            HEARTBEAT["open_positions"].format(len(positions)) +
            HEARTBEAT["total_pnl"].format(total_pnl) +
            HEARTBEAT["strategies"].format(strategy_status) +
            HEARTBEAT["uptime"].format(uptime_formatted) +
            HEARTBEAT["working_normally"]
        )

        if update.message:
            await update.message.reply_text(message, parse_mode="HTML")

    except Exception as e:
        if update.message:
            await update.message.reply_text(ERRORS["heartbeat_error"].format(e))

# 🧩 Flag Management
async def cmd_pause_trading(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    open("pause.flag", "w").close()
    if update.message:
        await update.message.reply_text("⏸ Trading paused (pause.flag active).")

async def cmd_resume_trading(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if os.path.exists("pause.flag"):
        os.remove("pause.flag")
    if update.message:
        await update.message.reply_text("▶️ Trading resumed (pause.flag removed).")

async def cmd_halt(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    open("halt.flag", "w").close()
    if update.message:
        await update.message.reply_text("🛑 Trading completely stopped (halt.flag active).")

async def cmd_unhalt(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if os.path.exists("halt.flag"):
        os.remove("halt.flag")
    if update.message:
        await update.message.reply_text(STATUS["halt_removed"])

# ℹ️ Information Commands
async def cmd_strategies(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if update.message:
        await update.message.reply_text(INFO["active_strategies"])

async def cmd_scanning(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if update.message:
        await update.message.reply_text(INFO["analyzing_pairs"])

async def cmd_reentry(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if update.message:
        await update.message.reply_text(INFO["reentry_activated"])

async def cmd_risk(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if update.message:
        await update.message.reply_text(INFO["risk_info"])

async def cmd_version(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if update.message:
        await update.message.reply_text(INFO["bot_version"])
        await update.message.reply_text(INFO["strategy_version"])