{"name": "ufo", "version": "1.5.4", "description": "URL utils for humans", "repository": "unjs/ufo", "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.mjs"}, "./*": "./*"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "automd && unbuild", "automd": "automd", "dev": "vitest", "lint": "eslint . && prettier -c src test", "lint:fix": "eslint --fix . && prettier -w src test", "prepack": "pnpm build", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && vitest run --typecheck"}, "devDependencies": {"@types/node": "^20.14.10", "@vitest/coverage-v8": "^2.0.3", "automd": "^0.3.8", "changelogen": "^0.5.5", "eslint": "^9.7.0", "eslint-config-unjs": "^0.3.2", "jiti": "^1.21.6", "prettier": "^3.3.3", "typescript": "^5.5.3", "unbuild": "^2.0.0", "untyped": "^1.4.2", "vitest": "^2.0.3"}, "packageManager": "pnpm@9.5.0"}