import{n as g,k as d,j as l,ch as p,ci as f}from"./index.BqDl3eRM.js";const I=d("50%{color:rgba(0,0,0,0);}"),$=g("span",{target:"eva3uk20"})(({includeDot:n,shouldBlink:s,theme:t})=>({...n?{"&::before":{opacity:1,content:'"•"',animation:"none",color:t.colors.gray,margin:`0 ${t.spacing.twoXS}`}}:{},...s?{color:t.colors.red,animationName:`${I}`,animationDuration:"0.5s",animationIterationCount:5}:{}})),b=({dirty:n,value:s,inForm:t,maxLength:a,className:m,type:i="single",allowEnterToSubmit:u=!0})=>{const o=[],r=(e,c=!1)=>{o.push(l($,{includeDot:o.length>0,shouldBlink:c,children:e},o.length))};if(u){const e=t?"submit form":"apply";if(i==="multiline"){const c=f()?"⌘":"Ctrl";r(`Press ${c}+Enter to ${e}`)}else i==="single"&&r(`Press Enter to ${e}`)}return a&&(i!=="chat"||n)&&r(`${s.length}/${a}`,n&&s.length>=a),l(p,{"data-testid":"InputInstructions",className:m,children:o})};export{b as I};
