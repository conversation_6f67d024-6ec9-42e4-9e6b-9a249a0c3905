"""
EGX-adapted SMC Strategy - Modified for Egyptian stock market
Replaces crypto-specific logic with stock market logic
"""

import json
import time
import pandas as pd
import os

from api.egx_async import get_ohlcv, get_current_price
from indicators.market_structure import detect_market_structure
from indicators.order_blocks import detect_order_blocks, get_active_order_blocks, analyze_order_block_confluence
from indicators.fvg import detect_fvg, get_active_fvgs, analyze_fvg_confluence
from indicators.liquidity_zones import detect_liquidity_zones, get_active_liquidity_zones, analyze_liquidity_confluence
from indicators.displacement import detect_displacement, get_recent_displacements, analyze_displacement_confluence
from indicators.eqh_eql import find_equal_highs_lows
from log_setup import logger
from monitor_liquidations import monitoring_only_mode
from utils.direction import determine_direction
from utils.dynamic_sl_tp import get_dynamic_sl_tp_advanced
from utils.risk_limits import risk_limits_exceeded
from utils.volume import check_volume_spike
from utils.log_signal import log_signal
from typing import Literal, Optional, Dict

try:
    with open("config/best_weights.json") as f:
        CONFIDENCE_WEIGHTS = json.load(f)
except FileNotFoundError:
    from utils.confidence_weights import CONFIDENCE_WEIGHTS

# EGX-specific configuration
ENTRY_CACHE: dict[str, dict] = {}
ENTRY_TIMEOUT = 60 * 60  # 1 hour for stocks (longer than crypto)
DEBUG_LOG_PATH = "logs/egx_signal_debug.log"

# EGX market hours and constraints
EGX_MARKET_OPEN_HOUR = 10
EGX_MARKET_CLOSE_HOUR = 14

def log_debug_signal(message: str):
    """Log debug messages for EGX strategy"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    with open(DEBUG_LOG_PATH, "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] EGX: {message}\n")

def update_entry_cache(symbol: str, price: float, direction: str) -> None:
    """Update entry cache for symbol"""
    ENTRY_CACHE[symbol] = {
        "price": price,
        "ts": time.time(),
        "direction": direction,
    }

def allow_reentry(symbol: str, current_price: float, direction: str) -> bool:
    """Check if re-entry is allowed for symbol"""
    cached = ENTRY_CACHE.get(symbol)
    if not cached:
        return True
    
    # Check timeout
    if time.time() - cached.get("ts", 0) > ENTRY_TIMEOUT:
        return True
    
    # Allow if direction changed
    if cached.get("direction") != direction:
        return True
    
    # Allow if price moved significantly (2% for stocks vs 1% for crypto)
    last_price = cached.get("price", 0)
    if abs(current_price - last_price) / max(last_price, 1) >= 0.02:
        return True
    
    return False

def normalize_confidence(raw_confidence: float) -> float:
    """Normalize confidence score"""
    max_weight_sum = sum(CONFIDENCE_WEIGHTS.values()) or 100
    return min(raw_confidence / max_weight_sum, 1.0)

def is_market_hours() -> bool:
    """Check if EGX market is open (simplified check)"""
    current_hour = time.localtime().tm_hour
    return EGX_MARKET_OPEN_HOUR <= current_hour <= EGX_MARKET_CLOSE_HOUR

async def run_smc_strategy_egx(
    symbol: str, 
    capital: float = 100000, 
    stop_event=None, 
    default_risk_pct: float = 0.02  # 2% risk for stocks
) -> Optional[Dict]:
    """
    Run SMC strategy adapted for EGX stocks
    
    Args:
        symbol: EGX stock symbol
        capital: Available capital in EGP
        stop_event: Stop event for graceful shutdown
        default_risk_pct: Risk percentage (2% for stocks vs 1% for crypto)
    
    Returns:
        Signal dictionary or None
    """
    logger.info(f"🇪🇬 Starting EGX SMC strategy for {symbol}")
    
    try:
        # Check halt/pause flags
        if os.path.exists("halt.flag"):
            log_debug_signal(f"{symbol}: strategy halted - halt.flag detected")
            return None

        if os.path.exists("pause.flag"):
            log_debug_signal(f"{symbol}: strategy paused - pause.flag active")
            return None

        # Check market hours
        if not is_market_hours():
            log_debug_signal(f"{symbol}: market closed - outside trading hours")
            return None

        # Check monitoring mode and risk limits
        if monitoring_only_mode or (stop_event and stop_event.is_set()) or await risk_limits_exceeded():
            log_debug_signal(f"{symbol}: strategy skipped - monitoring mode or risk limits")
            return None

        # Get OHLCV data (use 1h interval for stocks)
        df_raw = await get_ohlcv(symbol=symbol, interval="1h", limit=150)
        if not df_raw:
            log_debug_signal(f"{symbol}: no OHLCV data available")
            return None

        df = pd.DataFrame(df_raw)
        logger.info(f"✅ Got {len(df)} candles for {symbol}")

        if df.empty or len(df) < 50:  # Less data needed for stocks
            log_debug_signal(f"{symbol}: insufficient data - {len(df)} candles")
            return None

        # Detect market structure
        ms = detect_market_structure(df)
        logger.info(f"✅ Market structure: {ms}")

        if not ms or ms.get("event") not in ["BOS", "CHOCH"]:
            log_debug_signal(f"{symbol}: no valid market structure event")
            return None

        # Get current price
        current_price = df["close"].iloc[-1]
        direction = determine_direction(ms["trend"])
        logger.info(f"✅ Trend direction: {direction}")

        if direction == "neutral":
            log_debug_signal(f"{symbol}: neutral trend - entry skipped")
            return None

        # For stocks, we primarily focus on long positions
        if direction == "short":
            log_debug_signal(f"{symbol}: short signal ignored - stocks typically long only")
            return None

        # Check re-entry conditions
        if not allow_reentry(symbol, current_price, direction):
            log_debug_signal(f"{symbol}: re-entry blocked")
            return None

        # === COMPREHENSIVE SMC ANALYSIS ===
        logger.info(f"🔍 Running comprehensive SMC analysis for {symbol}")

        # 1. Order Blocks Analysis
        order_blocks = get_active_order_blocks(df, current_price, max_distance_pct=5.0)
        ob_confluence = analyze_order_block_confluence(order_blocks, current_price)

        # 2. Fair Value Gaps Analysis
        fvgs = get_active_fvgs(df, current_price, max_distance_pct=3.0)
        fvg_confluence = analyze_fvg_confluence(fvgs, current_price)

        # 3. Liquidity Zones Analysis
        liquidity_zones = get_active_liquidity_zones(df, current_price, max_distance_pct=8.0)
        liq_confluence = analyze_liquidity_confluence(liquidity_zones, current_price)

        # 4. Displacement Analysis
        displacements = get_recent_displacements(df, lookback_periods=30)
        disp_confluence = analyze_displacement_confluence(displacements, current_price)

        # 5. Equal Highs/Lows Analysis
        eqh_eql = find_equal_highs_lows(df)

        logger.info(f"📊 SMC Analysis Results:")
        logger.info(f"   Order Blocks: {len(order_blocks)} active, confluence: {ob_confluence['confluence']}")
        logger.info(f"   FVGs: {len(fvgs)} active, confluence: {fvg_confluence['confluence']}")
        logger.info(f"   Liquidity Zones: {len(liquidity_zones)} active, confluence: {liq_confluence['confluence']}")
        logger.info(f"   Displacements: {len(displacements)} recent, confluence: {disp_confluence['confluence']}")

        # === CONFIDENCE CALCULATION WITH ADVANCED SMC ===
        confidence = 0
        reasons = []

        # Market structure events (base score)
        if ms.get("event") == "BOS":
            confidence += CONFIDENCE_WEIGHTS.get("bos", 30)
            reasons.append("✅ BOS (Break of Structure)")

        if ms.get("event") == "CHOCH":
            confidence += CONFIDENCE_WEIGHTS.get("choch", 25)
            reasons.append("✅ CHOCH (Change of Character)")

        # Order Blocks Confluence
        if ob_confluence["confluence"]:
            ob_score = ob_confluence["strength"] * 25
            confidence += ob_score
            reasons.append(f"✅ Order Block Confluence ({ob_confluence['direction']}, {len(order_blocks)} blocks)")

            # Extra points for alignment with trend
            if (direction == "long" and ob_confluence["direction"] == "bullish") or \
               (direction == "short" and ob_confluence["direction"] == "bearish"):
                confidence += 10
                reasons.append("✅ Order Blocks align with trend")

        # FVG Confluence
        if fvg_confluence["confluence"]:
            fvg_score = fvg_confluence["strength"] * 20
            confidence += fvg_score
            reasons.append(f"✅ FVG Confluence ({fvg_confluence['direction']}, {len(fvgs)} gaps)")

        # Liquidity Zones Confluence
        if liq_confluence["confluence"]:
            liq_score = liq_confluence["strength"] * 30  # High weight for liquidity
            confidence += liq_score
            reasons.append(f"✅ Liquidity Zone Confluence ({liq_confluence['direction']}, {len(liquidity_zones)} zones)")

        # Displacement Confluence
        if disp_confluence["confluence"]:
            disp_score = disp_confluence["strength"] * 20
            confidence += disp_score
            reasons.append(f"✅ Displacement Confluence ({disp_confluence['direction']}, {len(displacements)} moves)")

            # Extra points for recent displacement with follow-through
            if disp_confluence.get("avg_follow_through", 0) > 0.7:
                confidence += 15
                reasons.append("✅ Strong displacement follow-through")

        # Equal Highs/Lows
        if eqh_eql.get("eqh") and direction == "long":
            confidence += CONFIDENCE_WEIGHTS.get("eqh", 15)
            reasons.append("✅ Equal Highs detected (bullish)")
        elif eqh_eql.get("eql") and direction == "short":
            confidence += CONFIDENCE_WEIGHTS.get("eql", 15)
            reasons.append("✅ Equal Lows detected (bearish)")

        # Volume analysis (important for stocks)
        if check_volume_spike(df):
            confidence += CONFIDENCE_WEIGHTS.get("volume_spike", 15)
            reasons.append("✅ Volume spike detected")

        # Multi-timeframe confluence bonus
        confluence_count = sum([
            ob_confluence["confluence"],
            fvg_confluence["confluence"],
            liq_confluence["confluence"],
            disp_confluence["confluence"]
        ])

        if confluence_count >= 3:
            confidence += 20
            reasons.append(f"✅ Multi-factor SMC confluence ({confluence_count}/4)")
        elif confluence_count >= 2:
            confidence += 10
            reasons.append(f"✅ SMC confluence ({confluence_count}/4)")

        # Normalize confidence
        normalized_conf = normalize_confidence(confidence)
        logger.info(f"✅ Confidence: {confidence}, normalized: {normalized_conf:.2f}")
        logger.info(f"✅ Entry reasons: {reasons}")

        # Minimum confidence threshold for stocks (higher than crypto)
        if normalized_conf < 0.4:  # 40% minimum confidence
            log_debug_signal(f"{symbol}: confidence too low - {normalized_conf:.2f}")
            return None

        # Get dynamic SL/TP (adapted for stocks)
        sl_tp = get_dynamic_sl_tp_advanced(df, current_price, direction, normalized_conf, symbol)
        
        # Adjust SL/TP for stock market (smaller moves, longer timeframes)
        sl_distance = abs(current_price - sl_tp["sl"]) / current_price
        tp_distance = abs(sl_tp["tp2"] - current_price) / current_price
        
        # Ensure reasonable risk/reward for stocks
        if sl_distance > 0.1:  # Max 10% stop loss
            sl_tp["sl"] = current_price * 0.9 if direction == "long" else current_price * 1.1
        
        if tp_distance < sl_distance * 1.5:  # Min 1.5:1 risk/reward
            sl_tp["tp2"] = current_price * (1 + sl_distance * 1.5) if direction == "long" else current_price * (1 - sl_distance * 1.5)

        # Create comprehensive signal with SMC data
        signal = {
            "symbol": symbol,
            "direction": direction,
            "entry_price": current_price,
            "stop_loss": sl_tp["sl"],
            "take_profit": sl_tp["tp2"],
            "confidence": confidence,
            "normalized_confidence": normalized_conf,
            "reasons": reasons,
            "timestamp": time.time(),
            "market_structure": ms,
            "risk_percent": default_risk_pct,

            # Advanced SMC Data
            "smc_analysis": {
                "order_blocks": {
                    "count": len(order_blocks),
                    "confluence": ob_confluence,
                    "active_blocks": [
                        {
                            "type": ob.block_type,
                            "high": ob.high,
                            "low": ob.low,
                            "strength": ob.strength,
                            "tested": ob.tested
                        } for ob in order_blocks[:3]  # Top 3 blocks
                    ]
                },
                "fair_value_gaps": {
                    "count": len(fvgs),
                    "confluence": fvg_confluence,
                    "active_fvgs": [
                        {
                            "type": fvg.gap_type,
                            "high": fvg.high,
                            "low": fvg.low,
                            "strength": fvg.strength,
                            "filled": fvg.filled
                        } for fvg in fvgs[:3]  # Top 3 FVGs
                    ]
                },
                "liquidity_zones": {
                    "count": len(liquidity_zones),
                    "confluence": liq_confluence,
                    "active_zones": [
                        {
                            "type": zone.zone_type.value,
                            "high": zone.high,
                            "low": zone.low,
                            "center": zone.center,
                            "strength": zone.strength,
                            "touches": zone.touches
                        } for zone in liquidity_zones[:3]  # Top 3 zones
                    ]
                },
                "displacements": {
                    "count": len(displacements),
                    "confluence": disp_confluence,
                    "recent_moves": [
                        {
                            "type": disp.displacement_type.value,
                            "size_pct": disp.displacement_pct,
                            "strength": disp.strength,
                            "follow_through": disp.follow_through,
                            "candles": disp.candles_count
                        } for disp in displacements[:3]  # Top 3 displacements
                    ]
                },
                "equal_highs_lows": eqh_eql,
                "confluence_summary": {
                    "total_factors": confluence_count,
                    "confluence_strength": confluence_count / 4,
                    "aligned_factors": [
                        factor for factor, active in [
                            ("order_blocks", ob_confluence["confluence"]),
                            ("fvgs", fvg_confluence["confluence"]),
                            ("liquidity_zones", liq_confluence["confluence"]),
                            ("displacements", disp_confluence["confluence"])
                        ] if active
                    ]
                }
            }
        }

        # Update entry cache
        update_entry_cache(symbol, current_price, direction)

        # Log signal
        log_signal(
            symbol=symbol,
            direction=direction,
            price=current_price,
            sl=sl_tp["sl"],
            tp=sl_tp["tp2"],
            size=0,  # Size will be calculated by executor
            confidence=confidence,
            reasons=reasons,
            status="egx_signal",
            result="pending",
            pnl=0.0
        )

        logger.info(f"🎯 EGX Signal generated for {symbol}: {direction} at {current_price:.2f} EGP")
        return signal

    except Exception as e:
        log_debug_signal(f"❌ Error in EGX SMC strategy for {symbol}: {str(e)}")
        logger.error(f"❌ EGX strategy error for {symbol}: {e}")
        return None

# Alias for compatibility with existing code
async def run_smc_strategy(symbol: str, capital: float = 100000, **kwargs):
    """Compatibility wrapper for EGX strategy"""
    return await run_smc_strategy_egx(symbol, capital, **kwargs)
