"""
EGX-adapted SMC Strategy - Modified for Egyptian stock market
Replaces crypto-specific logic with stock market logic
"""

import json
import time
import pandas as pd
import os

from api.egx_async import get_ohlcv, get_current_price
from indicators.market_structure import detect_market_structure
from log_setup import logger
from monitor_liquidations import monitoring_only_mode
from utils.direction import determine_direction
from utils.dynamic_sl_tp import get_dynamic_sl_tp_advanced
from utils.risk_limits import risk_limits_exceeded
from utils.volume import check_volume_spike
from utils.log_signal import log_signal
from typing import Literal, Optional, Dict

try:
    with open("config/best_weights.json") as f:
        CONFIDENCE_WEIGHTS = json.load(f)
except FileNotFoundError:
    from utils.confidence_weights import CONFIDENCE_WEIGHTS

# EGX-specific configuration
ENTRY_CACHE: dict[str, dict] = {}
ENTRY_TIMEOUT = 60 * 60  # 1 hour for stocks (longer than crypto)
DEBUG_LOG_PATH = "logs/egx_signal_debug.log"

# EGX market hours and constraints
EGX_MARKET_OPEN_HOUR = 10
EGX_MARKET_CLOSE_HOUR = 14

def log_debug_signal(message: str):
    """Log debug messages for EGX strategy"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    with open(DEBUG_LOG_PATH, "a", encoding="utf-8") as f:
        f.write(f"[{timestamp}] EGX: {message}\n")

def update_entry_cache(symbol: str, price: float, direction: str) -> None:
    """Update entry cache for symbol"""
    ENTRY_CACHE[symbol] = {
        "price": price,
        "ts": time.time(),
        "direction": direction,
    }

def allow_reentry(symbol: str, current_price: float, direction: str) -> bool:
    """Check if re-entry is allowed for symbol"""
    cached = ENTRY_CACHE.get(symbol)
    if not cached:
        return True
    
    # Check timeout
    if time.time() - cached.get("ts", 0) > ENTRY_TIMEOUT:
        return True
    
    # Allow if direction changed
    if cached.get("direction") != direction:
        return True
    
    # Allow if price moved significantly (2% for stocks vs 1% for crypto)
    last_price = cached.get("price", 0)
    if abs(current_price - last_price) / max(last_price, 1) >= 0.02:
        return True
    
    return False

def normalize_confidence(raw_confidence: float) -> float:
    """Normalize confidence score"""
    max_weight_sum = sum(CONFIDENCE_WEIGHTS.values()) or 100
    return min(raw_confidence / max_weight_sum, 1.0)

def is_market_hours() -> bool:
    """Check if EGX market is open (simplified check)"""
    current_hour = time.localtime().tm_hour
    return EGX_MARKET_OPEN_HOUR <= current_hour <= EGX_MARKET_CLOSE_HOUR

async def run_smc_strategy_egx(
    symbol: str, 
    capital: float = 100000, 
    stop_event=None, 
    default_risk_pct: float = 0.02  # 2% risk for stocks
) -> Optional[Dict]:
    """
    Run SMC strategy adapted for EGX stocks
    
    Args:
        symbol: EGX stock symbol
        capital: Available capital in EGP
        stop_event: Stop event for graceful shutdown
        default_risk_pct: Risk percentage (2% for stocks vs 1% for crypto)
    
    Returns:
        Signal dictionary or None
    """
    logger.info(f"🇪🇬 Starting EGX SMC strategy for {symbol}")
    
    try:
        # Check halt/pause flags
        if os.path.exists("halt.flag"):
            log_debug_signal(f"{symbol}: strategy halted - halt.flag detected")
            return None

        if os.path.exists("pause.flag"):
            log_debug_signal(f"{symbol}: strategy paused - pause.flag active")
            return None

        # Check market hours
        if not is_market_hours():
            log_debug_signal(f"{symbol}: market closed - outside trading hours")
            return None

        # Check monitoring mode and risk limits
        if monitoring_only_mode or (stop_event and stop_event.is_set()) or await risk_limits_exceeded():
            log_debug_signal(f"{symbol}: strategy skipped - monitoring mode or risk limits")
            return None

        # Get OHLCV data (use 1h interval for stocks)
        df_raw = await get_ohlcv(symbol=symbol, interval="1h", limit=150)
        if not df_raw:
            log_debug_signal(f"{symbol}: no OHLCV data available")
            return None

        df = pd.DataFrame(df_raw)
        logger.info(f"✅ Got {len(df)} candles for {symbol}")

        if df.empty or len(df) < 50:  # Less data needed for stocks
            log_debug_signal(f"{symbol}: insufficient data - {len(df)} candles")
            return None

        # Detect market structure
        ms = detect_market_structure(df)
        logger.info(f"✅ Market structure: {ms}")

        if not ms or ms.get("event") not in ["BOS", "CHOCH"]:
            log_debug_signal(f"{symbol}: no valid market structure event")
            return None

        # Get current price
        current_price = df["close"].iloc[-1]
        direction = determine_direction(ms["trend"])
        logger.info(f"✅ Trend direction: {direction}")

        if direction == "neutral":
            log_debug_signal(f"{symbol}: neutral trend - entry skipped")
            return None

        # For stocks, we primarily focus on long positions
        if direction == "short":
            log_debug_signal(f"{symbol}: short signal ignored - stocks typically long only")
            return None

        # Check re-entry conditions
        if not allow_reentry(symbol, current_price, direction):
            log_debug_signal(f"{symbol}: re-entry blocked")
            return None

        # Calculate confidence score (adapted for stocks)
        confidence = 0
        reasons = []

        # Market structure events
        if ms.get("event") == "BOS":
            confidence += CONFIDENCE_WEIGHTS.get("bos", 30)
            reasons.append("✅ BOS (Break of Structure)")

        if ms.get("event") == "CHOCH":
            confidence += CONFIDENCE_WEIGHTS.get("choch", 25)
            reasons.append("✅ CHOCH (Change of Character)")

        # Volume analysis (important for stocks)
        if check_volume_spike(df):
            confidence += CONFIDENCE_WEIGHTS.get("volume_spike", 15)  # Higher weight for stocks
            reasons.append("✅ Volume spike detected")

        # Additional stock-specific factors
        # Check if price is near support/resistance levels
        recent_highs = df["high"].tail(20).max()
        recent_lows = df["low"].tail(20).min()
        price_range = recent_highs - recent_lows
        
        if price_range > 0:
            # If price is near recent lows (potential support)
            if (current_price - recent_lows) / price_range < 0.2:
                confidence += 10
                reasons.append("✅ Near support level")
            
            # If breaking above resistance
            if current_price > recent_highs * 0.98:
                confidence += 15
                reasons.append("✅ Breaking resistance")

        # Normalize confidence
        normalized_conf = normalize_confidence(confidence)
        logger.info(f"✅ Confidence: {confidence}, normalized: {normalized_conf:.2f}")
        logger.info(f"✅ Entry reasons: {reasons}")

        # Minimum confidence threshold for stocks (higher than crypto)
        if normalized_conf < 0.4:  # 40% minimum confidence
            log_debug_signal(f"{symbol}: confidence too low - {normalized_conf:.2f}")
            return None

        # Get dynamic SL/TP (adapted for stocks)
        sl_tp = get_dynamic_sl_tp_advanced(df, current_price, direction, normalized_conf, symbol)
        
        # Adjust SL/TP for stock market (smaller moves, longer timeframes)
        sl_distance = abs(current_price - sl_tp["sl"]) / current_price
        tp_distance = abs(sl_tp["tp2"] - current_price) / current_price
        
        # Ensure reasonable risk/reward for stocks
        if sl_distance > 0.1:  # Max 10% stop loss
            sl_tp["sl"] = current_price * 0.9 if direction == "long" else current_price * 1.1
        
        if tp_distance < sl_distance * 1.5:  # Min 1.5:1 risk/reward
            sl_tp["tp2"] = current_price * (1 + sl_distance * 1.5) if direction == "long" else current_price * (1 - sl_distance * 1.5)

        # Create signal
        signal = {
            "symbol": symbol,
            "direction": direction,
            "entry_price": current_price,
            "stop_loss": sl_tp["sl"],
            "take_profit": sl_tp["tp2"],
            "confidence": confidence,
            "normalized_confidence": normalized_conf,
            "reasons": reasons,
            "timestamp": time.time(),
            "market_structure": ms,
            "risk_percent": default_risk_pct
        }

        # Update entry cache
        update_entry_cache(symbol, current_price, direction)

        # Log signal
        log_signal(
            symbol=symbol,
            direction=direction,
            price=current_price,
            sl=sl_tp["sl"],
            tp=sl_tp["tp2"],
            size=0,  # Size will be calculated by executor
            confidence=confidence,
            reasons=reasons,
            status="egx_signal",
            result="pending",
            pnl=0.0
        )

        logger.info(f"🎯 EGX Signal generated for {symbol}: {direction} at {current_price:.2f} EGP")
        return signal

    except Exception as e:
        log_debug_signal(f"❌ Error in EGX SMC strategy for {symbol}: {str(e)}")
        logger.error(f"❌ EGX strategy error for {symbol}: {e}")
        return None

# Alias for compatibility with existing code
async def run_smc_strategy(symbol: str, capital: float = 100000, **kwargs):
    """Compatibility wrapper for EGX strategy"""
    return await run_smc_strategy_egx(symbol, capital, **kwargs)
