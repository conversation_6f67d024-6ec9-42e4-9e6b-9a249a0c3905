export { B as BaseSequencer, V as VitestPlugin, c as createVitest, s as startVitest } from './vendor-cli-api.8cf937e8.js';
export { V as VitestExecutor } from './vendor-execute.132a3e09.js';
import 'pathe';
import './vendor-constants.538d9b49.js';
import './vendor-coverage.c8fd34c3.js';
import './vendor-index.75f2b63d.js';
import 'node:console';
import 'local-pkg';
import 'node:url';
import 'picocolors';
import './vendor-index.fad2598b.js';
import 'std-env';
import '@vitest/runner/utils';
import '@vitest/utils';
import './vendor-global.6795f91f.js';
import 'vite';
import 'node:path';
import 'node:process';
import 'node:fs';
import 'path';
import 'os';
import 'util';
import 'stream';
import 'events';
import 'fs';
import './vendor-_commonjsHelpers.76cdd49e.js';
import 'vite-node/utils';
import 'vite-node/client';
import '@vitest/snapshot/manager';
import 'vite-node/server';
import './vendor-paths.84fc7a99.js';
import 'node:v8';
import 'node:child_process';
import './vendor-index.5037f2c0.js';
import 'node:worker_threads';
import 'node:os';
import 'tinypool';
import 'node:perf_hooks';
import './vendor-tasks.f9d75aed.js';
import 'node:module';
import 'node:crypto';
import './vendor-index.c1e09929.js';
import 'node:buffer';
import 'child_process';
import 'assert';
import 'buffer';
import 'node:util';
import 'node:fs/promises';
import 'module';
import 'acorn';
import 'acorn-walk';
import 'magic-string';
import 'strip-literal';
import 'node:readline';
import 'readline';
import '@vitest/spy';
import './vendor-rpc.4d3d7a54.js';
