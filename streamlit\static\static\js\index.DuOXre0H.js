import{n as s,r as f,R as u,aE as b,ba as x,z as k,j as a,b7 as C,bb as L,C as v,D as T,aC as h}from"./index.BqDl3eRM.js";const w=s("div",{target:"e12r95n60"})(({containerWidth:r})=>({display:"flex",flexDirection:"column",width:r?"100%":"fit-content"})),S=s("a",{target:"e12r95n61"})(({disabled:r,isCurrentPage:o,containerWidth:l,theme:e})=>({textDecoration:"none",width:l?"100%":"fit-content",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"flex-start",gap:e.spacing.sm,borderRadius:e.radii.default,paddingLeft:e.spacing.sm,paddingRight:e.spacing.sm,marginTop:e.spacing.threeXS,marginBottom:e.spacing.threeXS,lineHeight:e.lineHeights.menuItem,backgroundColor:o?e.colors.darkenedBgMix15:"transparent","&:hover":{backgroundColor:o?e.colors.darkenedBgMix25:e.colors.darkenedBgMix15},"&:active,&:visited,&:hover":{textDecoration:"none"},"&:focus":{outline:"none"},"&:focus-visible":{backgroundColor:e.colors.darkenedBgMix15},"@media print":{paddingLeft:e.spacing.none},...r?{borderColor:e.colors.borderColor,backgroundColor:e.colors.transparent,color:e.colors.fadedText40,cursor:"not-allowed","&:hover":{color:e.colors.fadedText40,backgroundColor:e.colors.transparent}}:{}})),y=s("span",{target:"e12r95n62"})(({disabled:r,theme:o})=>({color:o.colors.bodyText,overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis",display:"table-cell",...r?{borderColor:o.colors.borderColor,backgroundColor:o.colors.transparent,color:o.colors.fadedText40,cursor:"not-allowed"}:{}}));function P(r,o){return r===null&&o?!0:r===null&&!o?!1:r===!0}function D(r){const{onPageChange:o,currentPageScriptHash:l}=u.useContext(b),e=u.useContext(x),{colors:c}=k(),{disabled:t,element:n}=r,i=P(n.useContainerWidth,e),d=l===n.pageScriptHash,p=g=>{n.external?t&&g.preventDefault():(g.preventDefault(),t||o(n.pageScriptHash))};return a("div",{className:"stPageLink","data-testid":"stPageLink",children:a(C,{help:n.help,placement:L.TOP_RIGHT,containerWidth:i,children:a(w,{containerWidth:i,children:v(S,{"data-testid":"stPageLink-NavLink",disabled:t,isCurrentPage:d,containerWidth:i,href:n.page,target:n.external?"_blank":"",rel:"noreferrer",onClick:p,children:[n.icon&&a(T,{size:"lg",color:t?c.fadedText40:c.bodyText,iconValue:n.icon}),a(y,{disabled:t,children:a(h,{source:n.label,allowHTML:!1,isLabel:!0,boldLabel:d,largerLabel:!0,disableLinks:!0})})]})})})})}const H=f.memo(D);export{H as default};
