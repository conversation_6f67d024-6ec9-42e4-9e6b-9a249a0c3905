{"name": "@babel/eslint-parser", "version": "7.15.7", "description": "ESLint parser that allows for linting of experimental syntax transformed by <PERSON>l", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "eslint/babel-eslint-parser"}, "publishConfig": {"access": "public"}, "bugs": {"url": "https://github.com/babel/babel/issues"}, "homepage": "https://babel.dev/", "engines": {"node": "^10.13.0 || ^12.13.0 || >=14.0.0"}, "main": "./lib/index.cjs", "type": "commonjs", "exports": {".": "./lib/index.cjs", "./experimental-worker": "./lib/experimental-worker.cjs", "./package.json": "./package.json"}, "peerDependencies": {"@babel/core": ">=7.11.0", "eslint": ">=7.5.0"}, "dependencies": {"eslint-scope": "^5.1.1", "eslint-visitor-keys": "^2.1.0", "semver": "^6.3.0"}, "devDependencies": {"@babel/core": "7.15.5", "dedent": "^0.7.0", "eslint": "^7.27.0"}}