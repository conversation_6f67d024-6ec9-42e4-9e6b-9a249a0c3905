#!/usr/bin/env python3
"""
Test TradingView import
"""

print("Testing TradingView import...")

try:
    from tradingview_data_fetcher import get_tradingview_data, test_tradingview_connection, tv_fetcher
    print("✅ TradingView import successful")
    TRADINGVIEW_AVAILABLE = True
except ImportError as e:
    print(f"❌ TradingView import failed: {e}")
    TRADINGVIEW_AVAILABLE = False

print(f"TradingView available: {TRADINGVIEW_AVAILABLE}")

if TRADINGVIEW_AVAILABLE:
    print("Testing connection...")
    result = test_tradingview_connection()
    print(f"Connection result: {result}")
