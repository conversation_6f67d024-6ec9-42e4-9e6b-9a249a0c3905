const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./downloader.B-uWAyLB.js","./index.BqDl3eRM.js","../css/index.DqDwtg6_.css","./sandbox.DpjSeqe2.js","./memory.DGVHab07.js"])))=>i.map(i=>d[i]);
var G=Object.defineProperty;var R=a=>{throw TypeError(a)};var Y=(a,t,e)=>t in a?G(a,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):a[t]=e;var g=(a,t,e)=>Y(a,typeof t!="symbol"?t+"":t,e),X=(a,t,e)=>t.has(a)||R("Cannot "+e);var j=(a,t,e)=>t.has(a)?R("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(a):t.set(a,e),M=(a,t,e,r)=>(X(a,t,"write to private field"),r?r.call(a,e):t.set(a,e),e);import{y as d}from"./index.BqDl3eRM.js";const N=globalThis.showDirectoryPicker;async function k(a={}){if(N&&!a._preferPolyfill)return N(a);const t=document.createElement("input");t.type="file",t.webkitdirectory=!0,t.multiple=!0,t.style.position="fixed",t.style.top="-100000px",t.style.left="-100000px",document.body.appendChild(t);const e=d(()=>Promise.resolve().then(()=>B),void 0,import.meta.url);return await new Promise(r=>{t.addEventListener("change",r),t.click()}),e.then(r=>r.getDirHandlesFromInput(t))}const J={accepts:[]},V=globalThis.showOpenFilePicker;async function K(a={}){const t={...J,...a};if(V&&!a._preferPolyfill)return V(t);const e=document.createElement("input");e.type="file",e.multiple=t.multiple,e.accept=(t.accepts||[]).map(n=>[...(n.extensions||[]).map(o=>"."+o),...n.mimeTypes||[]]).flat().join(","),Object.assign(e.style,{position:"fixed",top:"-100000px",left:"-100000px"}),document.body.appendChild(e);const r=d(()=>Promise.resolve().then(()=>B),void 0,import.meta.url);return await new Promise(n=>{e.addEventListener("change",n,{once:!0}),e.click()}),e.remove(),r.then(n=>n.getFileHandlesFromInput(e))}const W=globalThis.showSaveFilePicker;async function Q(a={}){if(W&&!a._preferPolyfill)return W(a);a._name&&(console.warn("deprecated _name, spec now have `suggestedName`"),a.suggestedName=a._name);const{FileSystemFileHandle:t}=await d(async()=>{const{FileSystemFileHandle:r}=await Promise.resolve().then(()=>_);return{FileSystemFileHandle:r}},void 0,import.meta.url),{FileHandle:e}=await d(async()=>{const{FileHandle:r}=await import("./downloader.B-uWAyLB.js");return{FileHandle:r}},__vite__mapDeps([0,1,2]),import.meta.url);return new t(new e(a.suggestedName))}globalThis.DataTransferItem&&!DataTransferItem.prototype.getAsFileSystemHandle&&(DataTransferItem.prototype.getAsFileSystemHandle=async function(){const a=this.webkitGetAsEntry(),[{FileHandle:t,FolderHandle:e},{FileSystemDirectoryHandle:r},{FileSystemFileHandle:n}]=await Promise.all([d(()=>import("./sandbox.DpjSeqe2.js"),__vite__mapDeps([3,1,2]),import.meta.url),d(()=>Promise.resolve().then(()=>D),void 0,import.meta.url),d(()=>Promise.resolve().then(()=>_),void 0,import.meta.url)]);return a.isFile?new n(new t(a,!1)):new r(new e(a,!1))});async function Z(a,t={}){var o,s;if(!a)return((s=(o=globalThis.navigator)==null?void 0:o.storage)==null?void 0:s.getDirectory())||globalThis.getOriginPrivateDirectory();const{FileSystemDirectoryHandle:e}=await d(async()=>{const{FileSystemDirectoryHandle:y}=await Promise.resolve().then(()=>D);return{FileSystemDirectoryHandle:y}},void 0,import.meta.url),r=await a,n=await(r.default?r.default(t):r(t));return new e(n)}const ee={WritableStream:globalThis.WritableStream,TransformStream:globalThis.TransformStream,DOMException:globalThis.DOMException,Blob:globalThis.Blob,File:globalThis.File},{WritableStream:te}=ee;var H;const L=class L extends te{constructor(e){super(e);j(this,H);M(this,H,e),Object.setPrototypeOf(this,L.prototype),this._closed=!1}async close(){this._closed=!0;const e=this.getWriter(),r=e.close();return e.releaseLock(),r}seek(e){return this.write({type:"seek",position:e})}truncate(e){return this.write({type:"truncate",size:e})}write(e){if(this._closed)return Promise.reject(new TypeError("Cannot write to a CLOSED writable stream"));const r=this.getWriter(),n=r.write(e);return r.releaseLock(),n}};H=new WeakMap;let p=L;Object.defineProperty(p.prototype,Symbol.toStringTag,{value:"FileSystemWritableFileStream",writable:!1,enumerable:!1,configurable:!0});Object.defineProperties(p.prototype,{close:{enumerable:!0},seek:{enumerable:!0},truncate:{enumerable:!0},write:{enumerable:!0}});globalThis.FileSystemFileHandle&&!globalThis.FileSystemFileHandle.prototype.createWritable&&!globalThis.FileSystemWritableFileStream&&(globalThis.FileSystemWritableFileStream=p);const m=Symbol("adapter");var q;q=m;class P{constructor(t){g(this,q);g(this,"name");g(this,"kind");this.kind=t.kind,this.name=t.name,this[m]=t}async queryPermission(t={}){const{mode:e="read"}=t,r=this[m];if(r.queryPermission)return r.queryPermission({mode:e});if(e==="read")return"granted";if(e==="readwrite")return r.writable?"granted":"denied";throw new TypeError(`Mode ${e} must be 'read' or 'readwrite'`)}async requestPermission({mode:t="read"}={}){const e=this[m];if(e.requestPermission)return e.requestPermission({mode:t});if(t==="read")return"granted";if(t==="readwrite")return e.writable?"granted":"denied";throw new TypeError(`Mode ${t} must be 'read' or 'readwrite'`)}async remove(t={}){await this[m].remove(t)}async isSameEntry(t){return this===t?!0:!t||typeof t!="object"||this.kind!==t.kind||!t[m]?!1:this[m].isSameEntry(t[m])}}Object.defineProperty(P.prototype,Symbol.toStringTag,{value:"FileSystemHandle",writable:!1,enumerable:!1,configurable:!0});var $;globalThis.FileSystemHandle&&(($=globalThis.FileSystemHandle.prototype).queryPermission??($.queryPermission=function(a){return"granted"}));const x={INVALID:["seeking position failed.","InvalidStateError"],GONE:["A requested file or directory could not be found at the time an operation was processed.","NotFoundError"],MISMATCH:["The path supplied exists, but was not an entry of requested type.","TypeMismatchError"],MOD_ERR:["The object can not be modified in this way.","InvalidModificationError"],SYNTAX:a=>[`Failed to execute 'write' on 'UnderlyingSinkBase': Invalid params passed. ${a}`,"SyntaxError"],SECURITY:["It was determined that certain files are unsafe for access within a Web application, or that too many calls are being made on file resources.","SecurityError"],DISALLOWED:["The request is not allowed by the user agent or the platform in the current context.","NotAllowedError"]},re={writable:globalThis.WritableStream};async function ae(a){console.warn("deprecated fromDataTransfer - use `dt.items[0].getAsFileSystemHandle()` instead");const[t,e,r]=await Promise.all([d(()=>import("./memory.DGVHab07.js"),__vite__mapDeps([4,1,2]),import.meta.url),d(()=>import("./sandbox.DpjSeqe2.js"),__vite__mapDeps([3,1,2]),import.meta.url),d(()=>Promise.resolve().then(()=>D),void 0,import.meta.url)]),n=new t.FolderHandle("",!1);return n._entries=a.map(o=>o.isFile?new e.FileHandle(o,!1):new e.FolderHandle(o,!1)),new r.FileSystemDirectoryHandle(n)}async function ie(a){const{FolderHandle:t,FileHandle:e}=await d(async()=>{const{FolderHandle:y,FileHandle:l}=await import("./memory.DGVHab07.js");return{FolderHandle:y,FileHandle:l}},__vite__mapDeps([4,1,2]),import.meta.url),{FileSystemDirectoryHandle:r}=await d(async()=>{const{FileSystemDirectoryHandle:y}=await Promise.resolve().then(()=>D);return{FileSystemDirectoryHandle:y}},void 0,import.meta.url),n=Array.from(a.files),o=n[0].webkitRelativePath.split("/",1)[0],s=new t(o,!1);return n.forEach(y=>{const l=y.webkitRelativePath.split("/");l.shift();const c=l.pop(),f=l.reduce((w,b)=>(w._entries[b]||(w._entries[b]=new t(b,!1)),w._entries[b]),s);f._entries[c]=new e(y.name,y,!1)}),new r(s)}async function ne(a){const{FileHandle:t}=await d(async()=>{const{FileHandle:r}=await import("./memory.DGVHab07.js");return{FileHandle:r}},__vite__mapDeps([4,1,2]),import.meta.url),{FileSystemFileHandle:e}=await d(async()=>{const{FileSystemFileHandle:r}=await Promise.resolve().then(()=>_);return{FileSystemFileHandle:r}},void 0,import.meta.url);return Array.from(a.files).map(r=>new e(new t(r.name,r,!1)))}const B=Object.freeze(Object.defineProperty({__proto__:null,config:re,errors:x,fromDataTransfer:ae,getDirHandlesFromInput:ie,getFileHandlesFromInput:ne},Symbol.toStringTag,{value:"Module"})),{GONE:oe,MOD_ERR:se}=x,u=Symbol("adapter");var z;let F=class T extends P{constructor(e){super(e);g(this,z);this[u]=e}async getDirectoryHandle(e,r={}){if(e==="")throw new TypeError("Name can't be an empty string.");if(e==="."||e===".."||e.includes("/"))throw new TypeError("Name contains invalid characters.");r.create=!!r.create;const n=await this[u].getDirectoryHandle(e,r);return new T(n)}async*entries(){const{FileSystemFileHandle:e}=await d(async()=>{const{FileSystemFileHandle:r}=await Promise.resolve().then(()=>_);return{FileSystemFileHandle:r}},void 0,import.meta.url);for await(const[r,n]of this[u].entries())yield[n.name,n.kind==="file"?new e(n):new T(n)]}async*getEntries(){const{FileSystemFileHandle:e}=await d(async()=>{const{FileSystemFileHandle:r}=await Promise.resolve().then(()=>_);return{FileSystemFileHandle:r}},void 0,import.meta.url);console.warn("deprecated, use .entries() instead");for await(let r of this[u].entries())yield r.kind==="file"?new e(r):new T(r)}async getFileHandle(e,r={}){const{FileSystemFileHandle:n}=await d(async()=>{const{FileSystemFileHandle:s}=await Promise.resolve().then(()=>_);return{FileSystemFileHandle:s}},void 0,import.meta.url);if(e==="")throw new TypeError("Name can't be an empty string.");if(e==="."||e===".."||e.includes("/"))throw new TypeError("Name contains invalid characters.");r.create=!!r.create;const o=await this[u].getFileHandle(e,r);return new n(o)}async removeEntry(e,r={}){if(e==="")throw new TypeError("Name can't be an empty string.");if(e==="."||e===".."||e.includes("/"))throw new TypeError("Name contains invalid characters.");return r.recursive=!!r.recursive,this[u].removeEntry(e,r)}async resolve(e){if(await e.isSameEntry(this))return[];const r=[{handle:this,path:[]}];for(;r.length;){let{handle:n,path:o}=r.pop();for await(const s of n.values()){if(await s.isSameEntry(e))return[...o,s.name];s.kind==="directory"&&r.push({handle:s,path:[...o,s.name]})}}return null}async*keys(){for await(const[e]of this[u].entries())yield e}async*values(){for await(const[e,r]of this)yield r}[(z=u,Symbol.asyncIterator)](){return this.entries()}};Object.defineProperty(F.prototype,Symbol.toStringTag,{value:"FileSystemDirectoryHandle",writable:!1,enumerable:!1,configurable:!0});Object.defineProperties(F.prototype,{getDirectoryHandle:{enumerable:!0},entries:{enumerable:!0},getFileHandle:{enumerable:!0},removeEntry:{enumerable:!0}});if(globalThis.FileSystemDirectoryHandle){const a=globalThis.FileSystemDirectoryHandle.prototype;a.resolve=async function(o){if(await o.isSameEntry(this))return[];const s=[{handle:this,path:[]}];for(;s.length;){let{handle:y,path:l}=s.pop();for await(const c of y.values()){if(await c.isSameEntry(o))return[...l,c.name];c.kind==="directory"&&s.push({handle:c,path:[...l,c.name]})}}return null};async function t(n){if(await(await navigator.storage.getDirectory()).resolve(n)===null)throw new DOMException(...oe)}const e=a.entries;a.entries=async function*(){await t(this),yield*e.call(this)},a[Symbol.asyncIterator]=async function*(){yield*this.entries()};const r=a.removeEntry;a.removeEntry=async function(n,o={}){return r.call(this,n,o).catch(async s=>{throw s instanceof DOMException&&s.name==="UnknownError"&&!o.recursive&&!(await e.call(this).next()).done?new DOMException(...se):s})}}const D=Object.freeze(Object.defineProperty({__proto__:null,FileSystemDirectoryHandle:F,default:F},Symbol.toStringTag,{value:"Module"})),{INVALID:le,SYNTAX:I,GONE:ce}=x,v=Symbol("adapter");var U,C;class E extends(C=P,U=v,C){constructor(e){super(e);g(this,U);this[v]=e}async createWritable(e={}){return new p(await this[v].createWritable(e))}async getFile(){return this[v].getFile()}}Object.defineProperty(E.prototype,Symbol.toStringTag,{value:"FileSystemFileHandle",writable:!1,enumerable:!1,configurable:!0});Object.defineProperties(E.prototype,{createWritable:{enumerable:!0},getFile:{enumerable:!0}});if(globalThis.FileSystemFileHandle&&!globalThis.FileSystemFileHandle.prototype.createWritable){const a=new WeakMap;let t;const e=()=>{let n,o;onmessage=async s=>{const y=s.ports[0],l=s.data;switch(l.type){case"open":const c=l.name;let f=await navigator.storage.getDirectory();for(const w of l.path)f=await f.getDirectoryHandle(w);n=await f.getFileHandle(c),o=await n.createSyncAccessHandle();break;case"write":o.write(l.data,{at:l.position}),o.flush();break;case"truncate":o.truncate(l.size);break;case"abort":case"close":o.close();break}y.postMessage(0)}};globalThis.FileSystemFileHandle.prototype.createWritable=async function(n){if(!t){const i=`(${e.toString()})()`,S=new Blob([i],{type:"text/javascript"});t=URL.createObjectURL(S)}const o=new Worker(t,{type:"module"});let s=0;const y=new TextEncoder;let l=await this.getFile().then(i=>i.size);const c=i=>new Promise((S,O)=>{const h=new MessageChannel;h.port1.onmessage=A=>{A.data instanceof Error?O(A.data):S(A.data),h.port1.close(),h.port2.close(),h.port1.onmessage=null},o.postMessage(i,[h.port2])}),f=await navigator.storage.getDirectory(),w=await a.get(this),b=await f.resolve(w);if(b===null)throw new DOMException(...ce);return await c({type:"open",path:b,name:this.name}),(n==null?void 0:n.keepExistingData)===!1&&(await c({type:"truncate",size:0}),l=0),new p({start:i=>{},async write(i){if((i==null?void 0:i.constructor)===Object?i={...i}:i={type:"write",data:i,position:s},i.type==="write"){if(!("data"in i))throw await c({type:"close"}),new DOMException(...I("write requires a data argument"));if(i.position??(i.position=s),typeof i.data=="string")i.data=y.encode(i.data);else if(i.data instanceof ArrayBuffer)i.data=new Uint8Array(i.data);else if(!(i.data instanceof Uint8Array)&&ArrayBuffer.isView(i.data))i.data=new Uint8Array(i.data.buffer,i.data.byteOffset,i.data.byteLength);else if(!(i.data instanceof Uint8Array)){const O=await new Response(i.data).arrayBuffer();i.data=new Uint8Array(O)}Number.isInteger(i.position)&&i.position>=0&&(s=i.position),s+=i.data.byteLength,l+=i.data.byteLength}else if(i.type==="seek")if(Number.isInteger(i.position)&&i.position>=0){if(l<i.position)throw new DOMException(...le);console.log("seeking",i),s=i.position;return}else throw await c({type:"close"}),new DOMException(...I("seek requires a position argument"));else if(i.type==="truncate")if(Number.isInteger(i.size)&&i.size>=0)l=i.size,s>l&&(s=l);else throw await c({type:"close"}),new DOMException(...I("truncate requires a size argument"));await c(i)},async close(){await c({type:"close"}),o.terminate()},async abort(i){await c({type:"abort",reason:i}),o.terminate()}})};const r=FileSystemDirectoryHandle.prototype.getFileHandle;FileSystemDirectoryHandle.prototype.getFileHandle=async function(...n){const o=await r.call(this,...n);return a.set(o,this),o}}const _=Object.freeze(Object.defineProperty({__proto__:null,FileSystemFileHandle:E,default:E},Symbol.toStringTag,{value:"Module"})),ue=Object.freeze(Object.defineProperty({__proto__:null,FileSystemDirectoryHandle:F,FileSystemFileHandle:E,FileSystemHandle:P,FileSystemWritableFileStream:p,getOriginPrivateDirectory:Z,showDirectoryPicker:k,showOpenFilePicker:K,showSaveFilePicker:Q},Symbol.toStringTag,{value:"Module"}));export{ue as a,ee as c,x as e};
