#!/usr/bin/env python3
"""
Real TradingView Data Fetcher
Using yfinance as a reliable alternative to get real market data
"""

import pandas as pd
import yfinance as yf
from typing import Optional, Dict, List
import streamlit as st
from datetime import datetime, timedelta

class RealTradingViewFetcher:
    """Fetch real market data using yfinance"""
    
    def __init__(self):
        self.connected = True
        
    def get_egx_symbol_mapping(self, symbol: str) -> str:
        """Map EGX symbols to Yahoo Finance format"""
        
        # EGX symbols on Yahoo Finance
        egx_mappings = {
            'CIB': 'COMI.CA',      # Commercial International Bank
            'COMI': 'COMI.CA',     # Commercial International Bank
            'ADIB': 'ADIB.CA',     # Abu Dhabi Islamic Bank Egypt
            'ALEX': 'ALEX.CA',     # Bank of Alexandria
            'SAIB': 'SAIB.CA',     # Société Arabe Internationale de Banque
            'ETEL': 'ETEL.CA',     # Egyptian Company for Mobile Services
            'ORTE': 'ORTE.CA',     # Orascom Telecom
            'PHDC': 'PHDC.CA',     # Palm Hills Developments
            'SODIC': 'SODIC.CA',   # Sixth of October Development & Investment
            'TMG': 'TMG.CA',       # Talaat Moustafa Group
            'IRON': 'IRON.CA',     # Egyptian Iron & Steel
            'ESRS': 'ESRS.CA',     # Egyptian Steel
            'JUFO': 'JUFO.CA',     # Juhayna Food Industries
            'DOMTY': 'DOMTY.CA',   # Domty
        }
        
        clean_symbol = symbol.upper().replace('.CA', '')
        return egx_mappings.get(clean_symbol, f"{clean_symbol}.CA")
    
    def get_timeframe_mapping(self, timeframe: str) -> str:
        """Map timeframe to yfinance period"""
        mapping = {
            '1D': '2y',    # 2 years of daily data
            '4h': '60d',   # 60 days for 4h (will be resampled)
            '1h': '30d',   # 30 days for 1h (will be resampled)
            '30m': '7d',   # 7 days for 30m (will be resampled)
            '15m': '5d',   # 5 days for 15m (will be resampled)
        }
        return mapping.get(timeframe, '2y')
    
    def get_interval_mapping(self, timeframe: str) -> str:
        """Map timeframe to yfinance interval"""
        mapping = {
            '1D': '1d',
            '4h': '1h',    # Will resample to 4h
            '1h': '1h',
            '30m': '30m',
            '15m': '15m',
        }
        return mapping.get(timeframe, '1d')
    
    def resample_data(self, df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """Resample data to target timeframe"""
        
        if timeframe == '4h':
            # Resample hourly data to 4-hour
            df_resampled = df.resample('4H').agg({
                'Open': 'first',
                'High': 'max',
                'Low': 'min',
                'Close': 'last',
                'Volume': 'sum'
            }).dropna()
            return df_resampled
        
        return df  # No resampling needed
    
    def fetch_real_data(self, symbol: str, timeframe: str = '1D', bars: int = 500) -> Optional[pd.DataFrame]:
        """Fetch real market data from Yahoo Finance"""
        
        try:
            # Map symbol to Yahoo Finance format
            yf_symbol = self.get_egx_symbol_mapping(symbol)
            period = self.get_timeframe_mapping(timeframe)
            interval = self.get_interval_mapping(timeframe)
            
            print(f"📡 Fetching real data for {yf_symbol} ({timeframe})...")
            
            # Create ticker object
            ticker = yf.Ticker(yf_symbol)
            
            # Fetch data
            data = ticker.history(
                period=period,
                interval=interval,
                auto_adjust=True,
                prepost=True
            )
            
            if data.empty:
                print(f"⚠️ No data received for {yf_symbol}")
                return None
            
            # Resample if needed
            if timeframe == '4h':
                data = self.resample_data(data, timeframe)
            
            # Standardize column names for SMC analysis
            data = data.rename(columns={
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume'
            })
            
            # Ensure all required columns exist
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in data.columns:
                    print(f"❌ Missing column: {col}")
                    return None
            
            # Convert to numeric and clean data
            for col in required_columns:
                data[col] = pd.to_numeric(data[col], errors='coerce')
            
            # Remove any rows with NaN values
            data = data.dropna()
            
            if len(data) < 50:
                print(f"⚠️ Insufficient data for {yf_symbol} (got {len(data)}, need at least 50)")
                return None
            
            # Limit to requested number of bars
            if len(data) > bars:
                data = data.tail(bars)
            
            # Sort by datetime index
            data = data.sort_index()
            
            print(f"✅ Fetched {len(data)} bars of real data for {symbol}")
            print(f"📊 Latest price: {data['close'].iloc[-1]:.2f} EGP")
            
            return data
            
        except Exception as e:
            print(f"❌ Error fetching real data for {symbol}: {e}")
            return None
    
    def get_available_symbols(self) -> List[str]:
        """Get list of available EGX symbols"""
        return [
            'CIB', 'COMI', 'ADIB', 'ALEX', 'SAIB',  # Banks
            'ETEL', 'ORTE',  # Telecom
            'PHDC', 'SODIC', 'TMG',  # Real Estate
            'IRON', 'ESRS',  # Industrial
            'JUFO', 'DOMTY'  # Consumer Goods
        ]
    
    def test_connection(self, symbol: str = 'COMI') -> Dict:
        """Test connection with real data"""
        
        test_results = {
            'connected': self.connected,
            'test_symbol': symbol,
            'test_successful': False,
            'data_points': 0,
            'latest_price': 0.0,
            'error': None
        }
        
        try:
            # Test with COMI (Commercial International Bank)
            test_data = self.fetch_real_data(symbol, '1D', 100)
            
            if test_data is not None and not test_data.empty:
                test_results['test_successful'] = True
                test_results['data_points'] = len(test_data)
                test_results['latest_price'] = float(test_data['close'].iloc[-1])
            else:
                test_results['error'] = "No data received"
                
        except Exception as e:
            test_results['error'] = str(e)
        
        return test_results

# Global instance
real_tv_fetcher = RealTradingViewFetcher()

def get_real_tradingview_data(symbol: str, timeframe: str = '1D', bars: int = 500) -> Optional[pd.DataFrame]:
    """Convenience function to fetch real TradingView data"""
    return real_tv_fetcher.fetch_real_data(symbol, timeframe, bars)

def test_real_tradingview_connection(symbol: str = 'COMI') -> Dict:
    """Test real TradingView data connection"""
    return real_tv_fetcher.test_connection(symbol)
