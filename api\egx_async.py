"""
EGX (Egyptian Exchange) API client for fetching stock data via TradingView bridge
Replaces the Bybit crypto API with EGX stock market data
"""

import aiohttp
import asyncio
from typing import List, Dict, Optional, Tuple
import logging
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Configuration
BRIDGE_BASE_URL = "http://localhost:3001/api/v1"
DEFAULT_EXCHANGE = "EGX"
REQUEST_TIMEOUT = 10
MAX_RETRIES = 3
RETRY_DELAY = 1

class EGXAPIError(Exception):
    """Custom exception for EGX API errors"""
    pass

class EGXClient:
    """Async client for EGX stock data via TradingView bridge"""
    
    def __init__(self, base_url: str = BRIDGE_BASE_URL):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=REQUEST_TIMEOUT)
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def _make_request(self, endpoint: str, params: Dict = None, method: str = "GET", data: Dict = None) -> Dict:
        """Make HTTP request to the bridge API with retry logic"""
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        for attempt in range(MAX_RETRIES):
            try:
                if not self.session:
                    self.session = aiohttp.ClientSession(
                        timeout=aiohttp.ClientTimeout(total=REQUEST_TIMEOUT)
                    )
                
                if method.upper() == "GET":
                    async with self.session.get(url, params=params) as response:
                        result = await response.json()
                elif method.upper() == "POST":
                    async with self.session.post(url, json=data, params=params) as response:
                        result = await response.json()
                else:
                    raise EGXAPIError(f"Unsupported HTTP method: {method}")
                
                if not result.get("success", False):
                    raise EGXAPIError(f"API error: {result.get('error', 'Unknown error')}")
                
                return result["data"]
                
            except aiohttp.ClientError as e:
                logger.warning(f"Request failed (attempt {attempt + 1}/{MAX_RETRIES}): {e}")
                if attempt == MAX_RETRIES - 1:
                    raise EGXAPIError(f"Failed to connect to bridge API: {e}")
                await asyncio.sleep(RETRY_DELAY * (attempt + 1))
            except Exception as e:
                logger.error(f"Unexpected error in API request: {e}")
                raise EGXAPIError(f"Request failed: {e}")

# Global client instance
_client = EGXClient()

async def get_ohlcv(symbol: str, interval: str = "1h", limit: int = 150) -> List[Dict]:
    """
    Fetch OHLCV (candlestick) data for an EGX symbol
    
    Args:
        symbol: EGX stock symbol (e.g., "CIB", "ETEL")
        interval: Timeframe (1m, 5m, 15m, 30m, 1h, 2h, 4h, 1d, 1w, 1M)
        limit: Number of candles to fetch (max 5000)
    
    Returns:
        List of OHLCV dictionaries with timestamp, open, high, low, close, volume
    """
    try:
        logger.info(f"Fetching OHLCV data for {symbol} ({interval}, {limit} bars)")
        
        params = {
            "exchange": DEFAULT_EXCHANGE,
            "interval": interval,
            "limit": limit
        }
        
        response = await _client._make_request(f"ohlcv/{symbol}", params=params)

        # Extract the actual data from the response
        if isinstance(response, dict) and "data" in response:
            data = response["data"]
        else:
            data = response

        # Debug: Log the actual data structure received
        if data and len(data) > 0:
            logger.info(f"🔍 DEBUG: First candle structure for {symbol}: {data[0]}")
            logger.info(f"🔍 DEBUG: Available keys: {list(data[0].keys())}")

        # Convert to format expected by the trading bot
        ohlcv_data = []
        for candle in data:
            # Handle different possible data formats from TradingView bridge
            try:
                # Try standard format first (with high/low)
                if "high" in candle and "low" in candle:
                    ohlcv_data.append({
                        "timestamp": candle["timestamp"],
                        "open": float(candle["open"]),
                        "high": float(candle["high"]),
                        "low": float(candle["low"]),
                        "close": float(candle["close"]),
                        "volume": float(candle.get("volume", 0))
                    })
                # Handle incomplete data (missing high/low) - use open/close as approximation
                elif "open" in candle and "close" in candle:
                    open_price = float(candle["open"])
                    close_price = float(candle["close"])
                    # Estimate high/low from open/close (not perfect but workable)
                    high_price = max(open_price, close_price)
                    low_price = min(open_price, close_price)

                    ohlcv_data.append({
                        "timestamp": candle["timestamp"],
                        "open": open_price,
                        "high": high_price,
                        "low": low_price,
                        "close": close_price,
                        "volume": float(candle.get("volume", 0))
                    })
                    logger.warning(f"⚠️ {symbol}: Missing high/low data, estimated from open/close")

                # Try alternative format (maybe different key names)
                elif "High" in candle:
                    ohlcv_data.append({
                        "timestamp": candle["timestamp"],
                        "open": float(candle["Open"]),
                        "high": float(candle["High"]),
                        "low": float(candle["Low"]),
                        "close": float(candle["Close"]),
                        "volume": float(candle.get("Volume", 0))
                    })
                # Try array format [timestamp, open, high, low, close, volume]
                elif isinstance(candle, list) and len(candle) >= 5:
                    ohlcv_data.append({
                        "timestamp": candle[0],
                        "open": float(candle[1]),
                        "high": float(candle[2]),
                        "low": float(candle[3]),
                        "close": float(candle[4]),
                        "volume": float(candle[5] if len(candle) > 5 else 0)
                    })
                # Try numeric keys format
                elif "1" in candle:  # TradingView sometimes uses numeric keys
                    ohlcv_data.append({
                        "timestamp": candle["0"],
                        "open": float(candle["1"]),
                        "high": float(candle["2"]),
                        "low": float(candle["3"]),
                        "close": float(candle["4"]),
                        "volume": float(candle.get("5", 0))
                    })
                else:
                    logger.error(f"❌ Unknown candle format for {symbol}: {candle}")
                    continue

            except (KeyError, ValueError, TypeError) as e:
                logger.error(f"❌ Error processing candle for {symbol}: {e}, candle: {candle}")
                continue
        
        logger.info(f"Successfully fetched {len(ohlcv_data)} OHLCV bars for {symbol}")
        return ohlcv_data
        
    except Exception as e:
        logger.error(f"Error fetching OHLCV for {symbol}: {e}")
        raise EGXAPIError(f"Failed to fetch OHLCV data: {e}")

async def get_current_price(symbol: str) -> Dict:
    """
    Get current price data for an EGX symbol
    
    Args:
        symbol: EGX stock symbol (e.g., "CIB", "ETEL")
    
    Returns:
        Dictionary with current price information
    """
    try:
        logger.info(f"Fetching current price for {symbol}")
        
        params = {"exchange": DEFAULT_EXCHANGE}
        data = await _client._make_request(f"price/{symbol}", params=params)
        
        # Convert to format expected by the trading bot
        price_data = {
            "symbol": data["symbol"],
            "price": float(data["price"]),
            "open": float(data.get("open", data["price"])),
            "high": float(data.get("high", data["price"])),
            "low": float(data.get("low", data["price"])),
            "volume": float(data.get("volume", 0)),
            "change": float(data.get("change", 0)),
            "changePercent": float(data.get("changePercent", 0)),
            "timestamp": data["timestamp"]
        }
        
        logger.info(f"Current price for {symbol}: {price_data['price']}")
        return price_data
        
    except Exception as e:
        logger.error(f"Error fetching price for {symbol}: {e}")
        raise EGXAPIError(f"Failed to fetch current price: {e}")

async def get_multiple_prices(symbols: List[str]) -> Dict[str, Dict]:
    """
    Get current prices for multiple EGX symbols
    
    Args:
        symbols: List of EGX stock symbols
    
    Returns:
        Dictionary mapping symbols to their price data
    """
    try:
        logger.info(f"Fetching prices for {len(symbols)} symbols")
        
        data = {
            "symbols": symbols,
            "exchange": DEFAULT_EXCHANGE
        }
        
        result = await _client._make_request("batch/prices", method="POST", data=data)
        
        # Convert to dictionary format
        prices = {}
        for item in result:
            if item["success"]:
                symbol = item["symbol"]
                price_data = item["data"]
                prices[symbol] = {
                    "symbol": price_data["symbol"],
                    "price": float(price_data["price"]),
                    "open": float(price_data["open"]),
                    "high": float(price_data["high"]),
                    "low": float(price_data["low"]),
                    "volume": float(price_data["volume"]),
                    "change": float(price_data["change"]),
                    "changePercent": float(price_data["changePercent"]),
                    "timestamp": price_data["timestamp"]
                }
            else:
                logger.warning(f"Failed to fetch price for {item['symbol']}: {item['error']}")
                prices[item["symbol"]] = None
        
        logger.info(f"Successfully fetched prices for {len([p for p in prices.values() if p])} symbols")
        return prices
        
    except Exception as e:
        logger.error(f"Error fetching multiple prices: {e}")
        raise EGXAPIError(f"Failed to fetch multiple prices: {e}")

async def search_symbols(query: str) -> List[Dict]:
    """
    Search for EGX symbols
    
    Args:
        query: Search query (symbol or company name)
    
    Returns:
        List of matching symbols with metadata
    """
    try:
        logger.info(f"Searching symbols for: {query}")
        
        params = {
            "q": query,
            "exchange": DEFAULT_EXCHANGE
        }
        
        data = await _client._make_request("symbols/search", params=params)
        
        logger.info(f"Found {len(data)} symbols matching '{query}'")
        return data
        
    except Exception as e:
        logger.error(f"Error searching symbols for '{query}': {e}")
        raise EGXAPIError(f"Failed to search symbols: {e}")

async def get_common_egx_symbols() -> List[Dict]:
    """
    Get list of common EGX symbols
    
    Returns:
        List of common EGX symbols with metadata
    """
    try:
        logger.info("Fetching common EGX symbols")
        
        data = await _client._make_request("symbols/egx")
        
        logger.info(f"Retrieved {len(data)} common EGX symbols")
        return data
        
    except Exception as e:
        logger.error(f"Error fetching common EGX symbols: {e}")
        raise EGXAPIError(f"Failed to fetch common symbols: {e}")

async def check_bridge_status() -> Dict:
    """
    Check the status of the TradingView bridge service
    
    Returns:
        Dictionary with service status information
    """
    try:
        data = await _client._make_request("status")
        return data
        
    except Exception as e:
        logger.error(f"Error checking bridge status: {e}")
        raise EGXAPIError(f"Failed to check bridge status: {e}")

# Legacy compatibility functions (to replace Bybit functions)
async def fetch_balance() -> Tuple[float, float, float]:
    """
    Legacy compatibility function - returns dummy balance for stocks
    For stocks, we don't have a traditional balance like crypto exchanges
    """
    logger.warning("fetch_balance called - returning dummy values for stock trading")
    return (100000.0, 100000.0, 0.0)  # total, free, used

async def get_open_positions() -> List[Dict]:
    """
    Legacy compatibility function - returns empty positions
    For stocks, position management would be handled differently
    """
    logger.warning("get_open_positions called - returning empty list for stock trading")
    return []

# Initialize client on module import
async def init_egx_client():
    """Initialize the EGX client"""
    global _client
    try:
        # Test connection to bridge
        status = await check_bridge_status()
        logger.info(f"✅ EGX bridge connected: {status.get('connected', False)}")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to connect to EGX bridge: {e}")
        return False
