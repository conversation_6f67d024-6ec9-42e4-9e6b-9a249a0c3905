{"name": "semver", "version": "6.3.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap", "preversion": "npm test", "postversion": "npm publish", "postpublish": "git push origin --follow-tags"}, "devDependencies": {"tap": "^14.3.1"}, "license": "ISC", "repository": "https://github.com/npm/node-semver", "bin": {"semver": "./bin/semver.js"}, "files": ["bin", "range.bnf", "semver.js"], "tap": {"check-coverage": true}}