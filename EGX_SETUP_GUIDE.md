# 🇪🇬 EGX Integration Setup Guide

This guide will help you set up the hybrid TradingView-EGX integration for your trading bot.

## 📋 Prerequisites

- ✅ **Node.js** (v16 or higher)
- ✅ **Python** (3.8 or higher) 
- ✅ **Virtual environment** (already set up)
- ✅ **Internet connection** for TradingView data

## 🚀 Quick Start

### Step 1: Set Up Node.js Bridge

```bash
# Navigate to the bridge directory
cd nodejs-bridge

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env

# Edit configuration (optional)
nano .env

# Start the bridge
npm start
```

The bridge will start on `http://localhost:3001`

### Step 2: Test the Bridge

```bash
# In a new terminal, test the API
npm test
```

You should see successful API responses for EGX symbols.

### Step 3: Test Python Integration

```bash
# Back in the main project directory
cd ..

# Activate your virtual environment
.\venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac

# Install additional Python dependencies
pip install aiohttp pytz

# Run the integration test
python test_egx_integration.py
```

## 📊 Available EGX Symbols

The system supports major EGX stocks including:

### 🏦 Banking
- **CIB** - Commercial International Bank
- **COMI** - Commercial Bank of Kuwait

### 📱 Telecommunications  
- **ETEL** - Egyptian Company for Mobile Services
- **ORTE** - Orascom Telecom Media and Technology

### 🏠 Real Estate
- **HELI** - Heliopolis Housing
- **PHDC** - Palm Hills Developments
- **TMGH** - TMG Holding

### ⚡ Energy & Industrial
- **EGAS** - Egyptian Natural Gas Company
- **SWDY** - El Sewedy Electric Company
- **IRON** - Iron and Steel Company

### 🏗️ Construction
- **HRHO** - Hassan Allam Holding
- **OCDI** - Orascom Construction Industries

## 🔧 Configuration

### Node.js Bridge (.env)
```env
PORT=3001
NODE_ENV=development

# TradingView credentials (optional)
TRADINGVIEW_USERNAME=your_username
TRADINGVIEW_PASSWORD=your_password

# API settings
API_RATE_LIMIT=100
CACHE_DURATION=5000
```

### Python Bot Integration

The EGX integration is designed to be a drop-in replacement for the Bybit API:

```python
# Old Bybit import
# from api.bybit_async import get_ohlcv, get_current_price

# New EGX import  
from api.egx_async import get_ohlcv, get_current_price

# Usage remains the same
ohlcv_data = await get_ohlcv("CIB", interval="1h", limit=150)
price_data = await get_current_price("CIB")
```

## 📈 API Endpoints

### Current Price
```bash
curl "http://localhost:3001/api/v1/price/CIB?exchange=EGX"
```

### OHLCV Data
```bash
curl "http://localhost:3001/api/v1/ohlcv/CIB?exchange=EGX&interval=1h&limit=10"
```

### Search Symbols
```bash
curl "http://localhost:3001/api/v1/symbols/search?q=bank"
```

### Batch Prices
```bash
curl -X POST "http://localhost:3001/api/v1/batch/prices" \
  -H "Content-Type: application/json" \
  -d '{"symbols": ["CIB", "ETEL", "HELI"], "exchange": "EGX"}'
```

## ⏰ Market Hours

**EGX Trading Hours (Cairo Time):**
- 📅 **Days**: Sunday to Thursday
- 🕙 **Open**: 10:00 AM  
- 🕐 **Close**: 2:30 PM
- 🌍 **Timezone**: Africa/Cairo

## 🔄 Timeframes Supported

- **Minutes**: 1m, 3m, 5m, 15m, 30m
- **Hours**: 1h, 2h, 4h
- **Daily**: 1d
- **Weekly**: 1w
- **Monthly**: 1M

## 🛠️ Troubleshooting

### Bridge Not Starting
```bash
# Check Node.js version
node --version  # Should be v16+

# Check port availability
netstat -an | grep 3001

# Check logs
tail -f nodejs-bridge/logs/bridge.log
```

### Python Connection Issues
```bash
# Test bridge connectivity
curl http://localhost:3001/health

# Check Python dependencies
pip list | grep aiohttp

# Run with debug logging
python test_egx_integration.py
```

### TradingView Connection Issues
- Verify internet connection
- Check if TradingView is accessible
- Try without login credentials first
- Check bridge logs for specific errors

### Symbol Not Found
- Verify symbol exists on EGX
- Use correct symbol format (e.g., "CIB" not "EGX:CIB")
- Try symbol search endpoint first
- Check TradingView website for symbol availability

## 📊 Performance Tips

1. **Caching**: The bridge caches data for 5 seconds
2. **Batch Requests**: Use batch endpoints for multiple symbols
3. **Rate Limiting**: Respect the 100 req/min limit
4. **Connection Pooling**: Reuse HTTP connections

## 🔒 Security Considerations

1. **Local Network**: Bridge runs on localhost by default
2. **Rate Limiting**: Built-in protection against abuse
3. **Input Validation**: All inputs are validated
4. **Error Handling**: Sensitive information is not exposed

## 📝 Logging

Logs are written to:
- **Console**: Colored output for development
- **File**: `nodejs-bridge/logs/bridge.log`
- **Errors**: `nodejs-bridge/logs/error.log`

## 🚀 Next Steps

After successful setup:

1. **Modify Trading Bot**: Update symbol lists to use EGX stocks
2. **Adapt Strategies**: Modify for stock market characteristics
3. **Paper Trading**: Test with simulated trades first
4. **Risk Management**: Adjust for stock-specific risks
5. **Market Hours**: Implement EGX trading hours logic

## 📞 Support

If you encounter issues:

1. Check the logs in `nodejs-bridge/logs/`
2. Run the test suite: `python test_egx_integration.py`
3. Verify TradingView access to EGX symbols
4. Check network connectivity and firewall settings

## 🔄 Updates

To update the bridge:

```bash
cd nodejs-bridge
npm update
```

To update Python integration:
```bash
# Pull latest changes and test
python test_egx_integration.py
```

---

**🎯 Success Criteria:**
- ✅ Bridge starts without errors
- ✅ All API endpoints respond correctly  
- ✅ Python integration tests pass
- ✅ EGX symbols return valid data
- ✅ Real-time price updates work

Once all criteria are met, you're ready to integrate with your trading bot! 🚀
