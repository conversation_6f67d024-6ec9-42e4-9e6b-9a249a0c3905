# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/ChatInput.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fstreamlit/proto/ChatInput.proto\"\xd0\x02\n\tChatInput\x12\n\n\x02id\x18\x01 \x01(\t\x12\x13\n\x0bplaceholder\x18\x02 \x01(\t\x12\x11\n\tmax_chars\x18\x03 \x01(\r\x12\x10\n\x08\x64isabled\x18\x04 \x01(\x08\x12\r\n\x05value\x18\x05 \x01(\t\x12\x11\n\tset_value\x18\x06 \x01(\x08\x12\x0f\n\x07\x64\x65\x66\x61ult\x18\x07 \x01(\t\x12%\n\x08position\x18\x08 \x01(\x0e\x32\x13.ChatInput.Position\x12*\n\x0b\x61\x63\x63\x65pt_file\x18\t \x01(\x0e\x32\x15.ChatInput.AcceptFile\x12\x11\n\tfile_type\x18\n \x03(\t\x12\x1a\n\x12max_upload_size_mb\x18\x0b \x01(\x05\"\x16\n\x08Position\x12\n\n\x06\x42OTTOM\x10\x00\"0\n\nAcceptFile\x12\x08\n\x04NONE\x10\x00\x12\n\n\x06SINGLE\x10\x01\x12\x0c\n\x08MULTIPLE\x10\x02\x42.\n\x1c\x63om.snowflake.apps.streamlitB\x0e\x43hatInputProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.ChatInput_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\016ChatInputProto'
  _globals['_CHATINPUT']._serialized_start=36
  _globals['_CHATINPUT']._serialized_end=372
  _globals['_CHATINPUT_POSITION']._serialized_start=300
  _globals['_CHATINPUT_POSITION']._serialized_end=322
  _globals['_CHATINPUT_ACCEPTFILE']._serialized_start=324
  _globals['_CHATINPUT_ACCEPTFILE']._serialized_end=372
# @@protoc_insertion_point(module_scope)
