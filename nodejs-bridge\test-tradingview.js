const { Client } = require('@mathieuc/tradingview');

console.log('🧪 Testing TradingView integration...');

const client = new Client();

client.onConnected(() => {
    console.log('✅ TradingView client connected!');
    
    // Test with a known working symbol first
    const chart = new client.Session.Chart();
    
    chart.setMarket('BINANCE:BTCUSDT', {
        timeframe: '1',
    });
    
    chart.onSymbolLoaded(() => {
        console.log('✅ BTCUSDT symbol loaded');
    });
    
    chart.onUpdate(() => {
        if (!chart.periods || chart.periods.length === 0) return;
        
        const lastPeriod = chart.periods[chart.periods.length - 1];
        console.log(`📊 BTCUSDT Price: ${lastPeriod.close}`);
        
        // Now test EGX symbol
        setTimeout(() => {
            console.log('\n🇪🇬 Testing EGX symbol...');
            
            const egxChart = new client.Session.Chart();
            egxChart.setMarket('EGX:COMI', {
                timeframe: '1D',
            });
            
            egxChart.onSymbolLoaded(() => {
                console.log('✅ EGX:COMI symbol loaded');
            });
            
            egxChart.onUpdate(() => {
                if (!egxChart.periods || egxChart.periods.length === 0) return;
                
                const lastPeriod = egxChart.periods[egxChart.periods.length - 1];
                console.log(`📊 EGX:COMI Price: ${lastPeriod.close} EGP`);
                
                // Clean up and exit
                setTimeout(() => {
                    chart.delete();
                    egxChart.delete();
                    client.end();
                    console.log('✅ Test completed successfully!');
                }, 2000);
            });
            
            egxChart.onError((error) => {
                console.error('❌ EGX Chart error:', error);
                chart.delete();
                egxChart.delete();
                client.end();
            });
            
        }, 3000);
    });
    
    chart.onError((error) => {
        console.error('❌ BTC Chart error:', error);
        chart.delete();
        client.end();
    });
});

client.onError((error) => {
    console.error('❌ Client error:', error);
});

client.onDisconnected(() => {
    console.log('🔌 Client disconnected');
});

// Timeout after 30 seconds
setTimeout(() => {
    console.log('⏰ Test timeout - closing client');
    client.end();
}, 30000);
