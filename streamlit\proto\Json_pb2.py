# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/Json.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1astreamlit/proto/Json.proto\"Z\n\x04Json\x12\x0c\n\x04\x62ody\x18\x01 \x01(\t\x12\x10\n\x08\x65xpanded\x18\x02 \x01(\x08\x12\x1d\n\x10max_expand_depth\x18\x03 \x01(\x05H\x00\x88\x01\x01\x42\x13\n\x11_max_expand_depthB)\n\x1c\x63om.snowflake.apps.streamlitB\tJsonProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.Json_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\tJsonProto'
  _globals['_JSON']._serialized_start=30
  _globals['_JSON']._serialized_end=120
# @@protoc_insertion_point(module_scope)
