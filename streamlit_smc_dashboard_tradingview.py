#!/usr/bin/env python3
"""
🇪🇬 EGX Professional Smart Money Concepts Dashboard with TradingView
Advanced SMC Trading Platform for Egyptian Exchange with Professional Charts
"""

import streamlit as st
import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import sys

# Add project root to path
sys.path.append('.')

# Import SMC modules
try:
    from indicators.market_structure import detect_market_structure
    from indicators.order_blocks import detect_order_blocks, get_active_order_blocks, analyze_order_block_confluence
    from indicators.fvg import detect_fvg, get_active_fvgs, analyze_fvg_confluence
    from indicators.liquidity_zones import detect_liquidity_zones, get_active_liquidity_zones, analyze_liquidity_confluence
    from indicators.displacement import detect_displacement, get_recent_displacements, analyze_displacement_confluence
    from indicators.eqh_eql import find_equal_highs_lows
    from indicators.candlestick_patterns import detect_candlestick_patterns
    from indicators.indicators import get_indicators
    from egx_smc_parameters import get_egx_parameters
    from tradingview_integration import render_tradingview_chart, create_mini_tradingview_widget, get_tradingview_symbol
except ImportError as e:
    st.error(f"❌ Error importing SMC modules: {e}")
    st.stop()

# Professional page configuration
st.set_page_config(
    page_title="🇪🇬 EGX Professional SMC Dashboard",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for professional styling
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;
        margin: 0.5rem 0;
    }
    .signal-strong { border-left-color: #28a745 !important; }
    .signal-moderate { border-left-color: #ffc107 !important; }
    .signal-weak { border-left-color: #dc3545 !important; }
    .smc-section {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }
    .tradingview-upgrade {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)

def load_csv_data(file_path: str) -> Optional[pd.DataFrame]:
    """Load and validate CSV data"""
    try:
        df = pd.read_csv(file_path)
        required_columns = ['open', 'high', 'low', 'close', 'volume']

        if not all(col in df.columns for col in required_columns):
            st.error(f"❌ Missing required columns in {file_path}")
            return None

        # Convert to numeric and handle any issues
        for col in required_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # Remove any rows with NaN values
        df = df.dropna()

        if len(df) < 50:
            st.warning(f"⚠️ Insufficient data in {file_path} (need at least 50 candles)")
            return None

        return df

    except Exception as e:
        st.error(f"❌ Error loading {file_path}: {e}")
        return None

def get_signal_strength_color(strength: float) -> str:
    """Get color based on signal strength"""
    if strength >= 0.7:
        return "🟢"
    elif strength >= 0.4:
        return "🟡"
    else:
        return "🔴"

def get_trend_emoji(trend: str) -> str:
    """Get emoji for trend direction"""
    if trend == "bullish":
        return "📈"
    elif trend == "bearish":
        return "📉"
    else:
        return "➡️"

def run_comprehensive_smc_analysis(df: pd.DataFrame, symbol: str, use_egx_params: bool = True) -> Dict:
    """Run complete SMC analysis with all indicators"""
    
    current_price = float(df['close'].iloc[-1])
    
    # Get EGX-optimized parameters
    if use_egx_params:
        egx_params = get_egx_parameters()
        st.info("🇪🇬 Using EGX-optimized parameters for better confluence detection")
    else:
        egx_params = None
    
    # === COMPREHENSIVE SMC ANALYSIS ===
    
    # 1. Market Structure Analysis
    market_structure = detect_market_structure(df)
    
    # 2. Order Blocks Analysis
    if egx_params:
        order_blocks = get_active_order_blocks(df, current_price, max_distance_pct=egx_params['distance_filters']['order_blocks_pct'])
        ob_confluence = analyze_order_block_confluence(order_blocks, current_price)
    else:
        order_blocks = get_active_order_blocks(df, current_price, max_distance_pct=5.0)
        ob_confluence = analyze_order_block_confluence(order_blocks, current_price)
    
    # 3. Fair Value Gaps Analysis
    if egx_params:
        fvgs = get_active_fvgs(df, current_price, max_distance_pct=egx_params['distance_filters']['fvg_pct'])
        fvg_confluence = analyze_fvg_confluence(fvgs, current_price)
    else:
        fvgs = get_active_fvgs(df, current_price, max_distance_pct=3.0)
        fvg_confluence = analyze_fvg_confluence(fvgs, current_price)
    
    # 4. Liquidity Zones Analysis
    if egx_params:
        liquidity_zones = get_active_liquidity_zones(df, current_price, max_distance_pct=egx_params['distance_filters']['liquidity_zones_pct'])
        liq_confluence = analyze_liquidity_confluence(liquidity_zones, current_price)
    else:
        liquidity_zones = get_active_liquidity_zones(df, current_price, max_distance_pct=8.0)
        liq_confluence = analyze_liquidity_confluence(liquidity_zones, current_price)
    
    # 5. Displacement Analysis
    displacements = get_recent_displacements(df, lookback_periods=30)
    disp_confluence = analyze_displacement_confluence(displacements, current_price)
    
    # 6. Equal Highs/Lows Analysis
    eqh_eql = find_equal_highs_lows(df)
    
    # 7. Candlestick Patterns
    patterns = detect_candlestick_patterns(df)
    
    # 8. Technical Indicators
    indicators = get_indicators(df)
    
    # Calculate overall confluence
    confluence_factors = [
        ob_confluence.get("confluence", False),
        fvg_confluence.get("confluence", False),
        liq_confluence.get("confluence", False),
        disp_confluence.get("confluence", False)
    ]
    
    active_factors = sum(confluence_factors)
    confluence_strength = active_factors / 4
    
    # Determine signal strength
    if confluence_strength >= 0.75:
        signal_strength = "VERY STRONG"
    elif confluence_strength >= 0.5:
        signal_strength = "STRONG"
    elif confluence_strength >= 0.25:
        signal_strength = "MODERATE"
    else:
        signal_strength = "WEAK"
    
    return {
        'symbol': symbol,
        'current_price': current_price,
        'market_structure': market_structure,
        'order_blocks': {
            'blocks': order_blocks,
            'confluence': ob_confluence,
            'count': len(order_blocks)
        },
        'fair_value_gaps': {
            'gaps': fvgs,
            'confluence': fvg_confluence,
            'count': len(fvgs)
        },
        'liquidity_zones': {
            'zones': liquidity_zones,
            'confluence': liq_confluence,
            'count': len(liquidity_zones)
        },
        'displacements': {
            'moves': displacements,
            'confluence': disp_confluence,
            'count': len(displacements)
        },
        'equal_highs_lows': eqh_eql,
        'candlestick_patterns': patterns,
        'technical_indicators': indicators,
        'confluence_summary': {
            'total_factors': active_factors,
            'max_factors': 4,
            'strength': confluence_strength,
            'signal_strength': signal_strength,
            'factors': confluence_factors
        }
    }

def main():
    """Main dashboard application"""

    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🇪🇬 EGX Professional SMC Dashboard</h1>
        <p>Advanced SMC Trading Platform with Professional TradingView Charts</p>
    </div>
    """, unsafe_allow_html=True)

    # TradingView Upgrade Notice
    st.markdown("""
    <div class="tradingview-upgrade">
        <h3>📈 Professional Chart Upgrade</h3>
        <p>Now featuring TradingView integration for institutional-grade charting experience!</p>
    </div>
    """, unsafe_allow_html=True)

    # Sidebar configuration
    with st.sidebar:
        st.header("🎛️ Dashboard Controls")

        # Chart type selection
        chart_type = st.radio(
            "📊 Chart Type",
            ["TradingView Professional", "Basic Analysis Only"],
            index=0
        )

        # Data source selection
        data_dir = st.text_input("📁 Data Directory", value="data/historical")

        if not os.path.exists(data_dir):
            st.error(f"❌ Directory {data_dir} not found")
            return

        # Get available CSV files (EGX stocks only)
        csv_files = [f for f in os.listdir(data_dir)
                    if f.endswith('.csv') and not any(crypto in f.upper()
                    for crypto in ['USDT', 'BTC', 'ETH', 'BNB', 'CRYPTO'])]

        if not csv_files:
            st.error("❌ No EGX stock CSV files found")
            return

        # Stock selection
        selected_file = st.selectbox("📈 Select EGX Stock", csv_files)
        symbol = selected_file.replace('.csv', '').upper()

        # Show mini TradingView widget in sidebar
        if chart_type == "TradingView Professional":
            st.markdown("### 📊 Live Price")
            tv_symbol = get_tradingview_symbol(symbol)
            mini_widget_html = create_mini_tradingview_widget(symbol)
            st.components.v1.html(mini_widget_html, height=240)

        # Analysis parameters
        st.subheader("⚙️ SMC Parameters")
        use_egx_params = st.checkbox("🇪🇬 Use EGX-Optimized Parameters", value=True)
        
        # Analysis trigger
        run_analysis = st.button("🚀 Run Professional SMC Analysis", type="primary")

    if run_analysis and selected_file:
        file_path = os.path.join(data_dir, selected_file)

        with st.spinner(f"🔍 Loading and analyzing {symbol}..."):
            df = load_csv_data(file_path)

            if df is None:
                return

            # Run comprehensive analysis
            analysis = run_comprehensive_smc_analysis(df, symbol, use_egx_params)

            # Display results in professional layout
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric(
                    "💰 Current Price",
                    f"{analysis['current_price']:.2f} EGP",
                    delta=f"{((analysis['current_price'] - df['close'].iloc[-2]) / df['close'].iloc[-2] * 100):.2f}%"
                )

            with col2:
                trend = analysis['market_structure']['trend']
                st.metric(
                    "📊 Market Trend",
                    f"{get_trend_emoji(trend)} {trend.upper()}",
                    delta=analysis['market_structure'].get('event', 'No Event')
                )

            with col3:
                confluence = analysis['confluence_summary']
                st.metric(
                    "⚡ Signal Strength",
                    f"{get_signal_strength_color(confluence['strength'])} {confluence['signal_strength']}",
                    delta=f"{confluence['total_factors']}/4 factors"
                )

            with col4:
                st.metric(
                    "📊 Data Points",
                    f"{len(df)}",
                    delta="Historical Candles"
                )

            # Professional Chart Section
            if chart_type == "TradingView Professional":
                render_tradingview_chart(symbol, analysis)
            else:
                st.info("📊 Basic analysis mode - TradingView chart disabled")

            # === COMPREHENSIVE SMC ANALYSIS SECTIONS ===
            
            # Row 1: Order Blocks & Fair Value Gaps
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("""
                <div class="smc-section">
                    <h3>🔲 Order Blocks Analysis</h3>
                </div>
                """, unsafe_allow_html=True)

                ob_data = analysis['order_blocks']
                confluence_status = "✅ STRONG" if ob_data['confluence'].get('confluence', False) else "❌ WEAK"
                st.markdown(f"**Active Order Blocks:** `{ob_data['count']}`")
                st.markdown(f"**Confluence Status:** {confluence_status}")

                if ob_data['blocks']:
                    for i, ob in enumerate(ob_data['blocks'][:3]):  # Show top 3
                        st.markdown(f"   📍 **Block {i+1}:** {ob.block_type} | Strength: {ob.strength:.2f}")

            with col2:
                st.markdown("""
                <div class="smc-section">
                    <h3>⚡ Fair Value Gaps</h3>
                </div>
                """, unsafe_allow_html=True)

                fvg_data = analysis['fair_value_gaps']
                confluence_status = "✅ STRONG" if fvg_data['confluence'].get('confluence', False) else "❌ WEAK"
                st.markdown(f"**Active FVGs:** `{fvg_data['count']}`")
                st.markdown(f"**Confluence Status:** {confluence_status}")

                if fvg_data['gaps']:
                    for i, fvg in enumerate(fvg_data['gaps'][:3]):  # Show top 3
                        st.markdown(f"   ⚡ **Gap {i+1}:** {fvg.gap_type} | Strength: {fvg.strength:.2f}")

            # Row 2: Liquidity Zones & Displacements
            col3, col4 = st.columns(2)

            with col3:
                st.markdown("""
                <div class="smc-section">
                    <h3>💧 Liquidity Zones</h3>
                </div>
                """, unsafe_allow_html=True)

                liq_data = analysis['liquidity_zones']
                confluence_status = "✅ STRONG" if liq_data['confluence'].get('confluence', False) else "❌ WEAK"
                st.markdown(f"**Active Zones:** `{liq_data['count']}`")
                st.markdown(f"**Confluence Status:** {confluence_status}")

                if liq_data['zones']:
                    for i, zone in enumerate(liq_data['zones'][:3]):  # Show top 3
                        zone_type = getattr(zone, 'zone_type', 'Unknown')
                        st.markdown(f"   💧 **Zone {i+1}:** {zone_type} | Strength: {zone.strength:.2f}")

            with col4:
                st.markdown("""
                <div class="smc-section">
                    <h3>🚀 Displacements</h3>
                </div>
                """, unsafe_allow_html=True)

                disp_data = analysis['displacements']
                confluence_status = "✅ STRONG" if disp_data['confluence'].get('confluence', False) else "❌ WEAK"
                st.markdown(f"**Recent Displacements:** `{disp_data['count']}`")
                st.markdown(f"**Confluence Status:** {confluence_status}")

                if disp_data['moves']:
                    for i, disp in enumerate(disp_data['moves'][:3]):  # Show top 3
                        disp_type = getattr(disp, 'displacement_type', 'Unknown')
                        st.markdown(f"   🚀 **Move {i+1}:** {disp_type} | Strength: {disp.strength:.2f}")

            # === TRADING SIGNALS SECTION ===
            st.markdown("""
            <div class="smc-section">
                <h2>🎯 Trading Signals & Recommendations</h2>
            </div>
            """, unsafe_allow_html=True)

            confluence = analysis['confluence_summary']

            col7, col8, col9 = st.columns(3)

            with col7:
                st.markdown("### 📊 Confluence Analysis")
                st.markdown(f"**Overall Strength:** {get_signal_strength_color(confluence['strength'])} `{confluence['signal_strength']}`")
                st.markdown(f"**Active Factors:** `{confluence['total_factors']}/4`")

                factor_names = ["Order Blocks", "Fair Value Gaps", "Liquidity Zones", "Displacements"]
                for i, (factor, active) in enumerate(zip(factor_names, confluence['factors'])):
                    status = "✅" if active else "❌"
                    st.markdown(f"   {status} {factor}")

            with col8:
                st.markdown("### 🎯 Trading Recommendation")

                # Determine trading bias
                if confluence['strength'] >= 0.75:
                    bias = "🟢 STRONG BUY/SELL"
                    recommendation = "High probability setup"
                elif confluence['strength'] >= 0.5:
                    bias = "🟡 MODERATE"
                    recommendation = "Wait for confirmation"
                elif confluence['strength'] >= 0.25:
                    bias = "🟠 WEAK"
                    recommendation = "Low probability setup"
                else:
                    bias = "🔴 NO SIGNAL"
                    recommendation = "Avoid trading"

                st.markdown(f"**Trading Bias:** {bias}")
                st.markdown(f"**Recommendation:** {recommendation}")

                # Market structure bias
                trend = analysis['market_structure']['trend']
                st.markdown(f"**Market Structure:** {get_trend_emoji(trend)} {trend.upper()}")

            with col9:
                st.markdown("### ⚠️ Risk Management")

                current_price = analysis['current_price']

                # Calculate nearest support/resistance
                all_levels = []

                # Add order block levels
                for ob in analysis['order_blocks']['blocks']:
                    all_levels.extend([ob.high, ob.low])

                # Add FVG levels
                for fvg in analysis['fair_value_gaps']['gaps']:
                    all_levels.extend([fvg.high, fvg.low])

                if all_levels:
                    # Find nearest support and resistance
                    resistance_levels = [level for level in all_levels if level > current_price]
                    support_levels = [level for level in all_levels if level < current_price]

                    nearest_resistance = min(resistance_levels) if resistance_levels else current_price * 1.02
                    nearest_support = max(support_levels) if support_levels else current_price * 0.98

                    st.markdown(f"**Nearest Resistance:** `{nearest_resistance:.2f} EGP`")
                    st.markdown(f"**Nearest Support:** `{nearest_support:.2f} EGP`")

                    # Risk/Reward calculation
                    risk = abs(current_price - nearest_support)
                    reward = abs(nearest_resistance - current_price)
                    rr_ratio = reward / risk if risk > 0 else 0

                    st.markdown(f"**Risk/Reward Ratio:** `{rr_ratio:.2f}:1`")
                else:
                    st.markdown("**No clear levels identified**")
                    st.markdown("**Use wider stops**")

    else:
        # Welcome screen
        st.markdown("""
        ## 🎯 Welcome to EGX Professional SMC Dashboard

        This advanced platform provides comprehensive Smart Money Concepts analysis for Egyptian Exchange stocks with professional TradingView charts.

        ### 🚀 New Features:
        - **📈 TradingView Integration** - Professional institutional-grade charts
        - **🔴 Real-time Data** - Live EGX stock prices and analysis
        - **📱 Mobile Responsive** - Works perfectly on all devices
        - **🎛️ Advanced Tools** - Drawing tools, alerts, multiple timeframes
        - **⚡ EGX-Optimized SMC** - Parameters tuned for Egyptian stocks

        ### 📋 Instructions:
        1. Select "TradingView Professional" for best experience
        2. Choose your EGX stock from the sidebar
        3. Enable EGX-optimized parameters
        4. Click "Run Professional SMC Analysis"
        5. Analyze with institutional-grade tools

        **Ready to trade like the institutions!** 🇪🇬📈💎
        """)

if __name__ == "__main__":
    main()
