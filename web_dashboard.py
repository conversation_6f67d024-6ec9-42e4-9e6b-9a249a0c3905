"""
EGX Trading Bot Web Dashboard
FastAPI-based web interface for monitoring and controlling the EGX trading bot
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Import EGX modules
from api.egx_async import (
    get_current_price, get_multiple_prices, check_bridge_status,
    get_common_egx_symbols
)
from config.egx_config import (
    DEFAULT_TRADING_SYMBOLS, get_market_status, EGX_SYMBOLS
)
from trade_executor_egx import (
    fetch_egx_balance, get_egx_open_positions, egx_portfolio
)

# FastAPI app
app = FastAPI(
    title="EGX Trading Bot Dashboard",
    description="Real-time monitoring and control for Egyptian stock trading",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket connections manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                pass

manager = ConnectionManager()

# API Routes

@app.get("/")
async def dashboard():
    """Serve the main dashboard HTML"""
    return HTMLResponse(content=get_dashboard_html(), status_code=200)

@app.get("/api/status")
async def get_bot_status():
    """Get overall bot status"""
    try:
        bridge_status = await check_bridge_status()
        market_status = get_market_status()
        balance_data = await fetch_egx_balance()
        
        return {
            "status": "running",
            "bridge_connected": bridge_status.get("connected", False),
            "market_open": market_status.get("is_open", False),
            "market_time": market_status.get("current_time", "Unknown"),
            "balance": {
                "total": balance_data[0] if balance_data else 0,
                "free": balance_data[1] if balance_data else 0,
                "used": balance_data[2] if balance_data else 0
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/prices")
async def get_current_prices():
    """Get current prices for all trading symbols"""
    try:
        prices = {}
        for symbol in DEFAULT_TRADING_SYMBOLS:
            try:
                price_data = await get_current_price(symbol)
                prices[symbol] = {
                    "price": price_data["price"],
                    "change": price_data.get("change", 0),
                    "changePercent": price_data.get("changePercent", 0),
                    "timestamp": datetime.now().isoformat()
                }
            except Exception as e:
                prices[symbol] = {
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
        
        return {"prices": prices}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/positions")
async def get_positions():
    """Get current trading positions"""
    try:
        positions = await get_egx_open_positions()
        return {"positions": positions}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/symbols")
async def get_symbols():
    """Get available EGX symbols"""
    try:
        symbols = await get_common_egx_symbols()
        return {"symbols": symbols}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/market")
async def get_market_info():
    """Get market information"""
    try:
        market_status = get_market_status()
        return {"market": market_status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Trading Control Endpoints

@app.post("/api/bot/start")
async def start_bot():
    """Start the trading bot"""
    try:
        # Create a flag file to signal bot to start
        with open("bot_start.flag", "w") as f:
            f.write("start")
        return {"status": "success", "message": "Bot start signal sent"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/bot/stop")
async def stop_bot():
    """Stop the trading bot"""
    try:
        # Create a flag file to signal bot to stop
        with open("bot_stop.flag", "w") as f:
            f.write("stop")
        return {"status": "success", "message": "Bot stop signal sent"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/bot/pause")
async def pause_bot():
    """Pause the trading bot"""
    try:
        # Create a pause flag
        with open("pause.flag", "w") as f:
            f.write("pause")
        return {"status": "success", "message": "Bot paused"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/bot/pause")
async def resume_bot():
    """Resume the trading bot"""
    try:
        # Remove pause flag
        if os.path.exists("pause.flag"):
            os.remove("pause.flag")
        return {"status": "success", "message": "Bot resumed"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/trade/buy/{symbol}")
async def manual_buy(symbol: str, shares: int = 100):
    """Manual buy order"""
    try:
        from trade_executor_egx import place_stock_order
        result = await place_stock_order(symbol, "buy", shares)
        if result:
            return {"status": "success", "message": f"Bought {shares} shares of {symbol}", "data": result}
        else:
            return {"status": "error", "message": "Order failed"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/trade/sell/{symbol}")
async def manual_sell(symbol: str, shares: int = 100):
    """Manual sell order"""
    try:
        from trade_executor_egx import place_stock_order
        result = await place_stock_order(symbol, "sell", shares)
        if result:
            return {"status": "success", "message": f"Sold {shares} shares of {symbol}", "data": result}
        else:
            return {"status": "error", "message": "Order failed"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/positions/close/{symbol}")
async def close_position(symbol: str):
    """Close entire position for a symbol"""
    try:
        from trade_executor_egx import close_stock_position
        result = await close_stock_position(symbol)
        if result:
            return {"status": "success", "message": f"Closed position for {symbol}"}
        else:
            return {"status": "error", "message": "Failed to close position"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket endpoint for real-time updates
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            # Send real-time updates every 5 seconds
            try:
                # Get current data
                bridge_status = await check_bridge_status()
                market_status = get_market_status()
                balance_data = await fetch_egx_balance()
                
                # Get prices for main symbols
                prices = {}
                for symbol in DEFAULT_TRADING_SYMBOLS[:3]:  # Limit to 3 for performance
                    try:
                        price_data = await get_current_price(symbol)
                        prices[symbol] = price_data["price"]
                    except:
                        prices[symbol] = "Error"
                
                # Send update
                update = {
                    "type": "update",
                    "data": {
                        "bridge_connected": bridge_status.get("connected", False),
                        "market_open": market_status.get("is_open", False),
                        "balance": balance_data[0] if balance_data else 0,
                        "prices": prices,
                        "timestamp": datetime.now().isoformat()
                    }
                }
                
                await websocket.send_text(json.dumps(update))
                
            except Exception as e:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": str(e)
                }))
            
            await asyncio.sleep(5)  # Update every 5 seconds
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)

def get_dashboard_html():
    """Generate the dashboard HTML"""
    return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇪🇬 EGX Trading Bot Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .controls-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-success { background: linear-gradient(135deg, #28a745, #20c997); color: white; }
        .btn-warning { background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; }
        .btn-info { background: linear-gradient(135deg, #17a2b8, #6f42c1); color: white; }
        .btn-danger { background: linear-gradient(135deg, #dc3545, #e83e8c); color: white; }
        .btn-buy { background: linear-gradient(135deg, #28a745, #20c997); color: white; }
        .btn-sell { background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; }
        .btn-close { background: linear-gradient(135deg, #6c757d, #495057); color: white; }

        .trading-controls {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }

        .status-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
        }

        .message {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            animation: slideIn 0.3s ease;
        }

        .message.success { background: linear-gradient(135deg, #28a745, #20c997); }
        .message.error { background: linear-gradient(135deg, #dc3545, #e83e8c); }
        .message.info { background: linear-gradient(135deg, #17a2b8, #6f42c1); }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        .card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #4CAF50; }
        .status-offline { background-color: #f44336; }
        .price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .price-item:last-child {
            border-bottom: none;
        }
        .price-value {
            font-weight: bold;
            color: #667eea;
        }
        .balance-amount {
            font-size: 1.5em;
            font-weight: bold;
            color: #4CAF50;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .timestamp {
            font-size: 0.8em;
            color: #999;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇪🇬 EGX Trading Bot Dashboard</h1>
            <p>Real-time monitoring of Egyptian stock trading</p>
        </div>
        
        <div class="status-grid">
            <div class="card">
                <h3>🔌 System Status</h3>
                <div id="system-status" class="loading">Loading...</div>
            </div>

            <div class="card">
                <h3>🏛️ Market Status</h3>
                <div id="market-status" class="loading">Loading...</div>
            </div>

            <div class="card">
                <h3>💰 Portfolio Balance</h3>
                <div id="balance-info" class="loading">Loading...</div>
            </div>

            <div class="card">
                <h3>📊 Live Prices</h3>
                <div id="price-feed" class="loading">Loading...</div>
            </div>
        </div>

        <!-- Trading Controls Section -->
        <div class="controls-section">
            <div class="card">
                <h3>🎛️ Bot Controls</h3>
                <div class="button-group">
                    <button class="btn btn-success" onclick="controlBot('start')">▶️ Start Bot</button>
                    <button class="btn btn-warning" onclick="controlBot('pause')">⏸️ Pause Bot</button>
                    <button class="btn btn-info" onclick="controlBot('resume')">▶️ Resume Bot</button>
                    <button class="btn btn-danger" onclick="controlBot('stop')">⏹️ Stop Bot</button>
                </div>
            </div>

            <div class="card">
                <h3>📈 Manual Trading</h3>
                <div class="trading-controls">
                    <select id="symbol-select" class="form-control">
                        <option value="COMI">COMI - Commercial International Bank</option>
                        <option value="ETEL">ETEL - Egyptian Mobile Services</option>
                        <option value="HELI">HELI - Heliopolis Housing</option>
                        <option value="EGAS">EGAS - Egyptian Natural Gas</option>
                        <option value="SWDY">SWDY - El Sewedy Electric</option>
                    </select>
                    <input type="number" id="shares-input" class="form-control" placeholder="Shares" value="100" min="1">
                    <div class="button-group">
                        <button class="btn btn-buy" onclick="manualTrade('buy')">📈 Buy</button>
                        <button class="btn btn-sell" onclick="manualTrade('sell')">📉 Sell</button>
                        <button class="btn btn-close" onclick="closePosition()">🚪 Close Position</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Messages -->
        <div id="status-messages" class="status-messages"></div>
    </div>

    <script>
        // WebSocket connection for real-time updates
        const ws = new WebSocket(`ws://${window.location.host}/ws`);
        
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            
            if (data.type === 'update') {
                updateDashboard(data.data);
            } else if (data.type === 'error') {
                console.error('WebSocket error:', data.message);
            }
        };
        
        ws.onopen = function() {
            console.log('WebSocket connected');
        };
        
        ws.onclose = function() {
            console.log('WebSocket disconnected');
            setTimeout(() => location.reload(), 5000); // Reload after 5 seconds
        };
        
        function updateDashboard(data) {
            // Update system status
            const systemStatus = document.getElementById('system-status');
            systemStatus.innerHTML = `
                <div>
                    <span class="status-indicator ${data.bridge_connected ? 'status-online' : 'status-offline'}"></span>
                    Bridge: ${data.bridge_connected ? 'Connected' : 'Disconnected'}
                </div>
                <div class="timestamp">Updated: ${new Date(data.timestamp).toLocaleTimeString()}</div>
            `;
            
            // Update market status
            const marketStatus = document.getElementById('market-status');
            marketStatus.innerHTML = `
                <div>
                    <span class="status-indicator ${data.market_open ? 'status-online' : 'status-offline'}"></span>
                    EGX: ${data.market_open ? 'OPEN' : 'CLOSED'}
                </div>
                <div class="timestamp">Cairo Time: ${new Date().toLocaleTimeString()}</div>
            `;
            
            // Update balance
            const balanceInfo = document.getElementById('balance-info');
            balanceInfo.innerHTML = `
                <div class="balance-amount">${data.balance.toLocaleString()} EGP</div>
                <div class="timestamp">Paper Trading Balance</div>
            `;
            
            // Update prices
            const priceFeed = document.getElementById('price-feed');
            let priceHtml = '';
            for (const [symbol, price] of Object.entries(data.prices)) {
                priceHtml += `
                    <div class="price-item">
                        <span>${symbol}</span>
                        <span class="price-value">${typeof price === 'number' ? price.toFixed(2) + ' EGP' : price}</span>
                    </div>
                `;
            }
            priceFeed.innerHTML = priceHtml;
        }

        // Trading Control Functions
        async function controlBot(action) {
            try {
                let response;
                if (action === 'resume') {
                    response = await fetch('/api/bot/pause', { method: 'DELETE' });
                } else {
                    response = await fetch(`/api/bot/${action}`, { method: 'POST' });
                }

                const result = await response.json();
                if (result.status === 'success') {
                    showMessage(result.message, 'success');
                } else {
                    showMessage(result.message || 'Operation failed', 'error');
                }
            } catch (error) {
                showMessage(`Error: ${error.message}`, 'error');
            }
        }

        async function manualTrade(action) {
            const symbol = document.getElementById('symbol-select').value;
            const shares = document.getElementById('shares-input').value;

            if (!symbol || !shares || shares <= 0) {
                showMessage('Please select a symbol and enter valid shares', 'error');
                return;
            }

            try {
                const response = await fetch(`/api/trade/${action}/${symbol}?shares=${shares}`, {
                    method: 'POST'
                });

                const result = await response.json();
                if (result.status === 'success') {
                    showMessage(result.message, 'success');
                } else {
                    showMessage(result.message || 'Trade failed', 'error');
                }
            } catch (error) {
                showMessage(`Error: ${error.message}`, 'error');
            }
        }

        async function closePosition() {
            const symbol = document.getElementById('symbol-select').value;

            if (!symbol) {
                showMessage('Please select a symbol', 'error');
                return;
            }

            try {
                const response = await fetch(`/api/positions/close/${symbol}`, {
                    method: 'POST'
                });

                const result = await response.json();
                if (result.status === 'success') {
                    showMessage(result.message, 'success');
                } else {
                    showMessage(result.message || 'Failed to close position', 'error');
                }
            } catch (error) {
                showMessage(`Error: ${error.message}`, 'error');
            }
        }

        function showMessage(message, type) {
            const container = document.getElementById('status-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;

            container.appendChild(messageDiv);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 5000);
        }

        // Initial load
        fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                // Initial update
                console.log('Initial status loaded:', data);
            })
            .catch(error => {
                console.error('Error loading initial status:', error);
            });
    </script>
</body>
</html>
    """

# Start the web server
if __name__ == "__main__":
    print("🌐 Starting EGX Trading Bot Web Dashboard...")
    print("📊 Dashboard will be available at: http://localhost:8000")
    print("🔗 WebSocket endpoint: ws://localhost:8000/ws")
    
    uvicorn.run(
        "web_dashboard:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
