"""
EGX Trading Bot Web Dashboard
FastAPI-based web interface for monitoring and controlling the EGX trading bot
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# Import EGX modules
import pandas as pd
from log_setup import logger
from api.egx_async import (
    get_current_price, get_multiple_prices, check_bridge_status,
    get_common_egx_symbols, get_ohlcv
)
from config.egx_config import (
    DEFAULT_TRADING_SYMBOLS, get_market_status, EGX_SYMBOLS
)
from trade_executor_egx import (
    fetch_egx_balance, get_egx_open_positions, egx_portfolio
)
from indicators.order_blocks import get_active_order_blocks, analyze_order_block_confluence
from indicators.fvg import get_active_fvgs, analyze_fvg_confluence
from indicators.liquidity_zones import get_active_liquidity_zones, analyze_liquidity_confluence
from indicators.displacement import get_recent_displacements, analyze_displacement_confluence
from indicators.eqh_eql import find_equal_highs_lows

# Import CSV data loader for testing SMC with historical data
from data_loader_csv import (
    get_ohlcv_from_csv, get_current_price_from_csv, get_available_csv_symbols
)

# FastAPI app
app = FastAPI(
    title="EGX Trading Bot Dashboard",
    description="Real-time monitoring and control for Egyptian stock trading",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket connections manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except:
                pass

manager = ConnectionManager()

# API Routes

@app.get("/")
async def dashboard():
    """Serve the main dashboard HTML"""
    return HTMLResponse(content=get_dashboard_html(), status_code=200)

@app.get("/api/status")
async def get_bot_status():
    """Get overall bot status"""
    try:
        bridge_status = await check_bridge_status()
        market_status = get_market_status()
        balance_data = await fetch_egx_balance()

        return {
            "status": "running",
            "bridge_connected": bridge_status.get("connected", False),
            "market_open": market_status.get("is_open", False),
            "market_time": market_status.get("current_time", "Unknown"),
            "balance": {
                "total": balance_data[0] if balance_data else 0,
                "free": balance_data[1] if balance_data else 0,
                "used": balance_data[2] if balance_data else 0
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/smc-analysis/{symbol}")
async def get_smc_analysis(symbol: str):
    """Get comprehensive SMC analysis for a symbol"""
    try:
        from api.egx_async import get_ohlcv
        from strategies.smc_strategy_egx import run_smc_strategy_egx

        # Get recent data
        df_raw = await get_ohlcv(symbol=symbol, interval="1h", limit=150)
        if not df_raw:
            raise HTTPException(status_code=404, detail=f"No data available for {symbol}")

        # Run SMC analysis
        signal = await run_smc_strategy_egx(symbol=symbol, capital=100000)

        if signal and "smc_analysis" in signal:
            return {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "signal": signal,
                "smc_analysis": signal["smc_analysis"]
            }
        else:
            return {
                "symbol": symbol,
                "timestamp": datetime.now().isoformat(),
                "signal": None,
                "smc_analysis": None,
                "message": "No SMC signal generated"
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/smc-dashboard")
async def get_smc_dashboard():
    """Get comprehensive SMC analysis for all configured symbols"""
    try:
        # Load symbols from configuration AND CSV files
        try:
            with open("config/pairs.json", "r") as f:
                config = json.load(f)
                config_symbols = config.get("pairs", DEFAULT_TRADING_SYMBOLS)
        except Exception:
            config_symbols = DEFAULT_TRADING_SYMBOLS

        # Get available CSV symbols (EGX stocks ONLY - no crypto)
        csv_symbols = await get_available_csv_symbols()

        # Filter to EGX stocks only (exclude crypto symbols)
        egx_symbols = []
        if csv_symbols:
            for symbol in csv_symbols:
                # Only include symbols that don't contain crypto patterns
                if not any(crypto_pattern in symbol.upper() for crypto_pattern in ['USDT', 'BTC', 'ETH', 'ADA', 'BNB', 'SOL', 'XRP', 'ATOM', 'EOS']):
                    egx_symbols.append(symbol)

        # Use EGX symbols if available, otherwise fall back to config
        if egx_symbols:
            symbols = egx_symbols[:5]  # Limit to first 5 EGX symbols for performance
            logger.info(f"🔍 Using EGX CSV data for SMC analysis: {symbols}")
        else:
            symbols = config_symbols
            logger.info(f"🔍 Using config symbols for SMC analysis: {symbols}")

        smc_data = {}

        for symbol in symbols:
            try:
                logger.info(f"📊 Analyzing {symbol}...")

                # Try CSV data first, then fall back to TradingView bridge
                df_raw = None
                current_price = None
                data_source = "unknown"

                # Try CSV data first
                if symbol in csv_symbols:
                    try:
                        df_raw = await get_ohlcv_from_csv(symbol, limit=150)
                        current_price_data = await get_current_price_from_csv(symbol)
                        if current_price_data:
                            current_price = current_price_data.get("price", 0)
                        data_source = "CSV"
                        logger.info(f"✅ {symbol}: Using CSV data")
                    except Exception as e:
                        logger.warning(f"⚠️ CSV data failed for {symbol}: {e}")

                # Fall back to TradingView bridge if CSV failed
                if not df_raw:
                    try:
                        df_raw = await get_ohlcv(symbol=symbol, interval="1h", limit=150)
                        current_price_data = await get_current_price(symbol)
                        if current_price_data:
                            current_price = current_price_data.get("price", 0) if isinstance(current_price_data, dict) else current_price_data
                        data_source = "TradingView"
                        logger.info(f"✅ {symbol}: Using TradingView data")
                    except Exception as e:
                        logger.warning(f"⚠️ TradingView data failed for {symbol}: {e}")

                if not df_raw:
                    logger.warning(f"⚠️ No data available for {symbol}")
                    continue

                df = pd.DataFrame(df_raw)
                if df.empty or len(df) < 50:
                    logger.warning(f"⚠️ Insufficient data for {symbol}: {len(df)} candles")
                    continue

                if not current_price:
                    logger.warning(f"⚠️ No current price for {symbol}")
                    continue

                logger.info(f"✅ {symbol}: {len(df)} candles, price: {current_price} (source: {data_source})")

                # Run SMC analysis directly (don't call strategy to avoid circular dependencies)
                logger.info(f"🔍 {symbol}: Running SMC analysis...")

                # Market Structure
                from indicators.market_structure import detect_market_structure
                ms = detect_market_structure(df)

                # Order Blocks
                order_blocks = get_active_order_blocks(df, current_price, max_distance_pct=5.0)
                ob_confluence = analyze_order_block_confluence(order_blocks, current_price)

                # Fair Value Gaps
                fvgs = get_active_fvgs(df, current_price, max_distance_pct=3.0)
                fvg_confluence = analyze_fvg_confluence(fvgs, current_price)

                # Liquidity Zones
                liquidity_zones = get_active_liquidity_zones(df, current_price, max_distance_pct=8.0)
                liq_confluence = analyze_liquidity_confluence(liquidity_zones, current_price)

                # Displacement
                displacements = get_recent_displacements(df, lookback_periods=30)
                disp_confluence = analyze_displacement_confluence(displacements, current_price)

                # Equal Highs/Lows
                eqh_eql = find_equal_highs_lows(df)

                # Calculate overall confluence
                confluence_factors = [
                    ob_confluence["confluence"],
                    fvg_confluence["confluence"],
                    liq_confluence["confluence"],
                    disp_confluence["confluence"]
                ]

                active_factors = sum(confluence_factors)
                confluence_strength = active_factors / 4

                # Convert all data to JSON-safe format
                def safe_convert(value):
                    """Convert numpy types to Python native types"""
                    import numpy as np
                    if isinstance(value, np.bool_):
                        return bool(value)
                    elif isinstance(value, np.integer):
                        return int(value)
                    elif isinstance(value, np.floating):
                        return float(value)
                    elif isinstance(value, np.ndarray):
                        return value.tolist()
                    elif hasattr(value, 'value'):  # Enum types
                        return str(value.value)
                    else:
                        return value

                smc_data[symbol] = {
                    "symbol": symbol,
                    "current_price": float(current_price),
                    "data_source": data_source,
                    "market_structure": {
                        "trend": str(ms.get("trend", "UNKNOWN")),
                        "strength": safe_convert(ms.get("strength", 0)),
                        "bos_count": safe_convert(ms.get("bos_count", 0)),
                        "choch_count": safe_convert(ms.get("choch_count", 0))
                    },
                    "order_blocks": {
                        "count": len(order_blocks),
                        "confluence": safe_convert(ob_confluence.get("confluence", False)),
                        "active_blocks": [
                            {
                                "type": str(ob.block_type),
                                "high": float(ob.high),
                                "low": float(ob.low),
                                "strength": float(ob.strength),
                                "tested": safe_convert(ob.tested)
                            } for ob in order_blocks[:3]
                        ]
                    },
                    "fair_value_gaps": {
                        "count": len(fvgs),
                        "confluence": safe_convert(fvg_confluence.get("confluence", False)),
                        "active_fvgs": [
                            {
                                "type": str(fvg.gap_type),
                                "high": float(fvg.high),
                                "low": float(fvg.low),
                                "strength": float(fvg.strength),
                                "filled": safe_convert(fvg.filled)
                            } for fvg in fvgs[:3]
                        ]
                    },
                    "liquidity_zones": {
                        "count": len(liquidity_zones),
                        "confluence": safe_convert(liq_confluence.get("confluence", False)),
                        "active_zones": [
                            {
                                "type": safe_convert(zone.zone_type),
                                "high": float(zone.high),
                                "low": float(zone.low),
                                "center": float(zone.center),
                                "strength": float(zone.strength),
                                "touches": int(zone.touches)
                            } for zone in liquidity_zones[:3]
                        ]
                    },
                    "displacements": {
                        "count": len(displacements),
                        "confluence": safe_convert(disp_confluence.get("confluence", False)),
                        "recent_moves": [
                            {
                                "type": safe_convert(disp.displacement_type),
                                "size_pct": float(disp.displacement_pct),
                                "strength": float(disp.strength),
                                "follow_through": safe_convert(disp.follow_through),
                                "candles": int(disp.candles_count)
                            } for disp in displacements[:3]
                        ]
                    },
                    "equal_highs_lows": {
                        "equal_highs": safe_convert(eqh_eql.get("equal_highs", [])),
                        "equal_lows": safe_convert(eqh_eql.get("equal_lows", []))
                    },
                    "confluence_summary": {
                        "total_factors": int(active_factors),
                        "confluence_strength": float(confluence_strength),
                        "aligned_factors": [
                            factor for factor, active in [
                                ("order_blocks", safe_convert(ob_confluence.get("confluence", False))),
                                ("fvgs", safe_convert(fvg_confluence.get("confluence", False))),
                                ("liquidity_zones", safe_convert(liq_confluence.get("confluence", False))),
                                ("displacements", safe_convert(disp_confluence.get("confluence", False)))
                            ] if active
                        ],
                        "signal_strength": "STRONG" if confluence_strength >= 0.75 else "MODERATE" if confluence_strength >= 0.5 else "WEAK"
                    }
                }
                logger.info(f"✅ {symbol} SMC analysis complete")

            except Exception as e:
                logger.error(f"❌ Error analyzing {symbol}: {e}")
                continue

        logger.info(f"✅ SMC analysis complete for {len(smc_data)} symbols")

        # Convert numpy types to Python native types for JSON serialization
        def convert_numpy_types(obj):
            """Recursively convert numpy types to Python native types"""
            import numpy as np

            if isinstance(obj, np.bool_):
                return bool(obj)
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            else:
                return obj

        # Convert the entire response
        clean_smc_data = convert_numpy_types(smc_data)

        return {"smc_analysis": clean_smc_data, "timestamp": datetime.now().isoformat()}

    except Exception as e:
        logger.error(f"❌ Error in SMC dashboard: {e}")
        return {"error": str(e), "smc_analysis": {}, "timestamp": datetime.now().isoformat()}

@app.get("/api/prices")
async def get_current_prices():
    """Get current prices for EGX symbols only"""
    try:
        prices = {}
        # Use EGX symbols only - no crypto
        egx_symbols = ["COMI", "ETEL", "HELI", "SWDY", "OCDI"]

        for symbol in egx_symbols:
            try:
                price_data = await get_current_price(symbol)
                prices[symbol] = {
                    "price": price_data["price"],
                    "change": price_data.get("change", 0),
                    "changePercent": price_data.get("changePercent", 0),
                    "timestamp": datetime.now().isoformat()
                }
            except Exception as e:
                prices[symbol] = {
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }

        return {"prices": prices}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/positions")
async def get_positions():
    """Get current trading positions"""
    try:
        positions = await get_egx_open_positions()
        return {"positions": positions}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/symbols")
async def get_symbols():
    """Get available EGX symbols"""
    try:
        symbols = await get_common_egx_symbols()
        return {"symbols": symbols}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/market")
async def get_market_info():
    """Get market information"""
    try:
        market_status = get_market_status()
        return {"market": market_status}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Trading Control Endpoints

@app.post("/api/bot/start")
async def start_bot():
    """Start the trading bot"""
    try:
        # Create a flag file to signal bot to start
        with open("bot_start.flag", "w") as f:
            f.write("start")
        return {"status": "success", "message": "Bot start signal sent"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/bot/stop")
async def stop_bot():
    """Stop the trading bot"""
    try:
        # Create a flag file to signal bot to stop
        with open("bot_stop.flag", "w") as f:
            f.write("stop")
        return {"status": "success", "message": "Bot stop signal sent"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/bot/pause")
async def pause_bot():
    """Pause the trading bot"""
    try:
        # Create a pause flag
        with open("pause.flag", "w") as f:
            f.write("pause")
        return {"status": "success", "message": "Bot paused"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/bot/pause")
async def resume_bot():
    """Resume the trading bot"""
    try:
        # Remove pause flag
        if os.path.exists("pause.flag"):
            os.remove("pause.flag")
        return {"status": "success", "message": "Bot resumed"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/trade/buy/{symbol}")
async def manual_buy(symbol: str, shares: int = 100):
    """Manual buy order"""
    try:
        from trade_executor_egx import place_stock_order
        result = await place_stock_order(symbol, "buy", shares)
        if result:
            return {"status": "success", "message": f"Bought {shares} shares of {symbol}", "data": result}
        else:
            return {"status": "error", "message": "Order failed"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/trade/sell/{symbol}")
async def manual_sell(symbol: str, shares: int = 100):
    """Manual sell order"""
    try:
        from trade_executor_egx import place_stock_order
        result = await place_stock_order(symbol, "sell", shares)
        if result:
            return {"status": "success", "message": f"Sold {shares} shares of {symbol}", "data": result}
        else:
            return {"status": "error", "message": "Order failed"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/positions/close/{symbol}")
async def close_position(symbol: str):
    """Close entire position for a symbol"""
    try:
        from trade_executor_egx import close_stock_position
        result = await close_stock_position(symbol)
        if result:
            return {"status": "success", "message": f"Closed position for {symbol}"}
        else:
            return {"status": "error", "message": "Failed to close position"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket endpoint for real-time updates
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            # Send real-time updates every 5 seconds
            try:
                # Get current data
                bridge_status = await check_bridge_status()
                market_status = get_market_status()
                balance_data = await fetch_egx_balance()
                
                # Get prices for EGX symbols only (no crypto)
                prices = {}
                egx_symbols = ["COMI", "ETEL", "HELI"]  # EGX stocks only
                for symbol in egx_symbols:
                    try:
                        price_data = await get_current_price(symbol)
                        prices[symbol] = price_data["price"]
                    except:
                        prices[symbol] = "Error"
                
                # Send update
                update = {
                    "type": "update",
                    "data": {
                        "bridge_connected": bridge_status.get("connected", False),
                        "market_open": market_status.get("is_open", False),
                        "balance": balance_data[0] if balance_data else 0,
                        "prices": prices,
                        "timestamp": datetime.now().isoformat()
                    }
                }
                
                await websocket.send_text(json.dumps(update))
                
            except Exception as e:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": str(e)
                }))
            
            await asyncio.sleep(5)  # Update every 5 seconds
            
    except WebSocketDisconnect:
        manager.disconnect(websocket)

def get_dashboard_html():
    """Generate the dashboard HTML"""
    return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🇪🇬 EGX Trading Bot Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container { 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 20px;
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .controls-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
            min-width: 120px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-success { background: linear-gradient(135deg, #28a745, #20c997); color: white; }
        .btn-warning { background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; }
        .btn-info { background: linear-gradient(135deg, #17a2b8, #6f42c1); color: white; }
        .btn-danger { background: linear-gradient(135deg, #dc3545, #e83e8c); color: white; }
        .btn-buy { background: linear-gradient(135deg, #28a745, #20c997); color: white; }
        .btn-sell { background: linear-gradient(135deg, #dc3545, #fd7e14); color: white; }
        .btn-close { background: linear-gradient(135deg, #6c757d, #495057); color: white; }

        .trading-controls {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-control {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        }

        .status-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
        }

        .message {
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            animation: slideIn 0.3s ease;
        }

        .message.success { background: linear-gradient(135deg, #28a745, #20c997); }
        .message.error { background: linear-gradient(135deg, #dc3545, #e83e8c); }
        .message.info { background: linear-gradient(135deg, #17a2b8, #6f42c1); }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        .card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h3 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #4CAF50; }
        .status-offline { background-color: #f44336; }
        .price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .price-item:last-child {
            border-bottom: none;
        }
        .price-value {
            font-weight: bold;
            color: #667eea;
        }
        .balance-amount {
            font-size: 1.5em;
            font-weight: bold;
            color: #4CAF50;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .timestamp {
            font-size: 0.8em;
            color: #999;
            margin-top: 10px;
        }

        /* SMC Analysis Styles */
        .smc-section {
            margin: 30px 0;
        }

        .smc-section h2 {
            color: white;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .smc-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .smc-card {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .smc-card h3 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .smc-indicator {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
            color: #333;
        }

        .smc-indicator:last-child {
            border-bottom: none;
        }

        .smc-value {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .smc-value.bullish {
            background: rgba(76, 175, 80, 0.8);
        }

        .smc-value.bearish {
            background: rgba(244, 67, 54, 0.8);
        }

        .smc-value.neutral {
            background: rgba(158, 158, 158, 0.8);
        }

        .confluence-meter {
            width: 100%;
            height: 20px;
            background: rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .confluence-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff4444, #ffaa00, #44ff44);
            transition: width 0.3s ease;
        }

        /* Signals Section */
        .signals-section {
            margin: 30px 0;
        }

        .signals-section h2 {
            color: white;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .signals-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .signal-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }

        .signal-card.bullish {
            border-left-color: #4CAF50;
        }

        .signal-card.bearish {
            border-left-color: #f44336;
        }

        .signal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .signal-symbol {
            font-size: 1.3em;
            font-weight: bold;
            color: #667eea;
        }

        .signal-direction {
            padding: 5px 10px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            text-transform: uppercase;
        }

        .signal-direction.long {
            background: #4CAF50;
        }

        .signal-direction.short {
            background: #f44336;
        }

        .signal-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 15px 0;
        }

        .signal-detail {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
        }

        .signal-reasons {
            margin-top: 15px;
        }

        .signal-reasons h4 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .reason-tag {
            display: inline-block;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🇪🇬 EGX Trading Bot Dashboard</h1>
            <p>Real-time monitoring of Egyptian stock trading</p>
        </div>
        
        <div class="status-grid">
            <div class="card">
                <h3>🔌 System Status</h3>
                <div id="system-status" class="loading">Loading...</div>
            </div>

            <div class="card">
                <h3>🏛️ Market Status</h3>
                <div id="market-status" class="loading">Loading...</div>
            </div>

            <div class="card">
                <h3>💰 Portfolio Balance</h3>
                <div id="balance-info" class="loading">Loading...</div>
            </div>

            <div class="card">
                <h3>📊 Live Prices</h3>
                <div id="price-feed" class="loading">Loading...</div>
            </div>
        </div>

        <!-- SMC Analysis Section -->
        <div class="smc-section">
            <h2>🧠 Smart Money Concepts Analysis</h2>
            <div class="smc-grid">
                <div class="card smc-card">
                    <h3>📈 Market Structure</h3>
                    <div id="market-structure" class="loading">Loading...</div>
                </div>

                <div class="card smc-card">
                    <h3>🔲 Order Blocks</h3>
                    <div id="order-blocks" class="loading">Loading...</div>
                </div>

                <div class="card smc-card">
                    <h3>⚡ Fair Value Gaps</h3>
                    <div id="fvg-analysis" class="loading">Loading...</div>
                </div>

                <div class="card smc-card">
                    <h3>💧 Liquidity Zones</h3>
                    <div id="liquidity-zones" class="loading">Loading...</div>
                </div>

                <div class="card smc-card">
                    <h3>🚀 Displacement</h3>
                    <div id="displacement-analysis" class="loading">Loading...</div>
                </div>

                <div class="card smc-card">
                    <h3>🎯 SMC Confluence</h3>
                    <div id="smc-confluence" class="loading">Loading...</div>
                </div>
            </div>
        </div>

        <!-- SMC Signals Section -->
        <div class="signals-section">
            <h2>📡 Active SMC Signals</h2>
            <div class="signals-container">
                <div id="active-signals" class="loading">Loading signals...</div>
            </div>
        </div>

        <!-- Trading Controls Section -->
        <div class="controls-section">
            <div class="card">
                <h3>🎛️ Bot Controls</h3>
                <div class="button-group">
                    <button class="btn btn-success" onclick="controlBot('start')">▶️ Start Bot</button>
                    <button class="btn btn-warning" onclick="controlBot('pause')">⏸️ Pause Bot</button>
                    <button class="btn btn-info" onclick="controlBot('resume')">▶️ Resume Bot</button>
                    <button class="btn btn-danger" onclick="controlBot('stop')">⏹️ Stop Bot</button>
                </div>
            </div>

            <div class="card">
                <h3>📈 Manual Trading</h3>
                <div class="trading-controls">
                    <select id="symbol-select" class="form-control">
                        <option value="COMI">COMI - Commercial International Bank</option>
                        <option value="ETEL">ETEL - Egyptian Mobile Services</option>
                        <option value="HELI">HELI - Heliopolis Housing</option>
                        <option value="EGAS">EGAS - Egyptian Natural Gas</option>
                        <option value="SWDY">SWDY - El Sewedy Electric</option>
                    </select>
                    <input type="number" id="shares-input" class="form-control" placeholder="Shares" value="100" min="1">
                    <div class="button-group">
                        <button class="btn btn-buy" onclick="manualTrade('buy')">📈 Buy</button>
                        <button class="btn btn-sell" onclick="manualTrade('sell')">📉 Sell</button>
                        <button class="btn btn-close" onclick="closePosition()">🚪 Close Position</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Messages -->
        <div id="status-messages" class="status-messages"></div>
    </div>

    <script>
        // WebSocket connection for real-time updates
        const ws = new WebSocket(`ws://${window.location.host}/ws`);
        
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            
            if (data.type === 'update') {
                updateDashboard(data.data);
            } else if (data.type === 'error') {
                console.error('WebSocket error:', data.message);
            }
        };
        
        ws.onopen = function() {
            console.log('WebSocket connected');
        };
        
        ws.onclose = function() {
            console.log('WebSocket disconnected');
            setTimeout(() => location.reload(), 5000); // Reload after 5 seconds
        };
        
        function updateDashboard(data) {
            // Update system status
            const systemStatus = document.getElementById('system-status');
            systemStatus.innerHTML = `
                <div>
                    <span class="status-indicator ${data.bridge_connected ? 'status-online' : 'status-offline'}"></span>
                    Bridge: ${data.bridge_connected ? 'Connected' : 'Disconnected'}
                </div>
                <div class="timestamp">Updated: ${new Date(data.timestamp).toLocaleTimeString()}</div>
            `;
            
            // Update market status
            const marketStatus = document.getElementById('market-status');
            marketStatus.innerHTML = `
                <div>
                    <span class="status-indicator ${data.market_open ? 'status-online' : 'status-offline'}"></span>
                    EGX: ${data.market_open ? 'OPEN' : 'CLOSED'}
                </div>
                <div class="timestamp">Cairo Time: ${new Date().toLocaleTimeString()}</div>
            `;
            
            // Update balance
            const balanceInfo = document.getElementById('balance-info');
            balanceInfo.innerHTML = `
                <div class="balance-amount">${data.balance.toLocaleString()} EGP</div>
                <div class="timestamp">Paper Trading Balance</div>
            `;
            
            // Update prices
            const priceFeed = document.getElementById('price-feed');
            let priceHtml = '';
            for (const [symbol, price] of Object.entries(data.prices)) {
                priceHtml += `
                    <div class="price-item">
                        <span>${symbol}</span>
                        <span class="price-value">${typeof price === 'number' ? price.toFixed(2) + ' EGP' : price}</span>
                    </div>
                `;
            }
            priceFeed.innerHTML = priceHtml;
        }

        // Trading Control Functions
        async function controlBot(action) {
            try {
                let response;
                if (action === 'resume') {
                    response = await fetch('/api/bot/pause', { method: 'DELETE' });
                } else {
                    response = await fetch(`/api/bot/${action}`, { method: 'POST' });
                }

                const result = await response.json();
                if (result.status === 'success') {
                    showMessage(result.message, 'success');
                } else {
                    showMessage(result.message || 'Operation failed', 'error');
                }
            } catch (error) {
                showMessage(`Error: ${error.message}`, 'error');
            }
        }

        async function manualTrade(action) {
            const symbol = document.getElementById('symbol-select').value;
            const shares = document.getElementById('shares-input').value;

            if (!symbol || !shares || shares <= 0) {
                showMessage('Please select a symbol and enter valid shares', 'error');
                return;
            }

            try {
                const response = await fetch(`/api/trade/${action}/${symbol}?shares=${shares}`, {
                    method: 'POST'
                });

                const result = await response.json();
                if (result.status === 'success') {
                    showMessage(result.message, 'success');
                } else {
                    showMessage(result.message || 'Trade failed', 'error');
                }
            } catch (error) {
                showMessage(`Error: ${error.message}`, 'error');
            }
        }

        async function closePosition() {
            const symbol = document.getElementById('symbol-select').value;

            if (!symbol) {
                showMessage('Please select a symbol', 'error');
                return;
            }

            try {
                const response = await fetch(`/api/positions/close/${symbol}`, {
                    method: 'POST'
                });

                const result = await response.json();
                if (result.status === 'success') {
                    showMessage(result.message, 'success');
                } else {
                    showMessage(result.message || 'Failed to close position', 'error');
                }
            } catch (error) {
                showMessage(`Error: ${error.message}`, 'error');
            }
        }

        function showMessage(message, type) {
            const container = document.getElementById('status-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;

            container.appendChild(messageDiv);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 5000);
        }

        // Load SMC Dashboard Data
        function loadSMCDashboard() {
            fetch('/api/smc-dashboard')
                .then(response => response.json())
                .then(data => {
                    updateSMCDashboard(data);
                })
                .catch(error => {
                    console.error('Error loading SMC dashboard:', error);
                });
        }

        function updateSMCDashboard(data) {
            // Update Market Structure
            updateMarketStructure(data);

            // Update Order Blocks
            updateOrderBlocks(data);

            // Update FVG Analysis
            updateFVGAnalysis(data);

            // Update Liquidity Zones
            updateLiquidityZones(data);

            // Update Displacement Analysis
            updateDisplacementAnalysis(data);

            // Update SMC Confluence
            updateSMCConfluence(data);

            // Update Active Signals
            updateActiveSignals(data);
        }

        function updateMarketStructure(data) {
            const container = document.getElementById('market-structure');
            let html = '';

            for (const [symbol, analysis] of Object.entries(data.symbols)) {
                if (analysis.signal && analysis.signal.market_structure) {
                    const ms = analysis.signal.market_structure;
                    html += `
                        <div class="smc-indicator">
                            <span>${symbol}</span>
                            <span class="smc-value ${ms.trend}">${ms.trend.toUpperCase()}</span>
                        </div>
                        <div class="smc-indicator">
                            <span>Event</span>
                            <span class="smc-value">${ms.event || 'None'}</span>
                        </div>
                    `;
                }
            }

            container.innerHTML = html || '<div class="loading">No market structure data</div>';
        }

        function updateOrderBlocks(data) {
            const container = document.getElementById('order-blocks');
            let html = '';

            for (const [symbol, analysis] of Object.entries(data.symbols)) {
                if (analysis.smc_analysis && analysis.smc_analysis.order_blocks) {
                    const ob = analysis.smc_analysis.order_blocks;
                    html += `
                        <div class="smc-indicator">
                            <span>${symbol} Blocks</span>
                            <span class="smc-value">${ob.count}</span>
                        </div>
                        <div class="smc-indicator">
                            <span>Confluence</span>
                            <span class="smc-value ${ob.confluence.direction}">${ob.confluence.direction}</span>
                        </div>
                    `;
                }
            }

            container.innerHTML = html || '<div class="loading">No order blocks data</div>';
        }

        function updateFVGAnalysis(data) {
            const container = document.getElementById('fvg-analysis');
            let html = '';

            for (const [symbol, analysis] of Object.entries(data.symbols)) {
                if (analysis.smc_analysis && analysis.smc_analysis.fair_value_gaps) {
                    const fvg = analysis.smc_analysis.fair_value_gaps;
                    html += `
                        <div class="smc-indicator">
                            <span>${symbol} FVGs</span>
                            <span class="smc-value">${fvg.count}</span>
                        </div>
                        <div class="smc-indicator">
                            <span>Direction</span>
                            <span class="smc-value ${fvg.confluence.direction}">${fvg.confluence.direction}</span>
                        </div>
                    `;
                }
            }

            container.innerHTML = html || '<div class="loading">No FVG data</div>';
        }

        function updateLiquidityZones(data) {
            const container = document.getElementById('liquidity-zones');
            let html = '';

            for (const [symbol, analysis] of Object.entries(data.symbols)) {
                if (analysis.smc_analysis && analysis.smc_analysis.liquidity_zones) {
                    const liq = analysis.smc_analysis.liquidity_zones;
                    html += `
                        <div class="smc-indicator">
                            <span>${symbol} Zones</span>
                            <span class="smc-value">${liq.count}</span>
                        </div>
                        <div class="smc-indicator">
                            <span>Confluence</span>
                            <span class="smc-value ${liq.confluence.direction}">${liq.confluence.direction}</span>
                        </div>
                    `;
                }
            }

            container.innerHTML = html || '<div class="loading">No liquidity zones data</div>';
        }

        function updateDisplacementAnalysis(data) {
            const container = document.getElementById('displacement-analysis');
            let html = '';

            for (const [symbol, analysis] of Object.entries(data.symbols)) {
                if (analysis.smc_analysis && analysis.smc_analysis.displacements) {
                    const disp = analysis.smc_analysis.displacements;
                    html += `
                        <div class="smc-indicator">
                            <span>${symbol} Moves</span>
                            <span class="smc-value">${disp.count}</span>
                        </div>
                        <div class="smc-indicator">
                            <span>Direction</span>
                            <span class="smc-value ${disp.confluence.direction}">${disp.confluence.direction}</span>
                        </div>
                    `;
                }
            }

            container.innerHTML = html || '<div class="loading">No displacement data</div>';
        }

        function updateSMCConfluence(data) {
            const container = document.getElementById('smc-confluence');
            let html = '';

            for (const [symbol, analysis] of Object.entries(data.symbols)) {
                if (analysis.smc_analysis && analysis.smc_analysis.confluence_summary) {
                    const conf = analysis.smc_analysis.confluence_summary;
                    const strengthPct = (conf.confluence_strength * 100).toFixed(0);

                    html += `
                        <div class="smc-indicator">
                            <span>${symbol}</span>
                            <span class="smc-value">${conf.total_factors}/4 factors</span>
                        </div>
                        <div class="confluence-meter">
                            <div class="confluence-fill" style="width: ${strengthPct}%"></div>
                        </div>
                        <div style="text-align: center; font-size: 0.9em; margin-top: 5px;">
                            ${strengthPct}% Confluence
                        </div>
                    `;
                }
            }

            container.innerHTML = html || '<div class="loading">No confluence data</div>';
        }

        function updateActiveSignals(data) {
            const container = document.getElementById('active-signals');
            let html = '';

            for (const [symbol, analysis] of Object.entries(data.symbols)) {
                if (analysis.signal && analysis.signal.direction !== 'neutral') {
                    const signal = analysis.signal;
                    const direction = signal.direction === 'long' ? 'bullish' : 'bearish';

                    html += `
                        <div class="signal-card ${direction}">
                            <div class="signal-header">
                                <div class="signal-symbol">${symbol}</div>
                                <div class="signal-direction ${signal.direction}">${signal.direction}</div>
                            </div>

                            <div class="signal-details">
                                <div class="signal-detail">
                                    <span>Entry:</span>
                                    <span>${signal.entry_price.toFixed(2)} EGP</span>
                                </div>
                                <div class="signal-detail">
                                    <span>Stop Loss:</span>
                                    <span>${signal.stop_loss.toFixed(2)} EGP</span>
                                </div>
                                <div class="signal-detail">
                                    <span>Take Profit:</span>
                                    <span>${signal.take_profit.toFixed(2)} EGP</span>
                                </div>
                                <div class="signal-detail">
                                    <span>Confidence:</span>
                                    <span>${(signal.normalized_confidence * 100).toFixed(0)}%</span>
                                </div>
                            </div>

                            <div class="signal-reasons">
                                <h4>SMC Reasons:</h4>
                                ${signal.reasons.map(reason => `<span class="reason-tag">${reason}</span>`).join('')}
                            </div>
                        </div>
                    `;
                }
            }

            if (!html) {
                html = '<div class="loading">No active signals</div>';
            }

            container.innerHTML = html;
        }

        // Initial load
        fetch('/api/status')
            .then(response => response.json())
            .then(data => {
                console.log('Initial status loaded:', data);
                // Load SMC dashboard after initial status
                loadSMCDashboard();
            })
            .catch(error => {
                console.error('Error loading initial status:', error);
            });

        // Refresh SMC data every 30 seconds
        setInterval(loadSMCDashboard, 30000);
    </script>
</body>
</html>
    """

# Start the web server
if __name__ == "__main__":
    print("🌐 Starting EGX Trading Bot Web Dashboard...")
    print("📊 Dashboard will be available at: http://localhost:8001")
    print("🔗 WebSocket endpoint: ws://localhost:8001/ws")

    uvicorn.run(
        "web_dashboard:app",
        host="0.0.0.0",
        port=8001,
        reload=False,
        log_level="info"
    )
