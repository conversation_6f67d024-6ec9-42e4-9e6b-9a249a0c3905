import os
import nest_asyncio
from telegram import Bot<PERSON>ommand, Update
from telegram.ext import (
    ApplicationBuilder,
    CallbackQueryHandler,
    CommandHandler,
    ContextTypes,
)

from config.telegram_config import TELEGRAM_CHAT_ID, TELEGRAM_TOKEN
from config.language import COMMANDS, MENU
from log_setup import logger
from telegram_bot.commands import (
    cmd_balance,
    cmd_heartbeat,
    cmd_positions,
    cmd_reentry,
    cmd_restart_bot,
    cmd_risk,
    cmd_start_strategies,
    cmd_status,
    cmd_stop_strategies,
    cmd_version,
    cmd_pause_trading,
    cmd_resume_trading,
    cmd_halt,
    cmd_unhalt,
)
from telegram_bot.commands_optimize import cmd_optimize_confidence
from telegram_bot.debug_commands import (
    debug_memory,
    debug_signals,
    debug_status,
    debug_threads,
    debug_weights,
    cmd_last_signal,
    cmd_equity_plot,
    cmd_drawdown_plot,
    cmd_equity_balance,
    cmd_pnl_summary,
)
from telegram_bot.menu import build_main_menu
from telegram_bot.menu_handler import handle_button

nest_asyncio.apply()

async def start_menu(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    if update.message:
        await update.message.reply_text(MENU["main_menu"], reply_markup=build_main_menu())


async def run_telegram_async() -> None:
    if TELEGRAM_TOKEN is None:
        raise ValueError("❌ TELEGRAM_TOKEN not set in environment variables.")

    app = ApplicationBuilder().token(TELEGRAM_TOKEN).build()

    await app.bot.set_my_commands([
        BotCommand("start", COMMANDS["start"]),
        BotCommand("start_strategies", COMMANDS["start_strategies"]),
        BotCommand("stop_strategies", COMMANDS["stop_strategies"]),
        BotCommand("pause_trading", COMMANDS["pause_trading"]),
        BotCommand("resume_trading", COMMANDS["resume_trading"]),
        BotCommand("halt", COMMANDS["halt"]),
        BotCommand("unhalt", COMMANDS["unhalt"]),
        BotCommand("status", COMMANDS["status"]),
        BotCommand("positions", COMMANDS["positions"]),
        BotCommand("balance", COMMANDS["balance"]),
        BotCommand("restart_bot", COMMANDS["restart_bot"]),
        BotCommand("risk", COMMANDS["risk"]),
        BotCommand("reentry", COMMANDS["reentry"]),
        BotCommand("version", COMMANDS["version"]),
        BotCommand("optimize_confidence", COMMANDS["optimize_confidence"]),
        BotCommand("heartbeat", COMMANDS["heartbeat"]),
        BotCommand("debug_status", COMMANDS["debug_status"]),
        BotCommand("debug_threads", COMMANDS["debug_threads"]),
        BotCommand("debug_memory", COMMANDS["debug_memory"]),
        BotCommand("debug_weights", COMMANDS["debug_weights"]),
        BotCommand("debug_signals", COMMANDS["debug_signals"]),
        BotCommand("last_signal", COMMANDS["last_signal"]),
        BotCommand("equity_plot", COMMANDS["equity_plot"]),
        BotCommand("drawdown_plot", COMMANDS["drawdown_plot"]),
        BotCommand("equity_balance", COMMANDS["equity_balance"]),
        BotCommand("pnl_summary", COMMANDS["pnl_summary"]),
    ])

    app.add_handler(CommandHandler("start", start_menu))
    app.add_handler(CommandHandler("start_strategies", cmd_start_strategies))
    app.add_handler(CommandHandler("stop_strategies", cmd_stop_strategies))
    app.add_handler(CommandHandler("pause_trading", cmd_pause_trading))
    app.add_handler(CommandHandler("resume_trading", cmd_resume_trading))
    app.add_handler(CommandHandler("halt", cmd_halt))
    app.add_handler(CommandHandler("unhalt", cmd_unhalt))
    app.add_handler(CommandHandler("status", cmd_status))
    app.add_handler(CommandHandler("positions", cmd_positions))
    app.add_handler(CommandHandler("balance", cmd_balance))
    app.add_handler(CommandHandler("restart_bot", cmd_restart_bot))
    app.add_handler(CommandHandler("risk", cmd_risk))
    app.add_handler(CommandHandler("reentry", cmd_reentry))
    app.add_handler(CommandHandler("version", cmd_version))
    app.add_handler(CommandHandler("optimize_confidence", cmd_optimize_confidence))
    app.add_handler(CommandHandler("heartbeat", cmd_heartbeat))
    app.add_handler(CommandHandler("debug_status", debug_status))
    app.add_handler(CommandHandler("debug_threads", debug_threads))
    app.add_handler(CommandHandler("debug_memory", debug_memory))
    app.add_handler(CommandHandler("debug_weights", debug_weights))
    app.add_handler(CommandHandler("debug_signals", debug_signals))
    app.add_handler(CommandHandler("last_signal", cmd_last_signal))
    app.add_handler(CommandHandler("equity_plot", cmd_equity_plot))
    app.add_handler(CommandHandler("drawdown_plot", cmd_drawdown_plot))
    app.add_handler(CommandHandler("equity_balance", cmd_equity_balance))
    app.add_handler(CommandHandler("pnl_summary", cmd_pnl_summary))
    app.add_handler(CallbackQueryHandler(handle_button))

    logger.info("Telegram bot started!")

    try:
        if TELEGRAM_CHAT_ID:
            await app.bot.send_message(
                chat_id=TELEGRAM_CHAT_ID,
                text="Bot successfully started! Press /start to control."
            )
        else:
            logger.warning("⚠️ TELEGRAM_CHAT_ID not set. Startup message not sent.")
    except Exception as e:
        logger.warning(f"[Telegram] Error sending startup message: {e}")

    app.run_polling()


async def start_telegram() -> None:
    try:
        await run_telegram_async()
    except Exception:
        logger.exception("Error starting Telegram bot:")


if __name__ == "__main__":
    import asyncio
    asyncio.run(start_telegram())
