"""
Advanced Displacement Detection for SMC Strategy
Identifies strong institutional moves that indicate smart money activity
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class DisplacementType(Enum):
    """Types of displacement moves"""
    BULLISH = "bullish"
    BEARISH = "bearish"
    CONTINUATION = "continuation"
    REVERSAL = "reversal"

@dataclass
class Displacement:
    """Represents a displacement move"""
    start_idx: int
    end_idx: int
    start_price: float
    end_price: float
    displacement_size: float
    displacement_pct: float
    displacement_type: DisplacementType
    strength: float  # 0-1 strength score
    volume_surge: float
    candles_count: int
    avg_candle_size: float
    follow_through: bool = False
    retracement_level: float = 0.0
    confluence_factors: List[str] = None

    def __post_init__(self):
        if self.confluence_factors is None:
            self.confluence_factors = []

def detect_displacement(df: pd.DataFrame, min_displacement_pct: float = 0.5, min_strength: float = 0.3) -> List[Displacement]:
    """
    Detect displacement moves in price data
    
    Args:
        df: OHLCV DataFrame
        min_displacement_pct: Minimum displacement percentage
        min_strength: Minimum strength threshold
    
    Returns:
        List of Displacement objects
    """
    if len(df) < 20:
        return []
    
    displacements = []
    
    # Detect different types of displacement
    displacements.extend(_detect_single_candle_displacement(df, min_displacement_pct))
    displacements.extend(_detect_multi_candle_displacement(df, min_displacement_pct))
    displacements.extend(_detect_volume_displacement(df, min_displacement_pct))
    displacements.extend(_detect_gap_displacement(df, min_displacement_pct))
    
    # Filter by strength
    displacements = [d for d in displacements if d.strength >= min_strength]
    
    # Remove overlapping displacements
    displacements = _remove_overlapping_displacements(displacements)
    
    # Update displacement status
    displacements = _update_displacement_status(df, displacements)
    
    # Sort by strength and recency
    displacements.sort(key=lambda x: (x.strength, -x.end_idx), reverse=True)
    
    return displacements[:15]  # Return top 15 displacements

def _detect_single_candle_displacement(df: pd.DataFrame, min_displacement_pct: float) -> List[Displacement]:
    """Detect single candle displacements (strong individual candles)"""
    displacements = []
    
    for i in range(10, len(df)):
        candle = df.iloc[i]
        
        # Calculate candle metrics
        candle_size = abs(candle['close'] - candle['open'])
        candle_size_pct = candle_size / candle['open'] * 100
        
        # Compare with recent average
        recent_candles = df.iloc[i-10:i]
        avg_candle_size = recent_candles.apply(lambda x: abs(x['close'] - x['open']), axis=1).mean()
        avg_volume = recent_candles['volume'].mean()
        
        # Check for displacement criteria
        if (candle_size_pct >= min_displacement_pct and 
            candle_size > avg_candle_size * 2 and  # At least 2x average size
            candle['volume'] > avg_volume * 1.5):   # Above average volume
            
            # Determine direction
            displacement_type = DisplacementType.BULLISH if candle['close'] > candle['open'] else DisplacementType.BEARISH
            
            # Calculate strength
            size_strength = min(candle_size / avg_candle_size / 5, 1.0)  # Normalize to 0-1
            volume_strength = min(candle['volume'] / avg_volume / 3, 1.0)  # Normalize to 0-1
            pct_strength = min(candle_size_pct / 5, 1.0)  # Normalize to 0-1
            
            strength = (size_strength + volume_strength + pct_strength) / 3
            
            # Check for wicks (less wick = stronger displacement)
            if displacement_type == DisplacementType.BULLISH:
                upper_wick = candle['high'] - candle['close']
                wick_ratio = upper_wick / candle_size if candle_size > 0 else 1
            else:
                lower_wick = candle['close'] - candle['low']
                wick_ratio = lower_wick / candle_size if candle_size > 0 else 1
            
            # Adjust strength based on wick (less wick = stronger)
            strength *= (1 - min(wick_ratio, 0.5))
            
            displacement = Displacement(
                start_idx=i,
                end_idx=i,
                start_price=candle['open'],
                end_price=candle['close'],
                displacement_size=candle_size,
                displacement_pct=candle_size_pct,
                displacement_type=displacement_type,
                strength=strength,
                volume_surge=candle['volume'] / avg_volume,
                candles_count=1,
                avg_candle_size=candle_size,
                confluence_factors=["single_candle", f"volume_surge_{candle['volume']/avg_volume:.1f}x"]
            )
            displacements.append(displacement)
    
    return displacements

def _detect_multi_candle_displacement(df: pd.DataFrame, min_displacement_pct: float) -> List[Displacement]:
    """Detect multi-candle displacement sequences"""
    displacements = []
    
    for i in range(15, len(df) - 5):
        # Look for sequences of 2-5 candles in same direction
        for sequence_length in range(2, 6):
            if i + sequence_length > len(df):
                continue
                
            sequence = df.iloc[i:i+sequence_length]
            
            # Check if all candles are in same direction
            bullish_candles = sum(1 for _, candle in sequence.iterrows() if candle['close'] > candle['open'])
            bearish_candles = sum(1 for _, candle in sequence.iterrows() if candle['close'] < candle['open'])
            
            if bullish_candles >= sequence_length * 0.8:  # At least 80% bullish
                direction = DisplacementType.BULLISH
                start_price = sequence.iloc[0]['open']
                end_price = sequence.iloc[-1]['close']
            elif bearish_candles >= sequence_length * 0.8:  # At least 80% bearish
                direction = DisplacementType.BEARISH
                start_price = sequence.iloc[0]['open']
                end_price = sequence.iloc[-1]['close']
            else:
                continue  # Mixed direction, not a clean displacement
            
            # Calculate displacement metrics
            displacement_size = abs(end_price - start_price)
            displacement_pct = displacement_size / start_price * 100
            
            if displacement_pct < min_displacement_pct:
                continue
            
            # Calculate strength
            total_volume = sequence['volume'].sum()
            avg_volume_before = df.iloc[i-10:i]['volume'].mean()
            volume_surge = total_volume / (avg_volume_before * sequence_length) if avg_volume_before > 0 else 1
            
            # Check for consistent candle sizes
            candle_sizes = sequence.apply(lambda x: abs(x['close'] - x['open']), axis=1)
            avg_candle_size = candle_sizes.mean()
            candle_consistency = 1 - (candle_sizes.std() / avg_candle_size) if avg_candle_size > 0 else 0
            
            # Calculate overall strength
            pct_strength = min(displacement_pct / 3, 1.0)
            volume_strength = min(volume_surge / 2, 1.0)
            consistency_strength = candle_consistency
            
            strength = (pct_strength + volume_strength + consistency_strength) / 3
            
            displacement = Displacement(
                start_idx=i,
                end_idx=i + sequence_length - 1,
                start_price=start_price,
                end_price=end_price,
                displacement_size=displacement_size,
                displacement_pct=displacement_pct,
                displacement_type=direction,
                strength=strength,
                volume_surge=volume_surge,
                candles_count=sequence_length,
                avg_candle_size=avg_candle_size,
                confluence_factors=[f"multi_candle_{sequence_length}", f"consistency_{candle_consistency:.2f}"]
            )
            displacements.append(displacement)
    
    return displacements

def _detect_volume_displacement(df: pd.DataFrame, min_displacement_pct: float) -> List[Displacement]:
    """Detect displacement based on volume spikes"""
    displacements = []
    
    # Calculate volume moving average
    df_copy = df.copy()
    df_copy['volume_ma'] = df_copy['volume'].rolling(window=20).mean()
    
    for i in range(20, len(df)):
        candle = df.iloc[i]
        volume_ma = df_copy.iloc[i]['volume_ma']
        
        # Look for volume spikes (3x+ average volume)
        if candle['volume'] > volume_ma * 3:
            # Check if accompanied by significant price movement
            price_move = abs(candle['close'] - candle['open'])
            price_move_pct = price_move / candle['open'] * 100
            
            if price_move_pct >= min_displacement_pct * 0.5:  # Lower threshold for volume-based
                direction = DisplacementType.BULLISH if candle['close'] > candle['open'] else DisplacementType.BEARISH
                
                # Calculate strength based on volume and price action
                volume_strength = min(candle['volume'] / volume_ma / 5, 1.0)
                price_strength = min(price_move_pct / 2, 1.0)
                
                strength = (volume_strength + price_strength) / 2
                
                displacement = Displacement(
                    start_idx=i,
                    end_idx=i,
                    start_price=candle['open'],
                    end_price=candle['close'],
                    displacement_size=price_move,
                    displacement_pct=price_move_pct,
                    displacement_type=direction,
                    strength=strength,
                    volume_surge=candle['volume'] / volume_ma,
                    candles_count=1,
                    avg_candle_size=price_move,
                    confluence_factors=["volume_spike", f"volume_{candle['volume']/volume_ma:.1f}x"]
                )
                displacements.append(displacement)
    
    return displacements

def _detect_gap_displacement(df: pd.DataFrame, min_displacement_pct: float) -> List[Displacement]:
    """Detect displacement through gaps"""
    displacements = []
    
    for i in range(1, len(df)):
        prev_candle = df.iloc[i-1]
        curr_candle = df.iloc[i]
        
        # Check for gaps
        gap_up = curr_candle['low'] > prev_candle['high']
        gap_down = curr_candle['high'] < prev_candle['low']
        
        if gap_up or gap_down:
            if gap_up:
                gap_size = curr_candle['low'] - prev_candle['high']
                direction = DisplacementType.BULLISH
                start_price = prev_candle['close']
                end_price = curr_candle['open']
            else:
                gap_size = prev_candle['low'] - curr_candle['high']
                direction = DisplacementType.BEARISH
                start_price = prev_candle['close']
                end_price = curr_candle['open']
            
            gap_pct = gap_size / start_price * 100
            
            if gap_pct >= min_displacement_pct * 0.3:  # Lower threshold for gaps
                # Calculate strength
                gap_strength = min(gap_pct / 2, 1.0)
                volume_strength = min(curr_candle['volume'] / df['volume'].rolling(10).mean().iloc[i], 2.0) / 2.0
                
                strength = (gap_strength + volume_strength) / 2
                
                displacement = Displacement(
                    start_idx=i-1,
                    end_idx=i,
                    start_price=start_price,
                    end_price=end_price,
                    displacement_size=gap_size,
                    displacement_pct=gap_pct,
                    displacement_type=direction,
                    strength=strength,
                    volume_surge=curr_candle['volume'] / df['volume'].rolling(10).mean().iloc[i],
                    candles_count=2,
                    avg_candle_size=gap_size,
                    confluence_factors=["gap_displacement", f"gap_{gap_pct:.1f}%"]
                )
                displacements.append(displacement)
    
    return displacements

def _remove_overlapping_displacements(displacements: List[Displacement]) -> List[Displacement]:
    """Remove overlapping displacements, keeping stronger ones"""
    if not displacements:
        return displacements
    
    # Sort by strength
    displacements.sort(key=lambda x: x.strength, reverse=True)
    
    filtered = []
    
    for displacement in displacements:
        overlapping = False
        
        for existing in filtered:
            # Check if time periods overlap
            if (displacement.start_idx <= existing.end_idx and 
                displacement.end_idx >= existing.start_idx):
                overlapping = True
                break
        
        if not overlapping:
            filtered.append(displacement)
    
    return filtered

def _update_displacement_status(df: pd.DataFrame, displacements: List[Displacement]) -> List[Displacement]:
    """Update displacement status with follow-through analysis"""
    
    for displacement in displacements:
        # Analyze follow-through after displacement
        follow_through_period = 10  # Look ahead 10 candles
        end_idx = min(displacement.end_idx + follow_through_period, len(df) - 1)
        
        if end_idx > displacement.end_idx:
            follow_through_data = df.iloc[displacement.end_idx + 1:end_idx + 1]
            
            if len(follow_through_data) > 0:
                # Check if price continued in displacement direction
                if displacement.displacement_type == DisplacementType.BULLISH:
                    highest_after = follow_through_data['high'].max()
                    follow_through = highest_after > displacement.end_price
                    retracement = (displacement.end_price - follow_through_data['low'].min()) / displacement.displacement_size
                else:
                    lowest_after = follow_through_data['low'].min()
                    follow_through = lowest_after < displacement.end_price
                    retracement = (follow_through_data['high'].max() - displacement.end_price) / displacement.displacement_size
                
                displacement.follow_through = follow_through
                displacement.retracement_level = max(0, min(retracement, 1))  # Clamp between 0-1
                
                # Add follow-through to confluence factors
                if follow_through:
                    displacement.confluence_factors.append("follow_through")
                if retracement < 0.5:
                    displacement.confluence_factors.append("low_retracement")
    
    return displacements

def get_recent_displacements(df: pd.DataFrame, lookback_periods: int = 50) -> List[Displacement]:
    """Get recent displacements within specified lookback period"""
    all_displacements = detect_displacement(df)
    
    recent_cutoff = len(df) - lookback_periods
    recent_displacements = [d for d in all_displacements if d.end_idx >= recent_cutoff]
    
    return recent_displacements

def analyze_displacement_confluence(displacements: List[Displacement], current_price: float) -> Dict:
    """Analyze confluence of recent displacements"""
    if not displacements:
        return {"confluence": False, "strength": 0, "direction": "neutral"}
    
    bullish_displacements = [d for d in displacements if d.displacement_type == DisplacementType.BULLISH]
    bearish_displacements = [d for d in displacements if d.displacement_type == DisplacementType.BEARISH]
    
    bullish_strength = sum(d.strength for d in bullish_displacements)
    bearish_strength = sum(d.strength for d in bearish_displacements)
    
    total_strength = bullish_strength + bearish_strength
    
    if total_strength == 0:
        return {"confluence": False, "strength": 0, "direction": "neutral"}
    
    # Determine dominant direction
    if bullish_strength > bearish_strength * 1.3:
        direction = "bullish"
        strength = bullish_strength / total_strength
    elif bearish_strength > bullish_strength * 1.3:
        direction = "bearish"
        strength = bearish_strength / total_strength
    else:
        direction = "neutral"
        strength = 0.5
    
    # Check for confluence
    confluence = len(displacements) >= 2 and total_strength > 1.5
    
    # Find most recent displacement
    most_recent = max(displacements, key=lambda d: d.end_idx) if displacements else None
    
    return {
        "confluence": confluence,
        "strength": strength,
        "direction": direction,
        "bullish_displacements": len(bullish_displacements),
        "bearish_displacements": len(bearish_displacements),
        "total_displacements": len(displacements),
        "most_recent": most_recent,
        "avg_follow_through": sum(1 for d in displacements if d.follow_through) / len(displacements) if displacements else 0
    }
