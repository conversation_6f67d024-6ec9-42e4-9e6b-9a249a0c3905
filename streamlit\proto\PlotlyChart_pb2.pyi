"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import collections.abc
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.internal.enum_type_wrapper
import google.protobuf.message
import sys
import typing

if sys.version_info >= (3, 10):
    import typing as typing_extensions
else:
    import typing_extensions

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class PlotlyChart(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    class _SelectionMode:
        ValueType = typing.NewType("ValueType", builtins.int)
        V: typing_extensions.TypeAlias = ValueType

    class _SelectionModeEnumTypeWrapper(google.protobuf.internal.enum_type_wrapper._EnumTypeWrapper[PlotlyChart._SelectionMode.ValueType], builtins.type):
        DESCRIPTOR: google.protobuf.descriptor.EnumDescriptor
        POINTS: PlotlyChart._SelectionMode.ValueType  # 0
        """Point selection mode"""
        BOX: PlotlyChart._SelectionMode.ValueType  # 1
        """Box selection mode"""
        LASSO: PlotlyChart._SelectionMode.ValueType  # 2
        """Lasso selection mode"""

    class SelectionMode(_SelectionMode, metaclass=_SelectionModeEnumTypeWrapper):
        """Available selection modes:"""

    POINTS: PlotlyChart.SelectionMode.ValueType  # 0
    """Point selection mode"""
    BOX: PlotlyChart.SelectionMode.ValueType  # 1
    """Box selection mode"""
    LASSO: PlotlyChart.SelectionMode.ValueType  # 2
    """Lasso selection mode"""

    USE_CONTAINER_WIDTH_FIELD_NUMBER: builtins.int
    THEME_FIELD_NUMBER: builtins.int
    ID_FIELD_NUMBER: builtins.int
    SELECTION_MODE_FIELD_NUMBER: builtins.int
    FORM_ID_FIELD_NUMBER: builtins.int
    SPEC_FIELD_NUMBER: builtins.int
    CONFIG_FIELD_NUMBER: builtins.int
    URL_FIELD_NUMBER: builtins.int
    FIGURE_FIELD_NUMBER: builtins.int
    use_container_width: builtins.bool
    """If True, will overwrite the chart width spec to fit to container."""
    theme: builtins.str
    """override the properties with a theme. Currently, only "streamlit" or None are accepted."""
    id: builtins.str
    """The unique element ID of this chart."""
    form_id: builtins.str
    """Form ID, filled if selections are activated."""
    spec: builtins.str
    """JSON-serialized dict containing keys from the set {data, frames, layout}."""
    config: builtins.str
    """JSON-serialized dict with Plotly's config object."""
    url: builtins.str
    """DEPRECATED and unused."""
    @property
    def selection_mode(self) -> google.protobuf.internal.containers.RepeatedScalarFieldContainer[global___PlotlyChart.SelectionMode.ValueType]:
        """Activate selections types on the chart."""

    @property
    def figure(self) -> global___Figure:
        """DEPRECATED and unused."""

    def __init__(
        self,
        *,
        use_container_width: builtins.bool = ...,
        theme: builtins.str = ...,
        id: builtins.str = ...,
        selection_mode: collections.abc.Iterable[global___PlotlyChart.SelectionMode.ValueType] | None = ...,
        form_id: builtins.str = ...,
        spec: builtins.str = ...,
        config: builtins.str = ...,
        url: builtins.str = ...,
        figure: global___Figure | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["chart", b"chart", "figure", b"figure", "url", b"url"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["chart", b"chart", "config", b"config", "figure", b"figure", "form_id", b"form_id", "id", b"id", "selection_mode", b"selection_mode", "spec", b"spec", "theme", b"theme", "url", b"url", "use_container_width", b"use_container_width"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["chart", b"chart"]) -> typing.Literal["url", "figure"] | None: ...

global___PlotlyChart = PlotlyChart

@typing.final
class Figure(google.protobuf.message.Message):
    """DEPRECATED and unused."""

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SPEC_FIELD_NUMBER: builtins.int
    CONFIG_FIELD_NUMBER: builtins.int
    spec: builtins.str
    config: builtins.str
    def __init__(
        self,
        *,
        spec: builtins.str = ...,
        config: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["config", b"config", "spec", b"spec"]) -> None: ...

global___Figure = Figure
