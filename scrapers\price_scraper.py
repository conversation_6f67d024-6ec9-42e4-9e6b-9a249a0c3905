import requests
from bs4 import BeautifulSoup
import pandas as pd
from datetime import datetime
import time
import random
import logging
import os
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from dateutil import tz
from selenium.common.exceptions import (
    TimeoutException,
    WebDriverException,
    NoSuchElementException,
    StaleElementReferenceException
)

# Import error handling utilities
try:
    from app.utils.error_handling import (
        ScrapingError,
        ResourceError,
        ConfigError,
        APIError,
        retry_on_exception,
        handle_exception,
        log_exception,
        robust_function,
        ErrorHandler
    )
    ERROR_HANDLING_AVAILABLE = True
except ImportError:
    ERROR_HANDLING_AVAILABLE = False
    logging.warning("Error handling utilities not available. Using basic error handling.")

    # Define fallback error classes if not available
    class ScrapingError(Exception):
        pass

    class ResourceError(Exception):
        pass

    class ConfigError(Exception):
        pass

    class APIError(Exception):
        pass

# Import retry utilities
try:
    from app.utils.retry import retry_with_backoff
    RETRY_AVAILABLE = True
except ImportError:
    RETRY_AVAILABLE = False
    logging.warning("Retry utilities not available. Scraping operations may be less reliable.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PriceScraper:
    """
    Scraper for getting stock price data from TradingView and Mubasher
    """

    def __init__(self, source="tradingview", use_real_time=False, username=None, password=None, tradingview_email=None, tradingview_password=None, is_gmail=False):
        """
        Initialize PriceScraper

        Args:
            source (str): Source for price data (tradingview, mubasher)
            use_real_time (bool): Whether to use real-time data
            username (str): Username for TradingView login
            password (str): Password for TradingView login
            tradingview_email (str): TradingView email/username
            tradingview_password (str): TradingView password
            is_gmail (bool): Whether the TradingView account uses Gmail login
        """
        self.source = source.lower()
        self.use_real_time = use_real_time
        self.tradingview_email = tradingview_email or username
        self.tradingview_password = tradingview_password or password
        self.is_logged_in = False
        self.is_gmail = is_gmail

        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept-Language': 'en-US,en;q=0.9',
        }

        self.driver = None

        logger.info(f"Initialized PriceScraper with source: {self.source}")

    def __del__(self):
        """
        Destructor to clean up WebDriver
        """
        self.close_driver()

    def close(self):
        """
        Close the scraper and clean up resources
        """
        self.close_driver()

    def __enter__(self):
        """
        Context manager entry
        """
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        Context manager exit
        """
        self.close_driver()

    def initialize_driver(self):
        """
        Initialize Chrome WebDriver with appropriate options
        """
        if self.driver is not None:
            return

        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # Try to find chromedriver
            chromedriver_path = os.path.join(os.path.dirname(__file__), 'chromedriver.exe')

            if os.path.exists(chromedriver_path):
                service = Service(chromedriver_path)
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                # Try system chromedriver
                self.driver = webdriver.Chrome(options=chrome_options)

            logger.info("Chrome WebDriver initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Chrome WebDriver: {str(e)}")
            self.driver = None

    def close_driver(self):
        """
        Close the WebDriver
        """
        if self.driver:
            try:
                self.driver.quit()
                logger.info("WebDriver closed successfully")
            except Exception as e:
                logger.error(f"Error closing WebDriver: {str(e)}")
            finally:
                self.driver = None

    def login_to_tradingview(self):
        """
        Login to TradingView using Selenium
        """
        if self.is_logged_in:
            return True

        if not self.tradingview_email or not self.tradingview_password:
            logger.warning("TradingView credentials not provided")
            return False

        try:
            self.initialize_driver()
            if not self.driver:
                return False

            logger.info("Attempting to login to TradingView...")

            # Navigate to TradingView login page
            self.driver.get("https://www.tradingview.com/accounts/signin/")
            time.sleep(3)

            if self.is_gmail:
                # Login with Google
                try:
                    google_button = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//span[contains(text(), 'Google')]"))
                    )
                    google_button.click()
                    time.sleep(2)

                    # Enter email
                    email_input = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.ID, "identifierId"))
                    )
                    email_input.send_keys(self.tradingview_email)

                    next_button = self.driver.find_element(By.ID, "identifierNext")
                    next_button.click()
                    time.sleep(3)

                    # Enter password
                    password_input = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.NAME, "password"))
                    )
                    password_input.send_keys(self.tradingview_password)

                    password_next = self.driver.find_element(By.ID, "passwordNext")
                    password_next.click()
                    time.sleep(5)

                except Exception as e:
                    logger.error(f"Google login failed: {str(e)}")
                    return False
            else:
                # Regular email/password login
                try:
                    # Enter email
                    email_input = WebDriverWait(self.driver, 10).until(
                        EC.presence_of_element_located((By.NAME, "username"))
                    )
                    email_input.send_keys(self.tradingview_email)

                    # Enter password
                    password_input = self.driver.find_element(By.NAME, "password")
                    password_input.send_keys(self.tradingview_password)

                    # Click login button
                    login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
                    login_button.click()
                    time.sleep(5)

                except Exception as e:
                    logger.error(f"Regular login failed: {str(e)}")
                    return False

            # Check if login was successful
            current_url = self.driver.current_url
            if "tradingview.com" in current_url and "signin" not in current_url:
                self.is_logged_in = True
                logger.info("Successfully logged in to TradingView")
                return True
            else:
                logger.error("Login failed - still on login page")
                return False

        except Exception as e:
            logger.error(f"Login to TradingView failed: {str(e)}")
            return False

    def is_logged_in_to_tradingview(self):
        """
        Check if logged in to TradingView
        """
        return self.is_logged_in

    def get_price(self, symbol, use_cache=True):
        """
        Get stock price from the specified source

        Args:
            symbol (str): Stock symbol
            use_cache (bool): Whether to use cached data (not implemented)

        Returns:
            dict: Price data
        """
        if self.source == "tradingview":
            return self.get_tradingview_price(symbol)
        elif self.source == "mubasher":
            return self.get_mubasher_price(symbol)
        else:
            return self._generate_sample_price(symbol)

    def get_tradingview_price(self, symbol):
        """
        Get stock price from TradingView using Selenium

        Args:
            symbol (str): Stock symbol

        Returns:
            dict: Price data
        """
        try:
            # Initialize driver if needed
            if not self.driver:
                self.initialize_driver()
                if not self.driver:
                    return self._generate_sample_price(symbol)

            # Format symbol for EGX
            formatted_symbol = f"EGX:{symbol.upper()}"
            url = f"https://www.tradingview.com/symbols/{formatted_symbol}/"

            logger.info(f"Fetching TradingView data for {formatted_symbol}")
            self.driver.get(url)
            time.sleep(3)

            # Try to get the price
            price_selectors = [
                "[data-field='last_price']",
                ".tv-symbol-price-quote__value",
                "[class*='price']",
                ".js-symbol-last"
            ]

            price = None
            for selector in price_selectors:
                try:
                    price_element = WebDriverWait(self.driver, 5).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    price_text = price_element.text.strip()
                    # Clean the price text
                    price_text = price_text.replace(',', '').replace('EGP', '').strip()
                    price = float(price_text)
                    break
                except (TimeoutException, ValueError, NoSuchElementException):
                    continue

            if price is None:
                logger.warning(f"Could not find price for {symbol} on TradingView")
                return self._generate_sample_price(symbol)

            return {
                'symbol': symbol,
                'price': price,
                'currency': 'EGP',
                'timestamp': datetime.now().isoformat(),
                'source': 'TradingView',
                'real_time': self.use_real_time
            }

        except Exception as e:
            logger.error(f"Error getting TradingView price for {symbol}: {str(e)}")
            return self._generate_sample_price(symbol)

    def get_mubasher_price(self, symbol):
        """
        Get stock price from Mubasher using requests

        Args:
            symbol (str): Stock symbol

        Returns:
            dict: Price data
        """
        try:
            url = f"https://www.mubasher.info/markets/EGX/stocks/{symbol.upper()}"

            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Try to find price element
            price_selectors = [
                '.price',
                '.last-price',
                '[data-field="last_price"]',
                '.stock-price'
            ]

            price = None
            for selector in price_selectors:
                price_element = soup.select_one(selector)
                if price_element:
                    try:
                        price_text = price_element.get_text().strip()
                        price_text = price_text.replace(',', '').replace('EGP', '').strip()
                        price = float(price_text)
                        break
                    except ValueError:
                        continue

            if price is None:
                logger.warning(f"Could not find price for {symbol} on Mubasher")
                return self._generate_sample_price(symbol)

            return {
                'symbol': symbol,
                'price': price,
                'currency': 'EGP',
                'timestamp': datetime.now().isoformat(),
                'source': 'Mubasher',
                'real_time': False
            }

        except Exception as e:
            logger.error(f"Error getting Mubasher price for {symbol}: {str(e)}")
            return self._generate_sample_price(symbol)

    def _generate_sample_price(self, symbol):
        """
        Generate sample price data for testing/fallback
        """
        base_prices = {
            'COMI': 82.5,
            'CIB': 45.2,
            'ETEL': 18.7,
            'HRHO': 12.3,
            'OCDI': 156.8
        }

        base_price = base_prices.get(symbol.upper(), 50.0)
        variation = random.uniform(-0.05, 0.05)
        current_price = round(base_price * (1 + variation), 2)

        return {
            'symbol': symbol,
            'price': current_price,
            'currency': 'EGP',
            'timestamp': datetime.now().isoformat(),
            'source': 'Sample Data',
            'real_time': False
        }

    # Property getters/setters for backward compatibility
    @property
    def username(self):
        """Backward compatibility for username attribute"""
        return self.tradingview_email

    @username.setter
    def username(self, value):
        """Backward compatibility for username attribute"""
        self.tradingview_email = value

    @property
    def password(self):
        """Backward compatibility for password attribute"""
        return self.tradingview_password

    @password.setter
    def password(self, value):
        """Backward compatibility for password attribute"""
        self.tradingview_password = value
