#!/usr/bin/env python3
"""
SMC Diagnostics Tool
Analyze why confluence factors are not working and suggest parameter adjustments
"""

import pandas as pd
import numpy as np
from typing import Dict, List
import os

# Import SMC modules
from indicators.order_blocks import detect_order_blocks, get_active_order_blocks
from indicators.fvg import detect_fvg, get_active_fvgs
from indicators.liquidity_zones import detect_liquidity_zones, get_active_liquidity_zones
from indicators.displacement import detect_displacement, get_recent_displacements

def analyze_data_quality(df: pd.DataFrame, symbol: str) -> Dict:
    """Analyze data quality for SMC analysis"""
    
    analysis = {
        'symbol': symbol,
        'total_candles': len(df),
        'data_quality': {},
        'recommendations': []
    }
    
    # Check basic data quality
    analysis['data_quality']['has_volume'] = 'volume' in df.columns and df['volume'].sum() > 0
    analysis['data_quality']['volume_avg'] = df['volume'].mean() if 'volume' in df.columns else 0
    analysis['data_quality']['volume_zeros'] = (df['volume'] == 0).sum() if 'volume' in df.columns else 0
    
    # Price analysis
    analysis['data_quality']['price_range'] = df['high'].max() - df['low'].min()
    analysis['data_quality']['avg_candle_size'] = ((df['high'] - df['low']) / df['close']).mean()
    analysis['data_quality']['volatility'] = df['close'].pct_change().std()
    
    # Missing data
    analysis['data_quality']['missing_data'] = df.isnull().sum().sum()
    
    # Recommendations
    if not analysis['data_quality']['has_volume']:
        analysis['recommendations'].append("❌ No volume data - SMC analysis will be limited")
    
    if analysis['data_quality']['volume_zeros'] > len(df) * 0.1:
        analysis['recommendations'].append("⚠️ Many zero volume candles - consider data source quality")
    
    if analysis['data_quality']['avg_candle_size'] < 0.01:
        analysis['recommendations'].append("⚠️ Small candle sizes - may need lower thresholds")
    
    if len(df) < 100:
        analysis['recommendations'].append("⚠️ Insufficient data - need at least 100 candles for reliable SMC")
    
    return analysis

def diagnose_order_blocks(df: pd.DataFrame) -> Dict:
    """Diagnose order block detection issues"""
    
    # Try different parameters
    results = {}
    
    # Default parameters
    default_obs = detect_order_blocks(df, lookback=20, min_strength=0.3)
    results['default'] = {
        'count': len(default_obs),
        'params': 'lookback=20, min_strength=0.3'
    }
    
    # Relaxed parameters
    relaxed_obs = detect_order_blocks(df, lookback=10, min_strength=0.1)
    results['relaxed'] = {
        'count': len(relaxed_obs),
        'params': 'lookback=10, min_strength=0.1'
    }
    
    # Very relaxed parameters
    very_relaxed_obs = detect_order_blocks(df, lookback=5, min_strength=0.05)
    results['very_relaxed'] = {
        'count': len(very_relaxed_obs),
        'params': 'lookback=5, min_strength=0.05'
    }
    
    return results

def diagnose_fvgs(df: pd.DataFrame) -> Dict:
    """Diagnose FVG detection issues"""
    
    results = {}
    
    # Default parameters
    default_fvgs = detect_fvg(df, min_gap_size=0.005)  # 0.5%
    results['default'] = {
        'count': len(default_fvgs),
        'params': 'min_gap_size=0.5%'
    }
    
    # Relaxed parameters
    relaxed_fvgs = detect_fvg(df, min_gap_size=0.002)  # 0.2%
    results['relaxed'] = {
        'count': len(relaxed_fvgs),
        'params': 'min_gap_size=0.2%'
    }
    
    # Very relaxed parameters
    very_relaxed_fvgs = detect_fvg(df, min_gap_size=0.001)  # 0.1%
    results['very_relaxed'] = {
        'count': len(very_relaxed_fvgs),
        'params': 'min_gap_size=0.1%'
    }
    
    return results

def diagnose_liquidity_zones(df: pd.DataFrame) -> Dict:
    """Diagnose liquidity zone detection issues"""
    
    results = {}
    
    # Default parameters
    default_zones = detect_liquidity_zones(df, lookback=50)
    results['default'] = {
        'count': len(default_zones),
        'params': 'lookback=50'
    }
    
    # Relaxed parameters
    relaxed_zones = detect_liquidity_zones(df, lookback=20)
    results['relaxed'] = {
        'count': len(relaxed_zones),
        'params': 'lookback=20'
    }
    
    # Very relaxed parameters
    very_relaxed_zones = detect_liquidity_zones(df, lookback=10)
    results['very_relaxed'] = {
        'count': len(very_relaxed_zones),
        'params': 'lookback=10'
    }
    
    return results

def diagnose_displacements(df: pd.DataFrame) -> Dict:
    """Diagnose displacement detection issues"""
    
    results = {}
    
    # Default parameters
    default_disps = detect_displacement(df, min_displacement_pct=2.0, min_strength=0.3)
    results['default'] = {
        'count': len(default_disps),
        'params': 'min_displacement=2.0%, min_strength=0.3'
    }
    
    # Relaxed parameters
    relaxed_disps = detect_displacement(df, min_displacement_pct=1.0, min_strength=0.1)
    results['relaxed'] = {
        'count': len(relaxed_disps),
        'params': 'min_displacement=1.0%, min_strength=0.1'
    }
    
    # Very relaxed parameters
    very_relaxed_disps = detect_displacement(df, min_displacement_pct=0.5, min_strength=0.05)
    results['very_relaxed'] = {
        'count': len(very_relaxed_disps),
        'params': 'min_displacement=0.5%, min_strength=0.05'
    }
    
    return results

def generate_optimal_parameters(df: pd.DataFrame) -> Dict:
    """Generate optimal parameters based on data characteristics"""
    
    # Analyze data characteristics
    volatility = df['close'].pct_change().std()
    avg_volume = df['volume'].mean() if 'volume' in df.columns else 0
    candle_count = len(df)
    
    # Adjust parameters based on data
    if volatility < 0.02:  # Low volatility
        ob_min_strength = 0.1
        fvg_min_gap = 0.001  # 0.1%
        disp_min_pct = 0.5
        liq_lookback = 20
    elif volatility > 0.05:  # High volatility
        ob_min_strength = 0.4
        fvg_min_gap = 0.01  # 1.0%
        disp_min_pct = 3.0
        liq_lookback = 100
    else:  # Medium volatility
        ob_min_strength = 0.2
        fvg_min_gap = 0.003  # 0.3%
        disp_min_pct = 1.5
        liq_lookback = 50
    
    # Adjust for data size
    if candle_count < 100:
        ob_lookback = 5
        liq_lookback = min(liq_lookback, 20)
    elif candle_count < 500:
        ob_lookback = 10
        liq_lookback = min(liq_lookback, 50)
    else:
        ob_lookback = 20
    
    return {
        'order_blocks': {
            'lookback': ob_lookback,
            'min_strength': ob_min_strength
        },
        'fvg': {
            'min_gap_size': fvg_min_gap
        },
        'liquidity_zones': {
            'lookback': liq_lookback
        },
        'displacement': {
            'min_displacement_pct': disp_min_pct,
            'min_strength': ob_min_strength
        },
        'data_characteristics': {
            'volatility': volatility,
            'avg_volume': avg_volume,
            'candle_count': candle_count
        }
    }

def run_full_diagnostics(csv_file: str) -> Dict:
    """Run complete SMC diagnostics on a CSV file"""
    
    # Load data
    df = pd.read_csv(csv_file)
    symbol = os.path.basename(csv_file).replace('.csv', '')
    
    print(f"\n🔍 SMC Diagnostics for {symbol}")
    print("=" * 50)
    
    # Data quality analysis
    data_quality = analyze_data_quality(df, symbol)
    print(f"\n📊 Data Quality:")
    print(f"   Total Candles: {data_quality['total_candles']}")
    print(f"   Has Volume: {data_quality['data_quality']['has_volume']}")
    print(f"   Volatility: {data_quality['data_quality']['volatility']:.4f}")
    print(f"   Avg Candle Size: {data_quality['data_quality']['avg_candle_size']:.4f}")
    
    for rec in data_quality['recommendations']:
        print(f"   {rec}")
    
    # SMC diagnostics
    print(f"\n🔲 Order Blocks Diagnostics:")
    ob_results = diagnose_order_blocks(df)
    for level, result in ob_results.items():
        print(f"   {level}: {result['count']} blocks ({result['params']})")
    
    print(f"\n⚡ FVG Diagnostics:")
    fvg_results = diagnose_fvgs(df)
    for level, result in fvg_results.items():
        print(f"   {level}: {result['count']} FVGs ({result['params']})")
    
    print(f"\n💧 Liquidity Zones Diagnostics:")
    liq_results = diagnose_liquidity_zones(df)
    for level, result in liq_results.items():
        print(f"   {level}: {result['count']} zones ({result['params']})")
    
    print(f"\n🚀 Displacement Diagnostics:")
    disp_results = diagnose_displacements(df)
    for level, result in disp_results.items():
        print(f"   {level}: {result['count']} displacements ({result['params']})")
    
    # Optimal parameters
    optimal_params = generate_optimal_parameters(df)
    print(f"\n🎯 Recommended Parameters:")
    print(f"   Order Blocks: lookback={optimal_params['order_blocks']['lookback']}, min_strength={optimal_params['order_blocks']['min_strength']}")
    print(f"   FVGs: min_gap_size={optimal_params['fvg']['min_gap_size']:.3f}")
    print(f"   Liquidity Zones: lookback={optimal_params['liquidity_zones']['lookback']}")
    print(f"   Displacements: min_displacement={optimal_params['displacement']['min_displacement_pct']}%")
    
    return {
        'data_quality': data_quality,
        'order_blocks': ob_results,
        'fvgs': fvg_results,
        'liquidity_zones': liq_results,
        'displacements': disp_results,
        'optimal_parameters': optimal_params
    }

if __name__ == "__main__":
    # Test with available CSV files
    data_dir = "data/historical"
    if os.path.exists(data_dir):
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]
        if csv_files:
            test_file = os.path.join(data_dir, csv_files[0])
            run_full_diagnostics(test_file)
        else:
            print("❌ No CSV files found in data/historical/")
    else:
        print("❌ data/historical/ directory not found")
