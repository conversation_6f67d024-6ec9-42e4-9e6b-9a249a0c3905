#!/usr/bin/env python3
"""
CSV Data Loader for EGX Historical Data
Loads historical OHLCV data from CSV files for SMC analysis
"""

import pandas as pd
import os
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from log_setup import logger

class CSVDataLoader:
    """Load historical data from CSV files"""
    
    def __init__(self, data_dir: str = "data/historical"):
        self.data_dir = data_dir
        self.cache = {}  # Cache loaded data
        
    def get_available_symbols(self) -> List[str]:
        """Get list of available symbols from CSV files (prioritize EGX stocks)"""
        try:
            symbols = []
            egx_symbols = []  # EGX stocks (with date column)
            crypto_symbols = []  # Crypto data (without date column)

            if os.path.exists(self.data_dir):
                for file in os.listdir(self.data_dir):
                    if file.endswith('.csv') and not file.startswith('_'):
                        # Extract symbol from filename (e.g., COMI.csv -> COMI)
                        symbol = file.replace('.csv', '')

                        # Check if it's an EGX stock (has date column) or crypto
                        csv_path = os.path.join(self.data_dir, file)
                        try:
                            df_sample = pd.read_csv(csv_path, nrows=1)
                            if 'date' in df_sample.columns:
                                egx_symbols.append(symbol)
                            else:
                                crypto_symbols.append(symbol)
                        except:
                            symbols.append(symbol)  # Fallback

            # Prioritize EGX symbols first, then crypto
            symbols = egx_symbols + crypto_symbols

            logger.info(f"📁 Found {len(symbols)} CSV files:")
            logger.info(f"  📈 EGX stocks: {egx_symbols}")
            logger.info(f"  🪙 Crypto: {crypto_symbols}")
            return symbols

        except Exception as e:
            logger.error(f"❌ Error getting available symbols: {e}")
            return []
    
    def load_symbol_data(self, symbol: str, limit: int = 150) -> Optional[List[Dict]]:
        """
        Load OHLCV data for a symbol from CSV file
        
        Args:
            symbol: Stock symbol (e.g., 'COMI')
            limit: Number of recent candles to return
            
        Returns:
            List of OHLCV dictionaries or None if error
        """
        try:
            # Check cache first
            cache_key = f"{symbol}_{limit}"
            if cache_key in self.cache:
                logger.info(f"📊 Using cached data for {symbol}")
                return self.cache[cache_key]
            
            # Load from CSV file
            csv_file = os.path.join(self.data_dir, f"{symbol}.csv")
            
            if not os.path.exists(csv_file):
                logger.error(f"❌ CSV file not found: {csv_file}")
                return None
            
            logger.info(f"📊 Loading {symbol} data from CSV: {csv_file}")
            
            # Read CSV file
            df = pd.read_csv(csv_file)

            # Check if this is EGX format (with date) or crypto format (without date)
            has_date_column = 'date' in df.columns

            if has_date_column:
                # EGX format: date,open,high,low,close,volume
                required_cols = ['date', 'open', 'high', 'low', 'close', 'volume']
                missing_cols = [col for col in required_cols if col not in df.columns]

                if missing_cols:
                    logger.error(f"❌ Missing columns in {symbol}.csv: {missing_cols}")
                    return None

                # Convert date column to timestamp
                df['date'] = pd.to_datetime(df['date'])

                # Sort by date (oldest first)
                df = df.sort_values('date')

                # Take the most recent 'limit' candles
                if len(df) > limit:
                    df = df.tail(limit)

                # Convert to list of dictionaries
                ohlcv_data = []
                for _, row in df.iterrows():
                    candle = {
                        "timestamp": int(row['date'].timestamp() * 1000),  # Convert to milliseconds
                        "open": float(row['open']),
                        "high": float(row['high']),
                        "low": float(row['low']),
                        "close": float(row['close']),
                        "volume": float(row['volume'])
                    }
                    ohlcv_data.append(candle)

            else:
                # Crypto format: open,high,low,close,volume (no date column)
                required_cols = ['open', 'high', 'low', 'close', 'volume']
                missing_cols = [col for col in required_cols if col not in df.columns]

                if missing_cols:
                    logger.error(f"❌ Missing columns in {symbol}.csv: {missing_cols}")
                    return None

                # Take the most recent 'limit' candles
                if len(df) > limit:
                    df = df.tail(limit)

                # Convert to list of dictionaries (generate timestamps)
                ohlcv_data = []
                base_timestamp = int(datetime.now().timestamp() * 1000)  # Current time in ms

                for i, (_, row) in enumerate(df.iterrows()):
                    # Generate timestamps going backwards (most recent first)
                    timestamp = base_timestamp - (len(df) - i - 1) * 3600000  # 1 hour intervals

                    candle = {
                        "timestamp": timestamp,
                        "open": float(row['open']),
                        "high": float(row['high']),
                        "low": float(row['low']),
                        "close": float(row['close']),
                        "volume": float(row['volume'])
                    }
                    ohlcv_data.append(candle)
            
            # Cache the result
            self.cache[cache_key] = ohlcv_data

            logger.info(f"✅ Loaded {len(ohlcv_data)} candles for {symbol}")

            # Log date range only for EGX format (with date column)
            if has_date_column:
                logger.info(f"📅 Date range: {df.iloc[0]['date'].strftime('%Y-%m-%d')} to {df.iloc[-1]['date'].strftime('%Y-%m-%d')}")
            else:
                logger.info(f"📅 Crypto data: {len(ohlcv_data)} candles with generated timestamps")

            logger.info(f"💰 Price range: {df['low'].min():.2f} - {df['high'].max():.2f}")

            return ohlcv_data
            
        except Exception as e:
            logger.error(f"❌ Error loading {symbol} from CSV: {e}")
            return None
    
    def get_current_price(self, symbol: str) -> Optional[Dict]:
        """
        Get the most recent price data for a symbol
        
        Args:
            symbol: Stock symbol
            
        Returns:
            Dictionary with current price info or None if error
        """
        try:
            # Load recent data (just need the last candle)
            data = self.load_symbol_data(symbol, limit=1)
            
            if not data or len(data) == 0:
                return None
            
            last_candle = data[-1]
            
            # Calculate price change
            if len(data) >= 2:
                prev_close = data[-2]['close']
                current_close = last_candle['close']
                change = current_close - prev_close
                change_percent = (change / prev_close) * 100
            else:
                change = 0
                change_percent = 0
            
            price_data = {
                "symbol": f"EGX:{symbol}",
                "price": last_candle['close'],
                "open": last_candle['open'],
                "high": last_candle['high'],
                "low": last_candle['low'],
                "volume": last_candle['volume'],
                "change": change,
                "changePercent": change_percent,
                "timestamp": last_candle['timestamp']
            }
            
            logger.info(f"💰 Current price for {symbol}: {price_data['price']}")
            return price_data
            
        except Exception as e:
            logger.error(f"❌ Error getting current price for {symbol}: {e}")
            return None
    
    def clear_cache(self):
        """Clear the data cache"""
        self.cache.clear()
        logger.info("🗑️ CSV data cache cleared")

# Global instance
csv_loader = CSVDataLoader()

# Async wrapper functions to match the API interface
async def get_ohlcv_from_csv(symbol: str, interval: str = "1d", limit: int = 150) -> Optional[List[Dict]]:
    """
    Async wrapper for loading OHLCV data from CSV
    
    Args:
        symbol: Stock symbol
        interval: Time interval (ignored for CSV data, always daily)
        limit: Number of candles to return
        
    Returns:
        List of OHLCV dictionaries
    """
    logger.info(f"📊 Loading CSV data for {symbol} ({limit} candles)")
    return csv_loader.load_symbol_data(symbol, limit)

async def get_current_price_from_csv(symbol: str) -> Optional[Dict]:
    """
    Async wrapper for getting current price from CSV
    
    Args:
        symbol: Stock symbol
        
    Returns:
        Dictionary with current price info
    """
    logger.info(f"💰 Getting current price for {symbol} from CSV")
    return csv_loader.get_current_price(symbol)

async def get_available_csv_symbols() -> List[str]:
    """
    Get list of available symbols from CSV files
    
    Returns:
        List of available symbols
    """
    return csv_loader.get_available_symbols()

if __name__ == "__main__":
    # Test the CSV loader
    import asyncio
    
    async def test_csv_loader():
        print("🧪 Testing CSV Data Loader")
        print("=" * 50)
        
        # Test available symbols
        symbols = await get_available_csv_symbols()
        print(f"📁 Available symbols: {symbols}")
        
        if symbols:
            symbol = symbols[0]  # Test with first available symbol
            
            # Test OHLCV data
            print(f"\n📊 Testing OHLCV data for {symbol}...")
            ohlcv = await get_ohlcv_from_csv(symbol, limit=5)
            
            if ohlcv:
                print(f"✅ Loaded {len(ohlcv)} candles")
                print(f"📅 First candle: {ohlcv[0]}")
                print(f"📅 Last candle: {ohlcv[-1]}")
            else:
                print("❌ Failed to load OHLCV data")
            
            # Test current price
            print(f"\n💰 Testing current price for {symbol}...")
            price = await get_current_price_from_csv(symbol)
            
            if price:
                print(f"✅ Current price: {price}")
            else:
                print("❌ Failed to get current price")
        
        print("\n" + "=" * 50)
        print("✅ CSV loader test completed!")
    
    asyncio.run(test_csv_loader())
