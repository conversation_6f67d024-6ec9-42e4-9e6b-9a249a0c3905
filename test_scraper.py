#!/usr/bin/env python3
"""
Test script for TradingView scrapers
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from scrapers.price_scraper import PriceScraper

def test_price_scraper():
    """Test the price scraper with COMI"""
    
    print("🧪 Testing TradingView Price Scraper...")
    print("=" * 50)
    
    try:
        # Test with PriceScraper
        scraper = PriceScraper(source="tradingview", use_real_time=True)
        
        # Test COMI
        print("📡 Fetching COMI price from TradingView...")
        price_data = scraper.get_price("COMI")
        
        print(f"✅ Result: {price_data}")
        print(f"💰 COMI Price: {price_data.get('price', 'N/A')} EGP")
        print(f"📊 Source: {price_data.get('source', 'N/A')}")
        print(f"⏰ Timestamp: {price_data.get('timestamp', 'N/A')}")
        
        # Close scraper
        scraper.close()
        
        return price_data
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    result = test_price_scraper()
    
    if result and result.get('price'):
        price = result['price']
        if 75 <= price <= 85:  # Expected range for COMI
            print(f"🎯 SUCCESS: Price {price} EGP is in expected range!")
        else:
            print(f"⚠️ WARNING: Price {price} EGP seems unusual for COMI")
    else:
        print("❌ FAILED: Could not get price data")
