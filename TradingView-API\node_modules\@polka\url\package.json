{"version": "1.0.0-next.21", "name": "@polka/url", "repository": "lukeed/polka", "description": "Super fast, memoized `req.url` parser", "module": "build.mjs", "types": "index.d.ts", "main": "build.js", "license": "MIT", "exports": {".": {"import": "./build.mjs", "require": "./build.js"}, "./package.json": "./package.json"}, "files": ["*.d.ts", "build.*"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://lukeed.com"}, "publishConfig": {"access": "public"}, "gitHead": "8d6e31871225f4449e645ecba13de7014f772b34"}