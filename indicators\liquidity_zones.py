"""
Advanced Liquidity Zones Detection for SMC Strategy
Identifies areas where institutional liquidity is likely to be resting
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class LiquidityType(Enum):
    """Types of liquidity zones"""
    BUY_SIDE = "buy_side"  # Above resistance, stops from shorts
    SELL_SIDE = "sell_side"  # Below support, stops from longs
    INTERNAL = "internal"  # Within range, both sides

@dataclass
class LiquidityZone:
    """Represents a liquidity zone"""
    high: float
    low: float
    center: float
    timestamp: int
    zone_type: LiquidityType
    strength: float  # 0-1 strength score
    volume_profile: float
    touches: int = 0
    swept: bool = False
    active: bool = True
    confluence_factors: List[str] = None

    def __post_init__(self):
        if self.confluence_factors is None:
            self.confluence_factors = []

def detect_liquidity_zones(df: pd.DataFrame, lookback: int = 50, min_strength: float = 0.4) -> List[LiquidityZone]:
    """
    Detect liquidity zones using multiple SMC concepts
    
    Args:
        df: OHLCV DataFrame
        lookback: Periods to analyze for zone detection
        min_strength: Minimum strength threshold
    
    Returns:
        List of LiquidityZone objects
    """
    if len(df) < lookback:
        return []
    
    zones = []
    
    # Detect different types of liquidity zones
    zones.extend(_detect_equal_highs_lows_liquidity(df, lookback))
    zones.extend(_detect_previous_day_highs_lows(df, lookback))
    zones.extend(_detect_volume_profile_zones(df, lookback))
    zones.extend(_detect_institutional_levels(df, lookback))
    zones.extend(_detect_gap_zones(df, lookback))
    
    # Filter by strength and remove duplicates
    zones = [zone for zone in zones if zone.strength >= min_strength]
    zones = _remove_overlapping_zones(zones)
    
    # Update zone status
    zones = _update_zone_status(df, zones)
    
    # Sort by strength and recency
    zones.sort(key=lambda x: (x.strength, -x.timestamp), reverse=True)
    
    return zones[:20]  # Return top 20 zones

def _detect_equal_highs_lows_liquidity(df: pd.DataFrame, lookback: int) -> List[LiquidityZone]:
    """Detect liquidity at equal highs and lows"""
    zones = []
    
    # Find equal highs (buy-side liquidity)
    highs = df['high'].rolling(window=5, center=True).max()
    equal_highs = []
    
    for i in range(lookback, len(df)):
        current_high = df.iloc[i]['high']
        
        # Look for equal highs in recent history
        recent_highs = highs.iloc[i-lookback:i]
        equal_count = sum(abs(h - current_high) / current_high < 0.002 for h in recent_highs if not pd.isna(h))
        
        if equal_count >= 2:  # At least 2 equal highs
            volume_at_level = df.iloc[i-5:i+1]['volume'].sum()
            avg_volume = df['volume'].rolling(20).mean().iloc[i]
            
            strength = min(equal_count / 5.0, 1.0) * 0.6 + min(volume_at_level / (avg_volume * 6), 1.0) * 0.4
            
            zone = LiquidityZone(
                high=current_high * 1.001,  # Small buffer above
                low=current_high * 0.999,   # Small buffer below
                center=current_high,
                timestamp=i,
                zone_type=LiquidityType.BUY_SIDE,
                strength=strength,
                volume_profile=volume_at_level,
                confluence_factors=["equal_highs", f"touches_{equal_count}"]
            )
            zones.append(zone)
    
    # Find equal lows (sell-side liquidity)
    lows = df['low'].rolling(window=5, center=True).min()
    
    for i in range(lookback, len(df)):
        current_low = df.iloc[i]['low']
        
        recent_lows = lows.iloc[i-lookback:i]
        equal_count = sum(abs(l - current_low) / current_low < 0.002 for l in recent_lows if not pd.isna(l))
        
        if equal_count >= 2:
            volume_at_level = df.iloc[i-5:i+1]['volume'].sum()
            avg_volume = df['volume'].rolling(20).mean().iloc[i]
            
            strength = min(equal_count / 5.0, 1.0) * 0.6 + min(volume_at_level / (avg_volume * 6), 1.0) * 0.4
            
            zone = LiquidityZone(
                high=current_low * 1.001,
                low=current_low * 0.999,
                center=current_low,
                timestamp=i,
                zone_type=LiquidityType.SELL_SIDE,
                strength=strength,
                volume_profile=volume_at_level,
                confluence_factors=["equal_lows", f"touches_{equal_count}"]
            )
            zones.append(zone)
    
    return zones

def _detect_previous_day_highs_lows(df: pd.DataFrame, lookback: int) -> List[LiquidityZone]:
    """Detect liquidity at previous day/session highs and lows"""
    zones = []
    
    # Assuming hourly data, look for daily highs/lows
    daily_periods = 24  # 24 hours = 1 day
    
    for i in range(daily_periods, len(df), daily_periods):
        if i + daily_periods > len(df):
            break
            
        # Get previous day's data
        prev_day = df.iloc[i-daily_periods:i]
        day_high = prev_day['high'].max()
        day_low = prev_day['low'].min()
        
        # Calculate volume and strength
        day_volume = prev_day['volume'].sum()
        avg_volume = df['volume'].rolling(daily_periods * 5).mean().iloc[i]
        
        volume_strength = min(day_volume / (avg_volume * daily_periods) if avg_volume > 0 else 0, 2.0) / 2.0
        
        # High liquidity zone
        high_zone = LiquidityZone(
            high=day_high * 1.002,
            low=day_high * 0.998,
            center=day_high,
            timestamp=i,
            zone_type=LiquidityType.BUY_SIDE,
            strength=0.7 + volume_strength * 0.3,
            volume_profile=day_volume,
            confluence_factors=["previous_day_high", "session_boundary"]
        )
        zones.append(high_zone)
        
        # Low liquidity zone
        low_zone = LiquidityZone(
            high=day_low * 1.002,
            low=day_low * 0.998,
            center=day_low,
            timestamp=i,
            zone_type=LiquidityType.SELL_SIDE,
            strength=0.7 + volume_strength * 0.3,
            volume_profile=day_volume,
            confluence_factors=["previous_day_low", "session_boundary"]
        )
        zones.append(low_zone)
    
    return zones

def _detect_volume_profile_zones(df: pd.DataFrame, lookback: int) -> List[LiquidityZone]:
    """Detect liquidity zones based on volume profile analysis"""
    zones = []
    
    # Create price bins for volume profile
    price_range = df['high'].max() - df['low'].min()
    num_bins = 50
    bin_size = price_range / num_bins
    
    # Calculate volume at each price level
    volume_profile = {}
    
    for i in range(len(df)):
        candle = df.iloc[i]
        # Distribute volume across the candle's price range
        candle_range = candle['high'] - candle['low']
        if candle_range > 0:
            volume_per_price = candle['volume'] / candle_range
            
            # Add volume to each price level in the candle
            price = candle['low']
            while price <= candle['high']:
                bin_key = int(price / bin_size) * bin_size
                volume_profile[bin_key] = volume_profile.get(bin_key, 0) + volume_per_price
                price += bin_size / 10  # Fine granularity
    
    # Find high volume nodes (potential liquidity zones)
    sorted_volumes = sorted(volume_profile.items(), key=lambda x: x[1], reverse=True)
    top_volume_levels = sorted_volumes[:10]  # Top 10 volume levels
    
    for price_level, volume in top_volume_levels:
        if volume > 0:
            strength = min(volume / max(v for _, v in sorted_volumes), 1.0)
            
            zone = LiquidityZone(
                high=price_level + bin_size,
                low=price_level,
                center=price_level + bin_size / 2,
                timestamp=len(df) - 1,  # Most recent
                zone_type=LiquidityType.INTERNAL,
                strength=strength,
                volume_profile=volume,
                confluence_factors=["high_volume_node", "volume_profile"]
            )
            zones.append(zone)
    
    return zones

def _detect_institutional_levels(df: pd.DataFrame, lookback: int) -> List[LiquidityZone]:
    """Detect institutional levels (round numbers, psychological levels)"""
    zones = []
    
    current_price = df['close'].iloc[-1]
    
    # Round number levels (00, 50 levels)
    base_price = int(current_price)
    
    for level in [base_price, base_price + 0.5, base_price + 1, base_price - 0.5, base_price - 1]:
        if level > 0:
            distance_pct = abs(current_price - level) / current_price
            
            if distance_pct <= 0.05:  # Within 5% of current price
                # Check historical significance
                touches = sum(1 for i in range(max(0, len(df)-lookback), len(df))
                            if abs(df.iloc[i]['high'] - level) / level < 0.001 or 
                               abs(df.iloc[i]['low'] - level) / level < 0.001)
                
                if touches >= 1:
                    strength = min(touches / 5.0, 1.0) * 0.8 + (1 - distance_pct * 20) * 0.2
                    
                    zone_type = LiquidityType.BUY_SIDE if level > current_price else LiquidityType.SELL_SIDE
                    
                    zone = LiquidityZone(
                        high=level * 1.001,
                        low=level * 0.999,
                        center=level,
                        timestamp=len(df) - 1,
                        zone_type=zone_type,
                        strength=strength,
                        volume_profile=0,  # Not volume-based
                        confluence_factors=["round_number", "psychological_level", f"touches_{touches}"]
                    )
                    zones.append(zone)
    
    return zones

def _detect_gap_zones(df: pd.DataFrame, lookback: int) -> List[LiquidityZone]:
    """Detect liquidity in gap areas"""
    zones = []
    
    for i in range(1, len(df)):
        prev_candle = df.iloc[i-1]
        curr_candle = df.iloc[i]
        
        # Gap up
        if curr_candle['low'] > prev_candle['high']:
            gap_size = curr_candle['low'] - prev_candle['high']
            gap_center = (curr_candle['low'] + prev_candle['high']) / 2
            
            strength = min(gap_size / prev_candle['high'], 0.05) / 0.05  # Normalize gap size
            
            zone = LiquidityZone(
                high=curr_candle['low'],
                low=prev_candle['high'],
                center=gap_center,
                timestamp=i,
                zone_type=LiquidityType.SELL_SIDE,  # Gap likely to be filled from above
                strength=strength,
                volume_profile=curr_candle['volume'],
                confluence_factors=["gap_zone", "unfilled_gap"]
            )
            zones.append(zone)
        
        # Gap down
        elif curr_candle['high'] < prev_candle['low']:
            gap_size = prev_candle['low'] - curr_candle['high']
            gap_center = (prev_candle['low'] + curr_candle['high']) / 2
            
            strength = min(gap_size / prev_candle['low'], 0.05) / 0.05
            
            zone = LiquidityZone(
                high=prev_candle['low'],
                low=curr_candle['high'],
                center=gap_center,
                timestamp=i,
                zone_type=LiquidityType.BUY_SIDE,  # Gap likely to be filled from below
                strength=strength,
                volume_profile=curr_candle['volume'],
                confluence_factors=["gap_zone", "unfilled_gap"]
            )
            zones.append(zone)
    
    return zones

def _remove_overlapping_zones(zones: List[LiquidityZone]) -> List[LiquidityZone]:
    """Remove overlapping zones, keeping the stronger ones"""
    if not zones:
        return zones
    
    # Sort by strength
    zones.sort(key=lambda x: x.strength, reverse=True)
    
    filtered_zones = []
    
    for zone in zones:
        overlapping = False
        
        for existing_zone in filtered_zones:
            # Check if zones overlap
            if (zone.low <= existing_zone.high and zone.high >= existing_zone.low):
                overlapping = True
                break
        
        if not overlapping:
            filtered_zones.append(zone)
    
    return filtered_zones

def _update_zone_status(df: pd.DataFrame, zones: List[LiquidityZone]) -> List[LiquidityZone]:
    """Update zone status based on price action"""
    
    for zone in zones:
        # Count touches and check if swept
        touches = 0
        swept = False
        
        # Check subsequent price action
        start_idx = zone.timestamp + 1
        
        for i in range(start_idx, len(df)):
            candle = df.iloc[i]
            
            # Check if price touched the zone
            if candle['low'] <= zone.high and candle['high'] >= zone.low:
                touches += 1
            
            # Check if zone was swept
            if zone.zone_type == LiquidityType.BUY_SIDE and candle['close'] > zone.high:
                swept = True
                break
            elif zone.zone_type == LiquidityType.SELL_SIDE and candle['close'] < zone.low:
                swept = True
                break
        
        zone.touches = touches
        zone.swept = swept
        zone.active = not swept
    
    return zones

def get_active_liquidity_zones(df: pd.DataFrame, current_price: float, max_distance_pct: float = 10.0) -> List[LiquidityZone]:
    """Get active liquidity zones near current price"""
    all_zones = detect_liquidity_zones(df)
    
    active_zones = []
    for zone in all_zones:
        if not zone.active:
            continue
            
        distance_pct = abs(current_price - zone.center) / current_price * 100
        
        if distance_pct <= max_distance_pct:
            active_zones.append(zone)
    
    return active_zones

def analyze_liquidity_confluence(zones: List[LiquidityZone], current_price: float) -> Dict:
    """Analyze confluence of liquidity zones"""
    if not zones:
        return {"confluence": False, "strength": 0, "direction": "neutral"}
    
    buy_side_zones = [z for z in zones if z.zone_type == LiquidityType.BUY_SIDE and z.active]
    sell_side_zones = [z for z in zones if z.zone_type == LiquidityType.SELL_SIDE and z.active]
    
    buy_side_strength = sum(z.strength for z in buy_side_zones)
    sell_side_strength = sum(z.strength for z in sell_side_zones)
    
    total_strength = buy_side_strength + sell_side_strength
    
    if total_strength == 0:
        return {"confluence": False, "strength": 0, "direction": "neutral"}
    
    # Determine dominant direction
    if buy_side_strength > sell_side_strength * 1.5:
        direction = "bullish"
        strength = buy_side_strength / total_strength
    elif sell_side_strength > buy_side_strength * 1.5:
        direction = "bearish"
        strength = sell_side_strength / total_strength
    else:
        direction = "neutral"
        strength = 0.5
    
    confluence = len(zones) >= 3 and total_strength > 2.0
    
    return {
        "confluence": confluence,
        "strength": strength,
        "direction": direction,
        "buy_side_zones": len(buy_side_zones),
        "sell_side_zones": len(sell_side_zones),
        "total_zones": len(zones),
        "nearest_zone": min(zones, key=lambda z: abs(current_price - z.center)) if zones else None
    }
