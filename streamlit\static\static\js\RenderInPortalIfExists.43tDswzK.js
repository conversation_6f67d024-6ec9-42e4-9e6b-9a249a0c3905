import{n as i,r as s,j as r,a as d,P as m,b as p,F as P}from"./index.BqDl3eRM.js";const x=i("div",{target:"e1swy67l0"})({"@media print":{display:"none"}}),y=({className:t,scriptRunId:a,numParticles:e,numParticleTypes:o,ParticleComponent:n})=>r(x,{className:t,"data-testid":t,children:d(e).map(l=>{const c=Math.floor(Math.random()*o);return r(n,{particleType:c},a+l)})}),E=s.memo(y),f=({children:t})=>{var e;const a=(e=s.useContext(m))==null?void 0:e();return a?p.createPortal(t,a):r(P,{children:t})};export{E as P,f as R};
