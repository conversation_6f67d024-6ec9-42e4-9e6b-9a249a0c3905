"""
Order Blocks Detection for SMC Strategy
Identifies institutional order blocks - key support/resistance levels where smart money placed orders
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional
from dataclasses import dataclass

@dataclass
class OrderBlock:
    """Represents an order block zone"""
    high: float
    low: float
    timestamp: int
    block_type: str  # 'bullish' or 'bearish'
    strength: float  # 0-1 strength score
    tested: bool = False
    broken: bool = False
    volume: float = 0.0

def detect_order_blocks(df: pd.DataFrame, lookback: int = 20, min_strength: float = 0.3) -> List[OrderBlock]:
    """
    Detect order blocks in price data
    
    Args:
        df: OHLCV DataFrame
        lookback: Number of periods to look back for swing points
        min_strength: Minimum strength threshold for valid order blocks
    
    Returns:
        List of OrderBlock objects
    """
    if len(df) < lookback * 2:
        return []
    
    order_blocks = []
    
    # Find swing highs and lows
    swing_highs = find_swing_points(df, 'high', lookback)
    swing_lows = find_swing_points(df, 'low', lookback)
    
    # Detect bullish order blocks (from swing lows)
    for idx, low_price in swing_lows.items():
        if idx < lookback or idx >= len(df) - 5:
            continue
            
        # Look for the candle that created the swing low
        swing_candle = df.iloc[idx]
        
        # Check for strong rejection (long wick or strong volume)
        body_size = abs(swing_candle['close'] - swing_candle['open'])
        lower_wick = swing_candle['open'] - swing_candle['low'] if swing_candle['close'] > swing_candle['open'] else swing_candle['close'] - swing_candle['low']
        
        # Order block criteria
        if lower_wick > body_size * 0.5:  # Significant lower wick
            # Define order block zone
            ob_high = max(swing_candle['open'], swing_candle['close'])
            ob_low = swing_candle['low']
            
            # Calculate strength based on volume and price action
            volume_strength = min(swing_candle.get('volume', 0) / df['volume'].rolling(20).mean().iloc[idx], 2.0) / 2.0
            wick_strength = min(lower_wick / body_size, 3.0) / 3.0
            strength = (volume_strength + wick_strength) / 2
            
            if strength >= min_strength:
                order_blocks.append(OrderBlock(
                    high=ob_high,
                    low=ob_low,
                    timestamp=idx,
                    block_type='bullish',
                    strength=strength,
                    volume=swing_candle.get('volume', 0)
                ))
    
    # Detect bearish order blocks (from swing highs)
    for idx, high_price in swing_highs.items():
        if idx < lookback or idx >= len(df) - 5:
            continue
            
        swing_candle = df.iloc[idx]
        
        body_size = abs(swing_candle['close'] - swing_candle['open'])
        upper_wick = swing_candle['high'] - swing_candle['open'] if swing_candle['close'] < swing_candle['open'] else swing_candle['high'] - swing_candle['close']
        
        if upper_wick > body_size * 0.5:  # Significant upper wick
            ob_high = swing_candle['high']
            ob_low = min(swing_candle['open'], swing_candle['close'])
            
            volume_strength = min(swing_candle.get('volume', 0) / df['volume'].rolling(20).mean().iloc[idx], 2.0) / 2.0
            wick_strength = min(upper_wick / body_size, 3.0) / 3.0
            strength = (volume_strength + wick_strength) / 2
            
            if strength >= min_strength:
                order_blocks.append(OrderBlock(
                    high=ob_high,
                    low=ob_low,
                    timestamp=idx,
                    block_type='bearish',
                    strength=strength,
                    volume=swing_candle.get('volume', 0)
                ))
    
    # Update order block status (tested/broken)
    order_blocks = update_order_block_status(df, order_blocks)
    
    # Sort by timestamp and return most recent
    order_blocks.sort(key=lambda x: x.timestamp, reverse=True)
    return order_blocks[:10]  # Return top 10 most recent

def find_swing_points(df: pd.DataFrame, price_type: str, lookback: int) -> Dict[int, float]:
    """Find swing highs or lows"""
    swing_points = {}
    
    for i in range(lookback, len(df) - lookback):
        if price_type == 'high':
            # Check if current high is higher than surrounding highs
            current = df.iloc[i]['high']
            left_max = df.iloc[i-lookback:i]['high'].max()
            right_max = df.iloc[i+1:i+lookback+1]['high'].max()
            
            if current > left_max and current > right_max:
                swing_points[i] = current
                
        elif price_type == 'low':
            # Check if current low is lower than surrounding lows
            current = df.iloc[i]['low']
            left_min = df.iloc[i-lookback:i]['low'].min()
            right_min = df.iloc[i+1:i+lookback+1]['low'].min()
            
            if current < left_min and current < right_min:
                swing_points[i] = current
    
    return swing_points

def update_order_block_status(df: pd.DataFrame, order_blocks: List[OrderBlock]) -> List[OrderBlock]:
    """Update whether order blocks have been tested or broken"""
    
    for ob in order_blocks:
        # Check subsequent price action after the order block formation
        start_idx = ob.timestamp + 1
        
        if start_idx >= len(df):
            continue
            
        subsequent_data = df.iloc[start_idx:]
        
        for idx, candle in subsequent_data.iterrows():
            if ob.block_type == 'bullish':
                # Check if price tested the order block (came back to the zone)
                if candle['low'] <= ob.high and candle['high'] >= ob.low:
                    ob.tested = True
                
                # Check if order block was broken (price closed below)
                if candle['close'] < ob.low:
                    ob.broken = True
                    break
                    
            elif ob.block_type == 'bearish':
                # Check if price tested the order block
                if candle['high'] >= ob.low and candle['low'] <= ob.high:
                    ob.tested = True
                
                # Check if order block was broken (price closed above)
                if candle['close'] > ob.high:
                    ob.broken = True
                    break
    
    return order_blocks

def get_active_order_blocks(df: pd.DataFrame, current_price: float, max_distance_pct: float = 5.0) -> List[OrderBlock]:
    """Get order blocks that are still active and near current price"""
    all_blocks = detect_order_blocks(df)
    
    active_blocks = []
    for ob in all_blocks:
        if ob.broken:
            continue
            
        # Check if order block is within reasonable distance from current price
        ob_center = (ob.high + ob.low) / 2
        distance_pct = abs(current_price - ob_center) / current_price * 100
        
        if distance_pct <= max_distance_pct:
            active_blocks.append(ob)
    
    return active_blocks

def analyze_order_block_confluence(order_blocks: List[OrderBlock], current_price: float) -> Dict:
    """Analyze confluence of multiple order blocks"""
    if not order_blocks:
        return {"confluence": False, "strength": 0, "direction": "neutral"}
    
    bullish_blocks = [ob for ob in order_blocks if ob.block_type == 'bullish' and not ob.broken]
    bearish_blocks = [ob for ob in order_blocks if ob.block_type == 'bearish' and not ob.broken]
    
    bullish_strength = sum(ob.strength for ob in bullish_blocks)
    bearish_strength = sum(ob.strength for ob in bearish_blocks)
    
    total_strength = bullish_strength + bearish_strength
    
    if total_strength == 0:
        return {"confluence": False, "strength": 0, "direction": "neutral"}
    
    # Determine dominant direction
    if bullish_strength > bearish_strength * 1.5:
        direction = "bullish"
        strength = bullish_strength / total_strength
    elif bearish_strength > bullish_strength * 1.5:
        direction = "bearish"
        strength = bearish_strength / total_strength
    else:
        direction = "neutral"
        strength = 0.5
    
    confluence = len(order_blocks) >= 2 and total_strength > 1.0
    
    return {
        "confluence": confluence,
        "strength": strength,
        "direction": direction,
        "bullish_blocks": len(bullish_blocks),
        "bearish_blocks": len(bearish_blocks),
        "total_blocks": len(order_blocks)
    }
