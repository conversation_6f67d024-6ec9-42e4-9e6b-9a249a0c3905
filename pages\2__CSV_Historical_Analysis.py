#!/usr/bin/env python3
"""
🇪🇬 EGX Professional Smart Money Concepts Dashboard
Advanced SMC Trading Platform for Egyptian Exchange
"""

import streamlit as st

# Professional page configuration - MUST BE FIRST
st.set_page_config(
    page_title="📁 CSV Historical Analysis",
    page_icon="�",
    layout="wide",
    initial_sidebar_state="expanded"
)

import pandas as pd
import numpy as np
import os
import json
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import sys

# Add project root to path
sys.path.append('.')

# Import TradingView data fetcher
TRADINGVIEW_AVAILABLE = False  # Temporarily disabled due to dependency issues
# TODO: Re-enable when tvdatafeed dependencies are resolved

def get_tradingview_data(symbol: str, timeframe: str = '1D', bars: int = 500):
    """Placeholder for TradingView data fetching"""
    st.error("🚧 TradingView integration temporarily unavailable")
    return None

def test_tradingview_connection():
    """Placeholder for TradingView connection test"""
    return {'connected': False, 'error': 'TradingView integration temporarily disabled'}

class MockTVFetcher:
    def get_available_symbols(self):
        return ['CIB', 'COMI', 'ADIB', 'ALEX', 'SAIB', 'ETEL', 'ORTE', 'PHDC', 'SODIC', 'TMG']

tv_fetcher = MockTVFetcher()

# Import SMC modules
try:
    from indicators.market_structure import detect_market_structure
    from indicators.order_blocks import detect_order_blocks, get_active_order_blocks, analyze_order_block_confluence
    from indicators.fvg import detect_fvg, get_active_fvgs, analyze_fvg_confluence
    from indicators.liquidity_zones import detect_liquidity_zones, get_active_liquidity_zones, analyze_liquidity_confluence
    from indicators.displacement import detect_displacement, get_recent_displacements, analyze_displacement_confluence
    from indicators.eqh_eql import find_equal_highs_lows
    from indicators.candlestick_patterns import detect_candlestick_patterns
    from indicators.indicators import get_indicators
    from egx_smc_parameters import get_egx_parameters
except ImportError as e:
    st.error(f"❌ Error importing SMC modules: {e}")
    st.stop()

# Custom CSS for professional styling
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;
        margin: 0.5rem 0;
    }
    .signal-strong { border-left-color: #28a745 !important; }
    .signal-moderate { border-left-color: #ffc107 !important; }
    .signal-weak { border-left-color: #dc3545 !important; }
    .smc-section {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def load_csv_data(file_path: str) -> Optional[pd.DataFrame]:
    """Load and validate CSV data"""
    try:
        df = pd.read_csv(file_path)
        required_columns = ['open', 'high', 'low', 'close', 'volume']

        if not all(col in df.columns for col in required_columns):
            st.error(f"❌ Missing required columns in {file_path}")
            return None

        # Convert to numeric and handle any issues
        for col in required_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # Remove any rows with NaN values
        df = df.dropna()

        if len(df) < 50:
            st.warning(f"⚠️ Insufficient data in {file_path} (need at least 50 candles)")
            return None

        return df

    except Exception as e:
        st.error(f"❌ Error loading {file_path}: {e}")
        return None

def get_signal_strength_color(strength: float) -> str:
    """Get color based on signal strength"""
    if strength >= 0.7:
        return "🟢"
    elif strength >= 0.4:
        return "🟡"
    else:
        return "🔴"

def get_trend_emoji(trend: str) -> str:
    """Get emoji for trend direction"""
    if trend == "bullish":
        return "📈"
    elif trend == "bearish":
        return "📉"
    else:
        return "➡️"

def analyze_order_block_confluence_egx(order_blocks, current_price: float, egx_params: Dict) -> Dict:
    """EGX-optimized order block confluence analysis"""
    if not order_blocks:
        return {"confluence": False, "strength": 0, "direction": "neutral"}

    bullish_blocks = [ob for ob in order_blocks if ob.block_type == 'bullish' and not ob.broken]
    bearish_blocks = [ob for ob in order_blocks if ob.block_type == 'bearish' and not ob.broken]

    bullish_strength = sum(ob.strength for ob in bullish_blocks)
    bearish_strength = sum(ob.strength for ob in bearish_blocks)
    total_strength = bullish_strength + bearish_strength

    if total_strength == 0:
        return {"confluence": False, "strength": 0, "direction": "neutral"}

    # EGX-optimized confluence criteria
    min_blocks = egx_params['order_blocks']['confluence_min_blocks']
    min_strength = egx_params['order_blocks']['confluence_min_strength']

    confluence = len(order_blocks) >= min_blocks and total_strength >= min_strength

    # Determine direction
    if bullish_strength > bearish_strength * 1.2:
        direction = "bullish"
        strength = bullish_strength / total_strength
    elif bearish_strength > bullish_strength * 1.2:
        direction = "bearish"
        strength = bearish_strength / total_strength
    else:
        direction = "neutral"
        strength = 0.5

    return {
        "confluence": confluence,
        "strength": strength,
        "direction": direction,
        "bullish_blocks": len(bullish_blocks),
        "bearish_blocks": len(bearish_blocks),
        "total_blocks": len(order_blocks)
    }

def analyze_fvg_confluence_egx(fvgs, current_price: float, egx_params: Dict) -> Dict:
    """EGX-optimized FVG confluence analysis"""
    if not fvgs:
        return {"confluence": False, "strength": 0, "direction": "neutral"}

    bullish_fvgs = [fvg for fvg in fvgs if fvg.gap_type == 'bullish' and not fvg.filled]
    bearish_fvgs = [fvg for fvg in fvgs if fvg.gap_type == 'bearish' and not fvg.filled]

    bullish_strength = sum(fvg.strength for fvg in bullish_fvgs)
    bearish_strength = sum(fvg.strength for fvg in bearish_fvgs)
    total_strength = bullish_strength + bearish_strength

    if total_strength == 0:
        return {"confluence": False, "strength": 0, "direction": "neutral"}

    # EGX-optimized confluence criteria
    min_fvgs = egx_params['fvg']['confluence_min_fvgs']
    min_strength = egx_params['fvg']['confluence_min_strength']

    confluence = len(fvgs) >= min_fvgs and total_strength >= min_strength

    # Determine direction
    if bullish_strength > bearish_strength * 1.1:
        direction = "bullish"
        strength = bullish_strength / total_strength
    elif bearish_strength > bullish_strength * 1.1:
        direction = "bearish"
        strength = bearish_strength / total_strength
    else:
        direction = "neutral"
        strength = 0.5

    return {
        "confluence": confluence,
        "strength": strength,
        "direction": direction,
        "bullish_fvgs": len(bullish_fvgs),
        "bearish_fvgs": len(bearish_fvgs),
        "total_fvgs": len(fvgs)
    }

def analyze_liquidity_confluence_egx(zones, current_price: float, egx_params: Dict) -> Dict:
    """EGX-optimized liquidity confluence analysis"""
    if not zones:
        return {"confluence": False, "strength": 0, "direction": "neutral"}

    buy_side_zones = [z for z in zones if hasattr(z, 'zone_type') and str(z.zone_type).upper() == 'BUY_SIDE' and z.active]
    sell_side_zones = [z for z in zones if hasattr(z, 'zone_type') and str(z.zone_type).upper() == 'SELL_SIDE' and z.active]

    buy_side_strength = sum(z.strength for z in buy_side_zones)
    sell_side_strength = sum(z.strength for z in sell_side_zones)
    total_strength = buy_side_strength + sell_side_strength

    if total_strength == 0:
        return {"confluence": False, "strength": 0, "direction": "neutral"}

    # EGX-optimized confluence criteria
    min_zones = egx_params['liquidity_zones']['confluence_min_zones']
    min_strength = egx_params['liquidity_zones']['confluence_min_strength']

    confluence = len(zones) >= min_zones and total_strength >= min_strength

    # Determine direction
    if buy_side_strength > sell_side_strength * 1.3:
        direction = "bullish"
        strength = buy_side_strength / total_strength
    elif sell_side_strength > buy_side_strength * 1.3:
        direction = "bearish"
        strength = sell_side_strength / total_strength
    else:
        direction = "neutral"
        strength = 0.5

    return {
        "confluence": confluence,
        "strength": strength,
        "direction": direction,
        "buy_side_zones": len(buy_side_zones),
        "sell_side_zones": len(sell_side_zones),
        "total_zones": len(zones)
    }

def analyze_displacement_confluence_egx(displacements, current_price: float, egx_params: Dict) -> Dict:
    """EGX-optimized displacement confluence analysis"""
    if not displacements:
        return {"confluence": False, "strength": 0, "direction": "neutral"}

    bullish_displacements = [d for d in displacements if hasattr(d, 'displacement_type') and str(d.displacement_type).upper() == 'BULLISH']
    bearish_displacements = [d for d in displacements if hasattr(d, 'displacement_type') and str(d.displacement_type).upper() == 'BEARISH']

    bullish_strength = sum(d.strength for d in bullish_displacements)
    bearish_strength = sum(d.strength for d in bearish_displacements)
    total_strength = bullish_strength + bearish_strength

    if total_strength == 0:
        return {"confluence": False, "strength": 0, "direction": "neutral"}

    # EGX-optimized confluence criteria
    min_displacements = egx_params['displacement']['confluence_min_displacements']
    min_strength = egx_params['displacement']['confluence_min_strength']

    confluence = len(displacements) >= min_displacements and total_strength >= min_strength

    # Determine direction
    if bullish_strength > bearish_strength * 1.2:
        direction = "bullish"
        strength = bullish_strength / total_strength
    elif bearish_strength > bullish_strength * 1.2:
        direction = "bearish"
        strength = bearish_strength / total_strength
    else:
        direction = "neutral"
        strength = 0.5

    return {
        "confluence": confluence,
        "strength": strength,
        "direction": direction,
        "bullish_displacements": len(bullish_displacements),
        "bearish_displacements": len(bearish_displacements),
        "total_displacements": len(displacements)
    }

def run_comprehensive_smc_analysis(df: pd.DataFrame, symbol: str, use_egx_params: bool = True) -> Dict:
    """Run complete SMC analysis with all indicators"""

    current_price = float(df['close'].iloc[-1])

    # Get EGX-optimized parameters
    if use_egx_params:
        egx_params = get_egx_parameters()
        st.info("🇪🇬 Using EGX-optimized parameters for better confluence detection")
    else:
        egx_params = None

    # === COMPREHENSIVE SMC ANALYSIS ===

    # 1. Market Structure Analysis
    market_structure = detect_market_structure(df)

    # 2. Order Blocks Analysis (with EGX parameters)
    if egx_params:
        # Use EGX-optimized parameters
        all_order_blocks = detect_order_blocks(
            df,
            lookback=egx_params['order_blocks']['lookback'],
            min_strength=egx_params['order_blocks']['min_strength']
        )
        order_blocks = get_active_order_blocks(df, current_price, max_distance_pct=egx_params['distance_filters']['order_blocks_pct'])
        # Apply EGX confluence criteria
        ob_confluence = analyze_order_block_confluence_egx(order_blocks, current_price, egx_params)
    else:
        order_blocks = get_active_order_blocks(df, current_price, max_distance_pct=5.0)
        ob_confluence = analyze_order_block_confluence(order_blocks, current_price)

    # 3. Fair Value Gaps Analysis (with EGX parameters)
    if egx_params:
        all_fvgs = detect_fvg(df, min_gap_size=egx_params['fvg']['min_gap_size'])
        fvgs = get_active_fvgs(df, current_price, max_distance_pct=egx_params['distance_filters']['fvg_pct'])
        fvg_confluence = analyze_fvg_confluence_egx(fvgs, current_price, egx_params)
    else:
        fvgs = get_active_fvgs(df, current_price, max_distance_pct=3.0)
        fvg_confluence = analyze_fvg_confluence(fvgs, current_price)

    # 4. Liquidity Zones Analysis (with EGX parameters)
    if egx_params:
        all_liquidity_zones = detect_liquidity_zones(df, lookback=egx_params['liquidity_zones']['lookback'])
        liquidity_zones = get_active_liquidity_zones(df, current_price, max_distance_pct=egx_params['distance_filters']['liquidity_zones_pct'])
        liq_confluence = analyze_liquidity_confluence_egx(liquidity_zones, current_price, egx_params)
    else:
        liquidity_zones = get_active_liquidity_zones(df, current_price, max_distance_pct=8.0)
        liq_confluence = analyze_liquidity_confluence(liquidity_zones, current_price)

    # 5. Displacement Analysis (with EGX parameters)
    if egx_params:
        all_displacements = detect_displacement(
            df,
            min_displacement_pct=egx_params['displacement']['min_displacement_pct'],
            min_strength=egx_params['displacement']['min_strength']
        )
        displacements = get_recent_displacements(df, lookback_periods=30)
        disp_confluence = analyze_displacement_confluence_egx(displacements, current_price, egx_params)
    else:
        displacements = get_recent_displacements(df, lookback_periods=30)
        disp_confluence = analyze_displacement_confluence(displacements, current_price)

    # 6. Equal Highs/Lows Analysis
    eqh_eql = find_equal_highs_lows(df)

    # 7. Candlestick Patterns
    patterns = detect_candlestick_patterns(df)

    # 8. Technical Indicators
    indicators = get_indicators(df)

    # Calculate overall confluence
    confluence_factors = [
        ob_confluence.get("confluence", False),
        fvg_confluence.get("confluence", False),
        liq_confluence.get("confluence", False),
        disp_confluence.get("confluence", False)
    ]

    active_factors = sum(confluence_factors)
    confluence_strength = active_factors / 4

    # Determine signal strength
    if confluence_strength >= 0.75:
        signal_strength = "VERY STRONG"
    elif confluence_strength >= 0.5:
        signal_strength = "STRONG"
    elif confluence_strength >= 0.25:
        signal_strength = "MODERATE"
    else:
        signal_strength = "WEAK"

    return {
        'symbol': symbol,
        'current_price': current_price,
        'market_structure': market_structure,
        'order_blocks': {
            'blocks': order_blocks,
            'confluence': ob_confluence,
            'count': len(order_blocks)
        },
        'fair_value_gaps': {
            'gaps': fvgs,
            'confluence': fvg_confluence,
            'count': len(fvgs)
        },
        'liquidity_zones': {
            'zones': liquidity_zones,
            'confluence': liq_confluence,
            'count': len(liquidity_zones)
        },
        'displacements': {
            'moves': displacements,
            'confluence': disp_confluence,
            'count': len(displacements)
        },
        'equal_highs_lows': eqh_eql,
        'candlestick_patterns': patterns,
        'technical_indicators': indicators,
        'confluence_summary': {
            'total_factors': active_factors,
            'max_factors': 4,
            'strength': confluence_strength,
            'signal_strength': signal_strength,
            'factors': confluence_factors
        }
    }

def create_price_chart(df: pd.DataFrame, symbol: str, analysis: Dict) -> go.Figure:
    """Create an interactive price chart with SMC levels"""
    fig = go.Figure()
    
    # Add candlestick chart
    fig.add_trace(go.Candlestick(
        x=df.index,
        open=df['open'],
        high=df['high'],
        low=df['low'],
        close=df['close'],
        name=symbol,
        increasing_line_color='#26a69a',
        decreasing_line_color='#ef5350'
    ))
    
    # Add Order Blocks
    for ob in analysis['order_blocks']['blocks']:
        fig.add_hline(
            y=ob.high,
            line_dash="dash",
            line_color="blue",
            annotation_text=f"OB High: {ob.high:.2f}",
            annotation_position="bottom right"
        )
        fig.add_hline(
            y=ob.low,
            line_dash="dash",
            line_color="blue",
            annotation_text=f"OB Low: {ob.low:.2f}",
            annotation_position="top right"
        )
    
    # Add FVGs
    for fvg in analysis['fair_value_gaps']['gaps']:
        fig.add_hrect(
            y0=fvg.low,
            y1=fvg.high,
            fillcolor="yellow",
            opacity=0.2,
            line_width=0
        )
        # Add annotation separately to avoid position errors
        fig.add_annotation(
            x=df.index[len(df)//2],
            y=(fvg.low + fvg.high) / 2,
            text=f"FVG: {fvg.gap_type}",
            showarrow=False,
            font=dict(color="black", size=10),
            bgcolor="rgba(255,255,0,0.7)",
            bordercolor="orange",
            borderwidth=1
        )
    
    # Add Liquidity Zones
    for zone in analysis['liquidity_zones']['zones']:
        fig.add_hrect(
            y0=zone.low,
            y1=zone.high,
            fillcolor="purple",
            opacity=0.1,
            line_width=1,
            line_color="purple"
        )
        # Add annotation separately to avoid position errors
        fig.add_annotation(
            x=df.index[len(df)*2//3],
            y=(zone.low + zone.high) / 2,
            text=f"LZ: {zone.zone_type}",
            showarrow=False,
            font=dict(color="purple", size=10),
            bgcolor="rgba(128,0,128,0.2)",
            bordercolor="purple",
            borderwidth=1
        )
    
    fig.update_layout(
        title=f"{symbol} - SMC Analysis",
        xaxis_title="Time",
        yaxis_title="Price",
        height=600,
        showlegend=True
    )
    
    return fig

def main():
    """Main dashboard application"""

    # Header
    st.markdown("""
    <div class="main-header">
        <h1>📁 EGX Historical CSV Analysis</h1>
        <p>Smart Money Concepts Analysis using Historical CSV Data</p>
    </div>
    """, unsafe_allow_html=True)

    # Navigation info
    st.info("📡 **Want live TradingView data?** Check out the **TradingView Live Analysis** page in the sidebar!")

    # Sidebar configuration
    with st.sidebar:
        st.header("🎛️ Dashboard Controls")

        # Data source selection
        st.subheader("📡 Data Source")

        if TRADINGVIEW_AVAILABLE:
            data_source = st.radio(
                "Choose Data Source:",
                ["📡 TradingView (Live)", "📁 CSV Files (Historical)"],
                index=0
            )
            use_tradingview = data_source.startswith("📡")
        else:
            st.warning("⚠️ TradingView not available, using CSV files")
            use_tradingview = False

        if use_tradingview and TRADINGVIEW_AVAILABLE:
            # TradingView data source
            st.markdown("**🔗 Live TradingView Data**")

            # Test connection
            if st.button("🔍 Test TradingView Connection"):
                with st.spinner("Testing connection..."):
                    test_result = test_tradingview_connection()
                    if test_result['connected'] and test_result['test_successful']:
                        st.success(f"✅ Connected! Got {test_result['data_points']} data points for {test_result['test_symbol']}")
                    else:
                        st.error(f"❌ Connection failed: {test_result.get('error', 'Unknown error')}")

            # Stock selection from available symbols
            available_symbols = tv_fetcher.get_available_symbols()
            symbol = st.selectbox("📈 Select EGX Stock", available_symbols)

            # Timeframe selection
            timeframe = st.selectbox(
                "⏰ Timeframe",
                ["1D", "4h", "1h", "30m", "15m"],
                index=0
            )

            # Number of bars
            bars = st.slider("📊 Number of Bars", 100, 1000, 500, 50)

        else:
            # CSV data source (fallback)
            data_dir = st.text_input("📁 Data Directory", value="data/historical")

            if not os.path.exists(data_dir):
                st.error(f"❌ Directory {data_dir} not found")
                return

            # Get available CSV files (EGX stocks only)
            csv_files = [f for f in os.listdir(data_dir)
                        if f.endswith('.csv') and not any(crypto in f.upper()
                        for crypto in ['USDT', 'BTC', 'ETH', 'BNB', 'CRYPTO'])]

            if not csv_files:
                st.error("❌ No EGX stock CSV files found")
                return

            # Stock selection
            selected_file = st.selectbox("📈 Select EGX Stock", csv_files)
            symbol = selected_file.replace('.csv', '').upper()
            timeframe = "1D"  # Default for CSV

        # Analysis parameters
        st.subheader("⚙️ SMC Parameters")
        ob_distance = st.slider("Order Blocks Distance %", 1.0, 10.0, 5.0, 0.5)
        fvg_distance = st.slider("FVG Distance %", 1.0, 5.0, 3.0, 0.5)
        liq_distance = st.slider("Liquidity Zones Distance %", 3.0, 15.0, 8.0, 0.5)

        # Analysis trigger
        run_analysis = st.button("🚀 Run Professional SMC Analysis", type="primary")

    if run_analysis and symbol:
        with st.spinner(f"🔍 Loading and analyzing {symbol}..."):

            # Load data based on source
            if use_tradingview and TRADINGVIEW_AVAILABLE:
                # Fetch live TradingView data
                st.info(f"📡 Fetching live data from TradingView for {symbol} ({timeframe})")
                df = get_tradingview_data(symbol, timeframe, bars)

                if df is None:
                    st.error(f"❌ Failed to fetch TradingView data for {symbol}")
                    st.info("💡 Try using CSV files as fallback or check symbol availability")
                    return

                st.success(f"✅ Successfully fetched {len(df)} bars from TradingView")

            else:
                # Load CSV data (fallback)
                file_path = os.path.join(data_dir, selected_file)
                df = load_csv_data(file_path)

                if df is None:
                    return

            # Run comprehensive analysis
            analysis = run_comprehensive_smc_analysis(df, symbol)

            # Display results in professional layout
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric(
                    "💰 Current Price",
                    f"{analysis['current_price']:.2f} EGP",
                    delta=f"{((analysis['current_price'] - df['close'].iloc[-2]) / df['close'].iloc[-2] * 100):.2f}%"
                )

            with col2:
                trend = analysis['market_structure']['trend']
                st.metric(
                    "📊 Market Trend",
                    f"{get_trend_emoji(trend)} {trend.upper()}",
                    delta=analysis['market_structure'].get('event', 'No Event')
                )

            with col3:
                confluence = analysis['confluence_summary']
                st.metric(
                    "⚡ Signal Strength",
                    f"{get_signal_strength_color(confluence['strength'])} {confluence['signal_strength']}",
                    delta=f"{confluence['total_factors']}/4 factors"
                )

            with col4:
                st.metric(
                    "📊 Data Points",
                    f"{len(df)}",
                    delta="Historical Candles"
                )

            # Professional chart
            st.plotly_chart(
                create_price_chart(df, symbol, analysis),
                use_container_width=True
            )

            # === COMPREHENSIVE SMC ANALYSIS SECTIONS ===

            # Row 1: Order Blocks & Fair Value Gaps
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("""
                <div class="smc-section">
                    <h3>🔲 Order Blocks Analysis</h3>
                </div>
                """, unsafe_allow_html=True)

                ob_data = analysis['order_blocks']
                confluence_status = "✅ STRONG" if ob_data['confluence'].get('confluence', False) else "❌ WEAK"
                st.markdown(f"**Active Order Blocks:** `{ob_data['count']}`")
                st.markdown(f"**Confluence Status:** {confluence_status}")

                if ob_data['blocks']:
                    st.markdown("**📋 Active Order Blocks:**")
                    for i, ob in enumerate(ob_data['blocks'][:3]):
                        strength_bar = "🟢" * int(ob.strength * 5) + "⚪" * (5 - int(ob.strength * 5))
                        st.markdown(f"**OB-{i+1}:** `{ob.block_type.upper()}` | `{ob.low:.2f} - {ob.high:.2f}` EGP")
                        st.markdown(f"   Strength: {strength_bar} `{ob.strength:.2f}`")
                else:
                    st.info("No active order blocks near current price")

            with col2:
                st.markdown("""
                <div class="smc-section">
                    <h3>⚡ Fair Value Gaps</h3>
                </div>
                """, unsafe_allow_html=True)

                fvg_data = analysis['fair_value_gaps']
                confluence_status = "✅ STRONG" if fvg_data['confluence'].get('confluence', False) else "❌ WEAK"
                st.markdown(f"**Active FVGs:** `{fvg_data['count']}`")
                st.markdown(f"**Confluence Status:** {confluence_status}")

                if fvg_data['gaps']:
                    st.markdown("**📋 Active Fair Value Gaps:**")
                    for i, fvg in enumerate(fvg_data['gaps'][:3]):
                        strength_bar = "🟡" * int(fvg.strength * 5) + "⚪" * (5 - int(fvg.strength * 5))
                        filled_status = "🔴 FILLED" if fvg.filled else "🟢 OPEN"
                        st.markdown(f"**FVG-{i+1}:** `{fvg.gap_type.upper()}` | `{fvg.low:.2f} - {fvg.high:.2f}` EGP")
                        st.markdown(f"   Status: {filled_status} | Strength: {strength_bar} `{fvg.strength:.2f}`")
                else:
                    st.info("No active fair value gaps near current price")

            # === TRADING SIGNALS SECTION ===
            st.markdown("""
            <div class="smc-section">
                <h2>🎯 Trading Signals & Recommendations</h2>
            </div>
            """, unsafe_allow_html=True)

            confluence = analysis['confluence_summary']

            col7, col8, col9 = st.columns(3)

            with col7:
                st.markdown("### 📊 Confluence Analysis")
                st.markdown(f"**Overall Strength:** {get_signal_strength_color(confluence['strength'])} `{confluence['signal_strength']}`")
                st.markdown(f"**Active Factors:** `{confluence['total_factors']}/4`")

                factor_names = ["Order Blocks", "Fair Value Gaps", "Liquidity Zones", "Displacements"]
                for i, (factor, active) in enumerate(zip(factor_names, confluence['factors'])):
                    status = "✅" if active else "❌"
                    st.markdown(f"   {status} {factor}")

            with col8:
                st.markdown("### 🎯 Entry Signals")

                # Generate trading signals based on confluence
                if confluence['strength'] >= 0.5:
                    trend = analysis['market_structure']['trend']
                    if trend == "bullish":
                        st.success("🟢 **BULLISH SIGNAL DETECTED**")
                        st.markdown("**Recommended Action:** Consider LONG position")
                    elif trend == "bearish":
                        st.error("🔴 **BEARISH SIGNAL DETECTED**")
                        st.markdown("**Recommended Action:** Consider SHORT position")
                    else:
                        st.warning("🟡 **CONSOLIDATION**")
                        st.markdown("**Recommended Action:** Wait for breakout")
                else:
                    st.info("⚪ **NO CLEAR SIGNAL**")
                    st.markdown("**Recommended Action:** Wait for better setup")

            with col9:
                st.markdown("### ⚠️ Risk Management")

                current_price = analysis['current_price']

                # Calculate potential support/resistance levels
                support_levels = []
                resistance_levels = []

                # Add order block levels
                for ob in analysis['order_blocks']['blocks']:
                    if ob.block_type == "bullish" and ob.high < current_price:
                        support_levels.append(ob.high)
                    elif ob.block_type == "bearish" and ob.low > current_price:
                        resistance_levels.append(ob.low)

                # Add FVG levels
                for fvg in analysis['fair_value_gaps']['gaps']:
                    if fvg.gap_type == "bullish" and fvg.high < current_price:
                        support_levels.append(fvg.high)
                    elif fvg.gap_type == "bearish" and fvg.low > current_price:
                        resistance_levels.append(fvg.low)

                # Show nearest levels
                if support_levels:
                    nearest_support = max(support_levels)
                    st.markdown(f"**Nearest Support:** `{nearest_support:.2f}` EGP")
                    stop_loss = nearest_support * 0.98  # 2% below support
                    st.markdown(f"**Suggested Stop Loss:** `{stop_loss:.2f}` EGP")

                if resistance_levels:
                    nearest_resistance = min(resistance_levels)
                    st.markdown(f"**Nearest Resistance:** `{nearest_resistance:.2f}` EGP")
                    take_profit = nearest_resistance * 0.98  # 2% below resistance
                    st.markdown(f"**Suggested Take Profit:** `{take_profit:.2f}` EGP")

                # Risk-reward ratio
                if support_levels and resistance_levels:
                    risk = current_price - max(support_levels)
                    reward = min(resistance_levels) - current_price
                    if risk > 0:
                        rr_ratio = reward / risk
                        st.markdown(f"**Risk/Reward Ratio:** `1:{rr_ratio:.2f}`")

    else:
        # Welcome screen
        st.markdown("""
        ## 📁 Welcome to EGX Historical CSV Analysis

        This page provides comprehensive Smart Money Concepts analysis using **historical CSV data files**.

        ### 🚀 New Features:
        - **� Live TradingView Data** - Real-time OHLCV data directly from TradingView
        - **�📊 Complete SMC Analysis** - Order Blocks, FVGs, Liquidity Zones, Displacements
        - **📈 Professional Charts** - Multi-panel charts with all SMC levels
        - **⚡ Real-time Signals** - Trading signals based on confluence analysis
        - **🎛️ Advanced Controls** - Customizable parameters and timeframes
        - **� Synchronized Analysis** - SMC analysis on the same data displayed in charts

        ### 📋 Instructions:
        1. **Choose Data Source**: TradingView (Live) or CSV Files (Historical)
        2. **Select EGX Stock** from available symbols
        3. **Choose Timeframe** (1D, 4H, 1H, 30M, 15M) for TradingView data
        4. **Adjust SMC Parameters** if needed
        5. **Click "Run Professional SMC Analysis"**
        6. **Review Results** with synchronized chart and analysis data

        ### 🎯 **BREAKTHROUGH**:
        **SMC analysis now reads the SAME data as TradingView charts!** No more discrepancies between chart display and analysis results.

        **Ready to analyze Egyptian stocks with institutional-grade SMC tools and live data!** 🇪🇬📈📡
        """)

        # Show TradingView connection status
        if TRADINGVIEW_AVAILABLE:
            st.success("✅ TradingView data integration is ACTIVE")
            st.info("📡 You can now analyze live EGX data directly from TradingView!")
        else:
            st.warning("⚠️ TradingView integration not available - using CSV files only")

if __name__ == "__main__":
    main()
