{"name": "tinybench", "version": "2.5.0", "type": "module", "packageManager": "pnpm@7.5.1", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs", "default": "./dist/index.cjs"}, "files": ["dist/**"], "repository": "tinylibs/tinybench", "license": "MIT", "devDependencies": {"@size-limit/preset-small-lib": "^7.0.4", "@size-limit/time": "^7.0.8", "@types/node": "^18.7.13", "@typescript-eslint/eslint-plugin": "^5.35.1", "@typescript-eslint/parser": "^5.35.1", "bumpp": "^8.2.0", "changelogithub": "^0.12.4", "clean-publish": "^3.4.4", "eslint": "^8.22.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.26.0", "nano-staged": "^0.5.0", "size-limit": "^7.0.8", "tsup": "^5.11.7", "typescript": "^4.5.4", "vite": "^2.9.12", "vitest": "^0.14.2"}, "keywords": ["benchmark", "tinylibs", "tiny"], "scripts": {"dev": "tsup --watch", "build": "tsup", "release": "bumpp package.json --commit --push --tag && npm run publish", "test": "vitest --no-threads"}}