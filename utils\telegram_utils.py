"""
Simplified notification system - Web Dashboard replaces Telegram
All notifications now go to console/logs and web dashboard
"""

from log_setup import logger

# Telegram completely disabled - using web dashboard instead
TELEGRAM_ENABLED = False
print("📱 Telegram disabled - using Web Dashboard for notifications")

__all__ = [
    "send_telegram_message",
    "send_telegram_photo",
    "notify_entry",
    "notify_take_profit",
    "notify_stop_loss",
    "notify_manual_close",
    "notify_pnl",
]

async def send_telegram_message(message: str, parse_mode: str = "HTML"):
    """Log message instead of sending to Telegram"""
    # Clean HTML tags for console output
    clean_message = message.replace("<b>", "").replace("</b>", "").replace("<i>", "").replace("</i>", "")
    logger.info(f"📱 [Notification] {clean_message}")
    print(f"📱 {clean_message}")

async def send_telegram_photo(image_path: str, caption: str = ""):
    """Log photo caption instead of sending to Telegram"""
    logger.info(f"📱 [Photo] {caption} (file: {image_path})")
    print(f"📱 Photo: {caption}")

async def notify_entry(symbol: str, side: str, size: float, price: float):
    text = (
        f"🚀 <b>TRADE ENTRY</b>\n"
        f"• <b>{symbol}</b> {side.upper()}\n"
        f"• Volume: <b>{size}</b>\n"
        f"• Entry Price: <b>{price}</b>"
    )
    await send_telegram_message(text)

async def notify_take_profit(symbol: str, qty: float, price: float):
    text = (
        f"🎯 <b>TP</b> for <b>{symbol}</b>\n"
        f"• Volume: <b>{qty}</b>\n"
        f"• Price: <b>{price}</b>"
    )
    await send_telegram_message(text)

async def notify_stop_loss(symbol: str, qty: float, price: float):
    text = (
        f"🛑 <b>SL</b> for <b>{symbol}</b>\n"
        f"• Volume: <b>{qty}</b>\n"
        f"• Price: <b>{price}</b>"
    )
    await send_telegram_message(text)

async def notify_manual_close(symbol: str):
    text = f"🚪 Position for <b>{symbol}</b> closed manually."
    await send_telegram_message(text)

async def notify_pnl(symbol: str, pnl: float):
    emoji = "🟢" if pnl >= 0 else "🔴"
    text = f"{emoji} <b>Result for {symbol}</b>: PnL = <b>{pnl:.2f} USDT</b>"
    await send_telegram_message(text)
