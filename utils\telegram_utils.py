import os
import asyncio
from telegram.ext import Application
from dotenv import load_dotenv

load_dotenv()

TELEGRAM_TOKEN: str = os.getenv("TELEGRAM_TOKEN") or ""
TELEGRAM_CHAT_ID: str = os.getenv("TELEGRAM_CHAT_ID") or ""

# Make Telegram optional for testing
TELEGRAM_ENABLED = bool(TELEGRAM_TOKEN and TELEGRAM_CHAT_ID and TELEGRAM_TOKEN != "your_telegram_token_here")

if TELEGRAM_ENABLED:
    application = Application.builder().token(TELEGRAM_TOKEN).build()
    bot = application.bot
else:
    print("⚠️ Telegram notifications disabled (no valid token/chat_id)")
    application = None
    bot = None

__all__ = [
    "send_telegram_message",
    "send_telegram_photo",
    "notify_entry",
    "notify_take_profit",
    "notify_stop_loss",
    "notify_manual_close",
    "notify_pnl",
]

# Make sure all project modules import send_telegram_message and other notifications
# from here: from utils.telegram_utils import send_telegram_message, ...
# The old telegram_sender module should be removed and not used.

async def send_telegram_message(message: str, parse_mode: str = "HTML"):
    if not TELEGRAM_ENABLED or not bot:
        print(f"📱 [Telegram Disabled] {message}")
        return

    try:
        await bot.send_message(chat_id=TELEGRAM_CHAT_ID, text=message, parse_mode=parse_mode)
    except Exception as e:
        print(f"❌ Error sending message to Telegram: {e}")

async def send_telegram_photo(image_path: str, caption: str = ""):
    if not TELEGRAM_ENABLED or not bot:
        print(f"📱 [Telegram Disabled] Photo: {caption}")
        return

    try:
        with open(image_path, "rb") as photo:
            await bot.send_photo(chat_id=TELEGRAM_CHAT_ID, photo=photo, caption=caption)
    except Exception as e:
        print(f"❌ Error sending photo to Telegram: {e}")

async def notify_entry(symbol: str, side: str, size: float, price: float):
    text = (
        f"🚀 <b>TRADE ENTRY</b>\n"
        f"• <b>{symbol}</b> {side.upper()}\n"
        f"• Volume: <b>{size}</b>\n"
        f"• Entry Price: <b>{price}</b>"
    )
    await send_telegram_message(text)

async def notify_take_profit(symbol: str, qty: float, price: float):
    text = (
        f"🎯 <b>TP</b> for <b>{symbol}</b>\n"
        f"• Volume: <b>{qty}</b>\n"
        f"• Price: <b>{price}</b>"
    )
    await send_telegram_message(text)

async def notify_stop_loss(symbol: str, qty: float, price: float):
    text = (
        f"🛑 <b>SL</b> for <b>{symbol}</b>\n"
        f"• Volume: <b>{qty}</b>\n"
        f"• Price: <b>{price}</b>"
    )
    await send_telegram_message(text)

async def notify_manual_close(symbol: str):
    text = f"🚪 Position for <b>{symbol}</b> closed manually."
    await send_telegram_message(text)

async def notify_pnl(symbol: str, pnl: float):
    emoji = "🟢" if pnl >= 0 else "🔴"
    text = f"{emoji} <b>Result for {symbol}</b>: PnL = <b>{pnl:.2f} USDT</b>"
    await send_telegram_message(text)
