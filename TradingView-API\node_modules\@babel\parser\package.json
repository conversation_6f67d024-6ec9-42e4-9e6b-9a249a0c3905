{"name": "@babel/parser", "version": "7.15.7", "description": "A JavaScript parser", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-parser", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A+parser+%28babylon%29%22+is%3Aopen", "license": "MIT", "publishConfig": {"access": "public"}, "keywords": ["babel", "javascript", "parser", "tc39", "ecmascript", "@babel/parser"], "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-parser"}, "main": "./lib/index.js", "types": "./typings/babel-parser.d.ts", "files": ["bin", "lib", "typings"], "engines": {"node": ">=6.0.0"}, "devDependencies": {"@babel/code-frame": "7.14.5", "@babel/helper-fixtures": "7.14.5", "@babel/helper-validator-identifier": "7.15.7", "charcodes": "^0.2.0"}, "bin": "./bin/babel-parser.js"}