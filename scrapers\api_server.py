"""
FastAPI server for the advanced TradingView scraper
Provides REST API endpoints for scraping financial data
"""

import asyncio
import uvicorn
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any
import logging

from advanced_scraper import (
    scrape_multiple_pairs,
    PairRequest,
    TimeInterval,
    FinancialDTO,
    PLAYWRIGHT_AVAILABLE
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="TradingView Advanced Scraper API",
    description="API for scraping comprehensive financial data from TradingView",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class ScrapeRequest(BaseModel):
    pairs: List[str]
    intervals: List[str] = ["1D", "1W"]

class ScrapeResponse(BaseModel):
    success: bool
    data: Dict[str, List[Dict[str, Any]]]
    message: str = ""

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "TradingView Advanced Scraper API",
        "version": "1.0.0",
        "playwright_available": PLAYWRIGHT_AVAILABLE,
        "endpoints": {
            "scrape_pairs": "/api/scrape_pairs",
            "health": "/health",
            "docs": "/docs"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "playwright_available": PLAYWRIGHT_AVAILABLE,
        "timestamp": "2024-01-01T00:00:00Z"
    }

@app.post("/api/scrape_pairs", response_model=ScrapeResponse)
async def scrape_pairs_endpoint(request: ScrapeRequest):
    """
    Scrape financial data for multiple trading pairs
    
    Args:
        request: ScrapeRequest with pairs and intervals
        
    Returns:
        ScrapeResponse with comprehensive financial data
    """
    if not PLAYWRIGHT_AVAILABLE:
        raise HTTPException(
            status_code=503,
            detail="Playwright not available. Install with: pip install playwright && playwright install chromium"
        )
    
    try:
        logger.info(f"Scraping pairs: {request.pairs} with intervals: {request.intervals}")
        
        # Validate intervals
        valid_intervals = ["1m", "5m", "15m", "30m", "1h", "2h", "4h", "1D", "1W", "1M"]
        for interval in request.intervals:
            if interval not in valid_intervals:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid interval: {interval}. Valid intervals: {valid_intervals}"
                )
        
        # Scrape the data
        result = await scrape_multiple_pairs(request.pairs, request.intervals)
        
        # Convert FinancialDTO objects to dictionaries for JSON serialization
        serialized_result = {}
        for pair, financial_data_list in result.items():
            serialized_result[pair] = []
            for financial_data in financial_data_list:
                serialized_result[pair].append({
                    "pair": financial_data.pair,
                    "price": financial_data.price,
                    "oscillators": [
                        {
                            "pair": osc.pair,
                            "interval": osc.interval,
                            "register_time": osc.register_time,
                            "name": osc.name,
                            "value": osc.value,
                            "action": osc.action
                        }
                        for osc in financial_data.oscillators
                    ],
                    "moving_averages": [
                        {
                            "pair": ma.pair,
                            "interval": ma.interval,
                            "register_time": ma.register_time,
                            "name": ma.name,
                            "value": ma.value,
                            "action": ma.action
                        }
                        for ma in financial_data.moving_averages
                    ],
                    "pivots": [
                        {
                            "pair": pivot.pair,
                            "interval": pivot.interval,
                            "register_time": pivot.register_time,
                            "pivot": pivot.pivot,
                            "classic": pivot.classic,
                            "fibo": pivot.fibo,
                            "camarilla": pivot.camarilla,
                            "woodie": pivot.woodie,
                            "dm": pivot.dm
                        }
                        for pivot in financial_data.pivots
                    ]
                })
        
        return ScrapeResponse(
            success=True,
            data=serialized_result,
            message=f"Successfully scraped {len(request.pairs)} pairs"
        )
        
    except Exception as e:
        logger.error(f"Error scraping pairs: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error scraping data: {str(e)}"
        )

@app.get("/api/pairs/{pair}")
async def get_pair_data(pair: str, intervals: str = "1D,1W"):
    """
    Get data for a single trading pair
    
    Args:
        pair: Trading pair symbol (e.g., EGX-COMI)
        intervals: Comma-separated intervals (e.g., "1D,1W")
        
    Returns:
        Financial data for the pair
    """
    interval_list = [interval.strip() for interval in intervals.split(",")]
    
    request = ScrapeRequest(pairs=[pair], intervals=interval_list)
    response = await scrape_pairs_endpoint(request)
    
    if pair in response.data:
        return {
            "success": True,
            "pair": pair,
            "data": response.data[pair],
            "message": f"Successfully scraped {pair}"
        }
    else:
        raise HTTPException(
            status_code=404,
            detail=f"No data found for pair: {pair}"
        )

@app.get("/api/intervals")
async def get_available_intervals():
    """Get list of available time intervals"""
    return {
        "intervals": [
            {"value": "1m", "label": "1 Minute"},
            {"value": "5m", "label": "5 Minutes"},
            {"value": "15m", "label": "15 Minutes"},
            {"value": "30m", "label": "30 Minutes"},
            {"value": "1h", "label": "1 Hour"},
            {"value": "2h", "label": "2 Hours"},
            {"value": "4h", "label": "4 Hours"},
            {"value": "1D", "label": "1 Day"},
            {"value": "1W", "label": "1 Week"},
            {"value": "1M", "label": "1 Month"}
        ],
        "recommended_for_egx": ["1D", "1W"],
        "trading_hours": "Sunday-Thursday, 10:00 AM - 2:30 PM (Cairo Time)"
    }

if __name__ == "__main__":
    print("🚀 Starting TradingView Advanced Scraper API Server")
    print("📊 API Endpoint: http://127.0.0.1:8000/api/scrape_pairs")
    print("🌐 Web Interface: http://127.0.0.1:8000/")
    print("📚 Documentation: http://127.0.0.1:8000/docs")
    print("⏰ EGX Trading Hours: Sunday-Thursday, 10:00 AM - 2:30 PM (Cairo Time)")
    
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        log_level="info"
    )
