import{n as jt,r as Et,j as st,F as Vt}from"./index.BqDl3eRM.js";/*! @license DOMPurify 3.2.5 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.5/LICENSE */const{entries:gt,setPrototypeOf:lt,isFrozen:$t,getPrototypeOf:qt,getOwnPropertyDescriptor:Kt}=Object;let{freeze:S,seal:y,create:At}=Object,{apply:Ce,construct:we}=typeof Reflect<"u"&&Reflect;S||(S=function(n){return n});y||(y=function(n){return n});Ce||(Ce=function(n,s,l){return n.apply(s,l)});we||(we=function(n,s){return new n(...s)});const se=R(Array.prototype.forEach),Zt=R(Array.prototype.lastIndexOf),ct=R(Array.prototype.pop),V=R(Array.prototype.push),Jt=R(Array.prototype.splice),ce=R(String.prototype.toLowerCase),be=R(String.prototype.toString),ft=R(String.prototype.match),$=R(String.prototype.replace),Qt=R(String.prototype.indexOf),en=R(String.prototype.trim),L=R(Object.prototype.hasOwnProperty),h=R(RegExp.prototype.test),q=tn(TypeError);function R(a){return function(n){n instanceof RegExp&&(n.lastIndex=0);for(var s=arguments.length,l=new Array(s>1?s-1:0),T=1;T<s;T++)l[T-1]=arguments[T];return Ce(a,n,l)}}function tn(a){return function(){for(var n=arguments.length,s=new Array(n),l=0;l<n;l++)s[l]=arguments[l];return we(a,s)}}function r(a,n){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:ce;lt&&lt(a,null);let l=n.length;for(;l--;){let T=n[l];if(typeof T=="string"){const D=s(T);D!==T&&($t(n)||(n[l]=D),T=D)}a[T]=!0}return a}function nn(a){for(let n=0;n<a.length;n++)L(a,n)||(a[n]=null);return a}function w(a){const n=At(null);for(const[s,l]of gt(a))L(a,s)&&(Array.isArray(l)?n[s]=nn(l):l&&typeof l=="object"&&l.constructor===Object?n[s]=w(l):n[s]=l);return n}function K(a,n){for(;a!==null;){const l=Kt(a,n);if(l){if(l.get)return R(l.get);if(typeof l.value=="function")return R(l.value)}a=qt(a)}function s(){return null}return s}const ut=S(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ne=S(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),De=S(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),on=S(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ie=S(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),an=S(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),mt=S(["#text"]),pt=S(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),Me=S(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),dt=S(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),le=S(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),rn=y(/\{\{[\w\W]*|[\w\W]*\}\}/gm),sn=y(/<%[\w\W]*|[\w\W]*%>/gm),ln=y(/\$\{[\w\W]*/gm),cn=y(/^data-[\-\w.\u00B7-\uFFFF]+$/),fn=y(/^aria-[\-\w]+$/),ht=y(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),un=y(/^(?:\w+script|data):/i),mn=y(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),St=y(/^html$/i),pn=y(/^[a-z][.\w]*(-[.\w]+)+$/i);var Tt=Object.freeze({__proto__:null,ARIA_ATTR:fn,ATTR_WHITESPACE:mn,CUSTOM_ELEMENT:pn,DATA_ATTR:cn,DOCTYPE_NAME:St,ERB_EXPR:sn,IS_ALLOWED_URI:ht,IS_SCRIPT_OR_DATA:un,MUSTACHE_EXPR:rn,TMPLIT_EXPR:ln});const Z={element:1,text:3,progressingInstruction:7,comment:8,document:9},dn=function(){return typeof window>"u"?null:window},Tn=function(n,s){if(typeof n!="object"||typeof n.createPolicy!="function")return null;let l=null;const T="data-tt-policy-suffix";s&&s.hasAttribute(T)&&(l=s.getAttribute(T));const D="dompurify"+(l?"#"+l:"");try{return n.createPolicy(D,{createHTML(x){return x},createScriptURL(x){return x}})}catch{return console.warn("TrustedTypes policy "+D+" could not be created."),null}},_t=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function Rt(){let a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:dn();const n=i=>Rt(i);if(n.version="3.2.5",n.removed=[],!a||!a.document||a.document.nodeType!==Z.document||!a.Element)return n.isSupported=!1,n;let{document:s}=a;const l=s,T=l.currentScript,{DocumentFragment:D,HTMLTemplateElement:x,Node:fe,Element:ke,NodeFilter:z,NamedNodeMap:Ot=a.NamedNodeMap||a.MozNamedAttrMap,HTMLFormElement:yt,DOMParser:Lt,trustedTypes:J}=a,G=ke.prototype,bt=K(G,"cloneNode"),Nt=K(G,"remove"),Dt=K(G,"nextSibling"),It=K(G,"childNodes"),Q=K(G,"parentNode");if(typeof x=="function"){const i=s.createElement("template");i.content&&i.content.ownerDocument&&(s=i.content.ownerDocument)}let E,W="";const{implementation:ue,createNodeIterator:Mt,createDocumentFragment:Ct,getElementsByTagName:wt}=s,{importNode:xt}=l;let g=_t();n.isSupported=typeof gt=="function"&&typeof Q=="function"&&ue&&ue.createHTMLDocument!==void 0;const{MUSTACHE_EXPR:me,ERB_EXPR:pe,TMPLIT_EXPR:de,DATA_ATTR:Pt,ARIA_ATTR:kt,IS_SCRIPT_OR_DATA:vt,ATTR_WHITESPACE:ve,CUSTOM_ELEMENT:Ut}=Tt;let{IS_ALLOWED_URI:Ue}=Tt,u=null;const Fe=r({},[...ut,...Ne,...De,...Ie,...mt]);let p=null;const He=r({},[...pt,...Me,...dt,...le]);let f=Object.seal(At(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),B=null,Te=null,ze=!0,_e=!0,Ge=!1,We=!0,P=!1,Ee=!0,C=!1,ge=!1,Ae=!1,k=!1,ee=!1,te=!1,Be=!0,Ye=!1;const Ft="user-content-";let he=!0,Y=!1,v={},U=null;const Xe=r({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let je=null;const Ve=r({},["audio","video","img","source","image","track"]);let Se=null;const $e=r({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ne="http://www.w3.org/1998/Math/MathML",oe="http://www.w3.org/2000/svg",I="http://www.w3.org/1999/xhtml";let F=I,Re=!1,Oe=null;const Ht=r({},[ne,oe,I],be);let ie=r({},["mi","mo","mn","ms","mtext"]),ae=r({},["annotation-xml"]);const zt=r({},["title","style","font","a","script"]);let X=null;const Gt=["application/xhtml+xml","text/html"],Wt="text/html";let m=null,H=null;const Bt=s.createElement("form"),qe=function(e){return e instanceof RegExp||e instanceof Function},ye=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!(H&&H===e)){if((!e||typeof e!="object")&&(e={}),e=w(e),X=Gt.indexOf(e.PARSER_MEDIA_TYPE)===-1?Wt:e.PARSER_MEDIA_TYPE,m=X==="application/xhtml+xml"?be:ce,u=L(e,"ALLOWED_TAGS")?r({},e.ALLOWED_TAGS,m):Fe,p=L(e,"ALLOWED_ATTR")?r({},e.ALLOWED_ATTR,m):He,Oe=L(e,"ALLOWED_NAMESPACES")?r({},e.ALLOWED_NAMESPACES,be):Ht,Se=L(e,"ADD_URI_SAFE_ATTR")?r(w($e),e.ADD_URI_SAFE_ATTR,m):$e,je=L(e,"ADD_DATA_URI_TAGS")?r(w(Ve),e.ADD_DATA_URI_TAGS,m):Ve,U=L(e,"FORBID_CONTENTS")?r({},e.FORBID_CONTENTS,m):Xe,B=L(e,"FORBID_TAGS")?r({},e.FORBID_TAGS,m):{},Te=L(e,"FORBID_ATTR")?r({},e.FORBID_ATTR,m):{},v=L(e,"USE_PROFILES")?e.USE_PROFILES:!1,ze=e.ALLOW_ARIA_ATTR!==!1,_e=e.ALLOW_DATA_ATTR!==!1,Ge=e.ALLOW_UNKNOWN_PROTOCOLS||!1,We=e.ALLOW_SELF_CLOSE_IN_ATTR!==!1,P=e.SAFE_FOR_TEMPLATES||!1,Ee=e.SAFE_FOR_XML!==!1,C=e.WHOLE_DOCUMENT||!1,k=e.RETURN_DOM||!1,ee=e.RETURN_DOM_FRAGMENT||!1,te=e.RETURN_TRUSTED_TYPE||!1,Ae=e.FORCE_BODY||!1,Be=e.SANITIZE_DOM!==!1,Ye=e.SANITIZE_NAMED_PROPS||!1,he=e.KEEP_CONTENT!==!1,Y=e.IN_PLACE||!1,Ue=e.ALLOWED_URI_REGEXP||ht,F=e.NAMESPACE||I,ie=e.MATHML_TEXT_INTEGRATION_POINTS||ie,ae=e.HTML_INTEGRATION_POINTS||ae,f=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&qe(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(f.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&qe(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(f.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements=="boolean"&&(f.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),P&&(_e=!1),ee&&(k=!0),v&&(u=r({},mt),p=[],v.html===!0&&(r(u,ut),r(p,pt)),v.svg===!0&&(r(u,Ne),r(p,Me),r(p,le)),v.svgFilters===!0&&(r(u,De),r(p,Me),r(p,le)),v.mathMl===!0&&(r(u,Ie),r(p,dt),r(p,le))),e.ADD_TAGS&&(u===Fe&&(u=w(u)),r(u,e.ADD_TAGS,m)),e.ADD_ATTR&&(p===He&&(p=w(p)),r(p,e.ADD_ATTR,m)),e.ADD_URI_SAFE_ATTR&&r(Se,e.ADD_URI_SAFE_ATTR,m),e.FORBID_CONTENTS&&(U===Xe&&(U=w(U)),r(U,e.FORBID_CONTENTS,m)),he&&(u["#text"]=!0),C&&r(u,["html","head","body"]),u.table&&(r(u,["tbody"]),delete B.tbody),e.TRUSTED_TYPES_POLICY){if(typeof e.TRUSTED_TYPES_POLICY.createHTML!="function")throw q('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if(typeof e.TRUSTED_TYPES_POLICY.createScriptURL!="function")throw q('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');E=e.TRUSTED_TYPES_POLICY,W=E.createHTML("")}else E===void 0&&(E=Tn(J,T)),E!==null&&typeof W=="string"&&(W=E.createHTML(""));S&&S(e),H=e}},Ke=r({},[...Ne,...De,...on]),Ze=r({},[...Ie,...an]),Yt=function(e){let t=Q(e);(!t||!t.tagName)&&(t={namespaceURI:F,tagName:"template"});const o=ce(e.tagName),c=ce(t.tagName);return Oe[e.namespaceURI]?e.namespaceURI===oe?t.namespaceURI===I?o==="svg":t.namespaceURI===ne?o==="svg"&&(c==="annotation-xml"||ie[c]):!!Ke[o]:e.namespaceURI===ne?t.namespaceURI===I?o==="math":t.namespaceURI===oe?o==="math"&&ae[c]:!!Ze[o]:e.namespaceURI===I?t.namespaceURI===oe&&!ae[c]||t.namespaceURI===ne&&!ie[c]?!1:!Ze[o]&&(zt[o]||!Ke[o]):!!(X==="application/xhtml+xml"&&Oe[e.namespaceURI]):!1},b=function(e){V(n.removed,{element:e});try{Q(e).removeChild(e)}catch{Nt(e)}},re=function(e,t){try{V(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch{V(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),e==="is")if(k||ee)try{b(t)}catch{}else try{t.setAttribute(e,"")}catch{}},Je=function(e){let t=null,o=null;if(Ae)e="<remove></remove>"+e;else{const d=ft(e,/^[\r\n\t ]+/);o=d&&d[0]}X==="application/xhtml+xml"&&F===I&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const c=E?E.createHTML(e):e;if(F===I)try{t=new Lt().parseFromString(c,X)}catch{}if(!t||!t.documentElement){t=ue.createDocument(F,"template",null);try{t.documentElement.innerHTML=Re?W:c}catch{}}const _=t.body||t.documentElement;return e&&o&&_.insertBefore(s.createTextNode(o),_.childNodes[0]||null),F===I?wt.call(t,C?"html":"body")[0]:C?t.documentElement:_},Qe=function(e){return Mt.call(e.ownerDocument||e,e,z.SHOW_ELEMENT|z.SHOW_COMMENT|z.SHOW_TEXT|z.SHOW_PROCESSING_INSTRUCTION|z.SHOW_CDATA_SECTION,null)},Le=function(e){return e instanceof yt&&(typeof e.nodeName!="string"||typeof e.textContent!="string"||typeof e.removeChild!="function"||!(e.attributes instanceof Ot)||typeof e.removeAttribute!="function"||typeof e.setAttribute!="function"||typeof e.namespaceURI!="string"||typeof e.insertBefore!="function"||typeof e.hasChildNodes!="function")},et=function(e){return typeof fe=="function"&&e instanceof fe};function M(i,e,t){se(i,o=>{o.call(n,e,t,H)})}const tt=function(e){let t=null;if(M(g.beforeSanitizeElements,e,null),Le(e))return b(e),!0;const o=m(e.nodeName);if(M(g.uponSanitizeElement,e,{tagName:o,allowedTags:u}),e.hasChildNodes()&&!et(e.firstElementChild)&&h(/<[/\w!]/g,e.innerHTML)&&h(/<[/\w!]/g,e.textContent)||e.nodeType===Z.progressingInstruction||Ee&&e.nodeType===Z.comment&&h(/<[/\w]/g,e.data))return b(e),!0;if(!u[o]||B[o]){if(!B[o]&&ot(o)&&(f.tagNameCheck instanceof RegExp&&h(f.tagNameCheck,o)||f.tagNameCheck instanceof Function&&f.tagNameCheck(o)))return!1;if(he&&!U[o]){const c=Q(e)||e.parentNode,_=It(e)||e.childNodes;if(_&&c){const d=_.length;for(let O=d-1;O>=0;--O){const N=bt(_[O],!0);N.__removalCount=(e.__removalCount||0)+1,c.insertBefore(N,Dt(e))}}}return b(e),!0}return e instanceof ke&&!Yt(e)||(o==="noscript"||o==="noembed"||o==="noframes")&&h(/<\/no(script|embed|frames)/i,e.innerHTML)?(b(e),!0):(P&&e.nodeType===Z.text&&(t=e.textContent,se([me,pe,de],c=>{t=$(t,c," ")}),e.textContent!==t&&(V(n.removed,{element:e.cloneNode()}),e.textContent=t)),M(g.afterSanitizeElements,e,null),!1)},nt=function(e,t,o){if(Be&&(t==="id"||t==="name")&&(o in s||o in Bt))return!1;if(!(_e&&!Te[t]&&h(Pt,t))){if(!(ze&&h(kt,t))){if(!p[t]||Te[t]){if(!(ot(e)&&(f.tagNameCheck instanceof RegExp&&h(f.tagNameCheck,e)||f.tagNameCheck instanceof Function&&f.tagNameCheck(e))&&(f.attributeNameCheck instanceof RegExp&&h(f.attributeNameCheck,t)||f.attributeNameCheck instanceof Function&&f.attributeNameCheck(t))||t==="is"&&f.allowCustomizedBuiltInElements&&(f.tagNameCheck instanceof RegExp&&h(f.tagNameCheck,o)||f.tagNameCheck instanceof Function&&f.tagNameCheck(o))))return!1}else if(!Se[t]){if(!h(Ue,$(o,ve,""))){if(!((t==="src"||t==="xlink:href"||t==="href")&&e!=="script"&&Qt(o,"data:")===0&&je[e])){if(!(Ge&&!h(vt,$(o,ve,"")))){if(o)return!1}}}}}}return!0},ot=function(e){return e!=="annotation-xml"&&ft(e,Ut)},it=function(e){M(g.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||Le(e))return;const o={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:p,forceKeepAttr:void 0};let c=t.length;for(;c--;){const _=t[c],{name:d,namespaceURI:O,value:N}=_,j=m(d);let A=d==="value"?N:en(N);if(o.attrName=j,o.attrValue=A,o.keepAttr=!0,o.forceKeepAttr=void 0,M(g.uponSanitizeAttribute,e,o),A=o.attrValue,Ye&&(j==="id"||j==="name")&&(re(d,e),A=Ft+A),Ee&&h(/((--!?|])>)|<\/(style|title)/i,A)){re(d,e);continue}if(o.forceKeepAttr||(re(d,e),!o.keepAttr))continue;if(!We&&h(/\/>/i,A)){re(d,e);continue}P&&se([me,pe,de],rt=>{A=$(A,rt," ")});const at=m(e.nodeName);if(nt(at,j,A)){if(E&&typeof J=="object"&&typeof J.getAttributeType=="function"&&!O)switch(J.getAttributeType(at,j)){case"TrustedHTML":{A=E.createHTML(A);break}case"TrustedScriptURL":{A=E.createScriptURL(A);break}}try{O?e.setAttributeNS(O,d,A):e.setAttribute(d,A),Le(e)?b(e):ct(n.removed)}catch{}}}M(g.afterSanitizeAttributes,e,null)},Xt=function i(e){let t=null;const o=Qe(e);for(M(g.beforeSanitizeShadowDOM,e,null);t=o.nextNode();)M(g.uponSanitizeShadowNode,t,null),tt(t),it(t),t.content instanceof D&&i(t.content);M(g.afterSanitizeShadowDOM,e,null)};return n.sanitize=function(i){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},t=null,o=null,c=null,_=null;if(Re=!i,Re&&(i="<!-->"),typeof i!="string"&&!et(i))if(typeof i.toString=="function"){if(i=i.toString(),typeof i!="string")throw q("dirty is not a string, aborting")}else throw q("toString is not a function");if(!n.isSupported)return i;if(ge||ye(e),n.removed=[],typeof i=="string"&&(Y=!1),Y){if(i.nodeName){const N=m(i.nodeName);if(!u[N]||B[N])throw q("root node is forbidden and cannot be sanitized in-place")}}else if(i instanceof fe)t=Je("<!---->"),o=t.ownerDocument.importNode(i,!0),o.nodeType===Z.element&&o.nodeName==="BODY"||o.nodeName==="HTML"?t=o:t.appendChild(o);else{if(!k&&!P&&!C&&i.indexOf("<")===-1)return E&&te?E.createHTML(i):i;if(t=Je(i),!t)return k?null:te?W:""}t&&Ae&&b(t.firstChild);const d=Qe(Y?i:t);for(;c=d.nextNode();)tt(c),it(c),c.content instanceof D&&Xt(c.content);if(Y)return i;if(k){if(ee)for(_=Ct.call(t.ownerDocument);t.firstChild;)_.appendChild(t.firstChild);else _=t;return(p.shadowroot||p.shadowrootmode)&&(_=xt.call(l,_,!0)),_}let O=C?t.outerHTML:t.innerHTML;return C&&u["!doctype"]&&t.ownerDocument&&t.ownerDocument.doctype&&t.ownerDocument.doctype.name&&h(St,t.ownerDocument.doctype.name)&&(O="<!DOCTYPE "+t.ownerDocument.doctype.name+`>
`+O),P&&se([me,pe,de],N=>{O=$(O,N," ")}),E&&te?E.createHTML(O):O},n.setConfig=function(){let i=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};ye(i),ge=!0},n.clearConfig=function(){H=null,ge=!1},n.isValidAttribute=function(i,e,t){H||ye({});const o=m(i),c=m(e);return nt(o,c,t)},n.addHook=function(i,e){typeof e=="function"&&V(g[i],e)},n.removeHook=function(i,e){if(e!==void 0){const t=Zt(g[i],e);return t===-1?void 0:Jt(g[i],t,1)[0]}return ct(g[i])},n.removeHooks=function(i){g[i]=[]},n.removeAllHooks=function(){g=_t()},n}var Pe=Rt();const _n=jt("div",{target:"ez8s6cm0"})({width:"100%"}),xe="data-temp-href-target";Pe.addHook("beforeSanitizeAttributes",function(a){a instanceof HTMLElement&&a.hasAttribute("target")&&a.getAttribute("target")==="_blank"&&a.setAttribute(xe,"_blank")});Pe.addHook("afterSanitizeAttributes",function(a){a instanceof HTMLElement&&a.hasAttribute(xe)&&(a.setAttribute("target","_blank"),a.setAttribute("rel","noopener noreferrer"),a.removeAttribute(xe))});const En=a=>{const n={USE_PROFILES:{html:!0},FORCE_BODY:!0};return Pe.sanitize(a,n)};function gn({element:a}){const{body:n}=a,s=Et.useMemo(()=>En(n),[n]);return st(Vt,{children:s&&st(_n,{className:"stHtml","data-testid":"stHtml",dangerouslySetInnerHTML:{__html:s}})})}const hn=Et.memo(gn);export{hn as default};
