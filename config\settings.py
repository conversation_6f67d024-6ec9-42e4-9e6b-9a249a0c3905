# config/settings.py

import os
from dotenv import load_dotenv

load_dotenv()

# Live mode: if True — orders are sent to the exchange
LIVE_MODE = os.getenv("LIVE_MODE", "false").lower() == "true"

# Show confidence threshold debugging
SHOW_THRESHOLD_DEBUG = os.getenv("SHOW_THRESHOLD_DEBUG", "false").lower() == "true"

# AUTO_TRADE — alias for LIVE_MODE (for compatibility)
AUTO_TRADE = LIVE_MODE

# Global risk per trade by default (in percentage)
DEFAULT_RISK_PCT = float(os.getenv("DEFAULT_RISK_PCT", "0.01"))
