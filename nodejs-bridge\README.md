# TradingView EGX Bridge

A Node.js API bridge that fetches real-time EGX (Egyptian Exchange) stock data from TradingView for use with Python trading bots.

## Features

- 📊 Real-time stock prices from TradingView
- 📈 OHLCV (candlestick) data with multiple timeframes
- 🔍 Symbol search functionality
- 📦 Batch price requests
- ⚡ Built-in caching for performance
- 🛡️ Rate limiting and error handling
- 📝 Comprehensive logging
- 🔄 Auto-reconnection on connection loss

## Quick Start

### 1. Installation

```bash
cd nodejs-bridge
npm install
```

### 2. Configuration

Copy the environment file and configure:

```bash
cp .env.example .env
```

Edit `.env` with your settings:

```env
PORT=3001
TRADINGVIEW_USERNAME=your_username_here  # Optional
TRADINGVIEW_PASSWORD=your_password_here  # Optional
```

### 3. Start the Server

```bash
# Development mode with auto-restart
npm run dev

# Production mode
npm start
```

### 4. Test the API

```bash
npm test
```

## API Endpoints

### Health Check
```
GET /health
```

### Current Price
```
GET /api/v1/price/:symbol?exchange=EGX
```

Example:
```bash
curl "http://localhost:3001/api/v1/price/CIB?exchange=EGX"
```

### OHLCV Data
```
GET /api/v1/ohlcv/:symbol?exchange=EGX&interval=1h&limit=150
```

Example:
```bash
curl "http://localhost:3001/api/v1/ohlcv/CIB?exchange=EGX&interval=1h&limit=10"
```

### Search Symbols
```
GET /api/v1/symbols/search?q=query&exchange=EGX
```

Example:
```bash
curl "http://localhost:3001/api/v1/symbols/search?q=CIB"
```

### Common EGX Symbols
```
GET /api/v1/symbols/egx
```

### Batch Prices
```
POST /api/v1/batch/prices
Content-Type: application/json

{
  "symbols": ["CIB", "ETEL", "HELI"],
  "exchange": "EGX"
}
```

### Service Status
```
GET /api/v1/status
```

## Supported Timeframes

- `1m`, `3m`, `5m`, `15m`, `30m` - Minutes
- `1h`, `2h`, `4h` - Hours  
- `1d` - Daily
- `1w` - Weekly
- `1M` - Monthly

## Common EGX Symbols

- **CIB** - Commercial International Bank
- **ETEL** - Egyptian Company for Mobile Services
- **HELI** - Heliopolis Housing
- **EGAS** - Egyptian Natural Gas Company
- **SWDY** - El Sewedy Electric Company
- **HRHO** - Hassan Allam Holding
- **EKHO** - El Kahera Housing
- **OCDI** - Orascom Construction Industries
- **TMGH** - TMG Holding
- **PHDC** - Palm Hills Developments

## Integration with Python Bot

Use the bridge in your Python trading bot:

```python
import aiohttp
import asyncio

async def get_egx_price(symbol):
    async with aiohttp.ClientSession() as session:
        url = f"http://localhost:3001/api/v1/price/{symbol}"
        params = {"exchange": "EGX"}
        async with session.get(url, params=params) as response:
            data = await response.json()
            return data["data"]["price"]

# Usage
price = await get_egx_price("CIB")
print(f"CIB price: {price}")
```

## Error Handling

The API returns standardized error responses:

```json
{
  "success": false,
  "error": "Error message here"
}
```

Common HTTP status codes:
- `200` - Success
- `400` - Bad Request
- `404` - Symbol not found
- `408` - Request timeout
- `429` - Rate limit exceeded
- `500` - Internal server error

## Logging

Logs are written to:
- Console (colored output)
- `logs/bridge.log` (all logs)
- `logs/error.log` (errors only)

## Performance

- Built-in caching (5 seconds for prices, 5 seconds for OHLCV)
- Rate limiting (100 requests per minute per IP)
- Automatic reconnection on connection loss
- Memory usage monitoring

## Troubleshooting

### Connection Issues
- Ensure TradingView credentials are correct (if using login)
- Check network connectivity
- Verify EGX symbols are available on TradingView

### Performance Issues
- Monitor cache hit rates in logs
- Adjust cache duration in service configuration
- Check rate limiting settings

### Symbol Not Found
- Verify symbol exists on EGX
- Use correct symbol format (e.g., "CIB" not "EGX:CIB")
- Try symbol search endpoint first

## License

MIT License
