{"name": "@babel/helper-validator-identifier", "version": "7.15.7", "description": "Validate identifier/keywords name", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-validator-identifier"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "exports": "./lib/index.js", "devDependencies": {"@unicode/unicode-14.0.0": "^1.2.1", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)"}