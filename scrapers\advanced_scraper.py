import asyncio
import requests
import pandas as pd
from datetime import datetime
import time
import random
import logging
import os
import re
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel
from enum import Enum

# Import Playwright for modern web scraping
try:
    from playwright.async_api import async_playwright, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    logging.warning("Playwright not available. Install with: pip install playwright && playwright install chromium")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Data Models
class TimeInterval(str, Enum):
    m1 = "1m"
    m5 = "5m"
    min15 = "15m"
    min30 = "30m"
    h1 = "1h"
    h2 = "2h"
    h4 = "4h"
    d1 = "1D"
    w1 = "1W"
    M1 = "1M"

class IndicatorDTO(BaseModel):
    pair: str
    interval: str
    register_time: str
    name: str
    value: float | None
    action: str | None

class PivotDTO(BaseModel):
    pair: str
    interval: str
    register_time: str
    pivot: str
    classic: float | None
    fibo: float | None
    camarilla: float | None
    woodie: float | None
    dm: float | None

class FinancialDTO(BaseModel):
    pair: str
    price: float
    oscillators: list[IndicatorDTO]
    moving_averages: list[IndicatorDTO]
    pivots: list[PivotDTO]

class PairRequest(BaseModel):
    pairs: list[str]
    intervals: list[TimeInterval]

# Utility Functions
def to_float(value: str) -> float | None:
    """Convert string value to float, handling special cases"""
    if value == "—" or value == "-" or not value:
        return None
    value = value.replace(".", "").replace(",", ".").strip().replace("−", "-")
    try:
        return float(value)
    except ValueError:
        return None

async def fetch_pivots(page: Page, css_selector: str, interval: str, pair: str) -> list[PivotDTO]:
    """Fetch pivot points data from TradingView"""
    await page.wait_for_selector(css_selector)
    elements = await page.locator(css_selector).all()
    pivot_list = []

    for i in range(0, len(elements), 6):
        if i + 5 < len(elements):
            pivot_list.append(
                PivotDTO(
                    pair=pair,
                    interval=interval,
                    register_time=datetime.now().strftime("%d/%m/%Y %H:%M"),
                    pivot=await elements[i].text_content(),
                    classic=to_float(await elements[i + 1].text_content()),
                    fibo=to_float(await elements[i + 2].text_content()),
                    camarilla=to_float(await elements[i + 3].text_content()),
                    woodie=to_float(await elements[i + 4].text_content()),
                    dm=to_float(await elements[i + 5].text_content()),
                )
            )
    return pivot_list

async def fetch_indicators(page: Page, css_selector: str, interval: str, pair: str) -> list[IndicatorDTO]:
    """Fetch technical indicators from TradingView"""
    await page.wait_for_selector(css_selector)
    elements = await page.locator(css_selector).all()
    indicator_list = []

    for i in range(0, len(elements), 3):
        if i + 2 < len(elements):
            indicator_list.append(
                IndicatorDTO(
                    pair=pair,
                    interval=interval,
                    register_time=datetime.now().strftime("%d/%m/%Y %H:%M"),
                    name=await elements[i].text_content(),
                    value=to_float(await elements[i + 1].text_content()),
                    action=await elements[i + 2].text_content(),
                )
            )
    return indicator_list

async def fetch_price(page: Page) -> float:
    """Fetch current price from TradingView"""
    selector = ".lastContainer-zoF9r75I"
    await page.wait_for_selector(selector)
    element = page.locator(selector).first
    price = await element.text_content()

    if price is not None:
        price_clean = re.sub(r"\D", "", price)
        if price_clean:
            return float(price_clean)
    return 0.0

async def scrape_pair(pair: str, page: Page, intervals: list[str]) -> list[FinancialDTO]:
    """Scrape comprehensive financial data for a trading pair"""
    url = f"https://tradingview.com/symbols/{pair}/technicals/"
    all_times_payload: list[FinancialDTO] = []
    await page.goto(url, wait_until="domcontentloaded")

    # CSS selectors for different data types
    oscillator_selector = "div:nth-child(1)> div.tableWrapper-hvDpy38G > table > tbody > tr.row-hvDpy38G > *"
    moving_avg_selector = "div.container-hvDpy38G.maTable-kg4MJrFB.tableWithAction-kg4MJrFB.tabletVertical-kg4MJrFB.tabletVertical-hvDpy38G > div.tableWrapper-hvDpy38G > table > tbody > tr.row-hvDpy38G > td"
    pivot_selector = "div.container-hvDpy38G.tabletVertical-hvDpy38G > div.container-Tv7LSjUz > div.wrapper-Tv7LSjUz > div > table > tbody > tr.row-hvDpy38G > td"

    for interval in intervals:
        try:
            await page.wait_for_selector(f'button[id="{interval}"]')
            await page.click(f'button[id="{interval}"]', timeout=300)

            price = await fetch_price(page)
            oscillators = await fetch_indicators(page, oscillator_selector, interval, pair)
            moving_averages = await fetch_indicators(page, moving_avg_selector, interval, pair)
            pivots = await fetch_pivots(page, pivot_selector, interval, pair)

            asset = FinancialDTO(
                pair=pair,
                price=price,
                oscillators=oscillators,
                moving_averages=moving_averages,
                pivots=pivots,
            )
            all_times_payload.append(asset)
            logger.info(f"Successfully scraped {pair} for {interval}")

        except Exception as e:
            logger.error(f"Error scraping {pair} for {interval}: {str(e)}")
            continue

    return all_times_payload

# Semaphore for concurrent processing
semaphore = asyncio.Semaphore(5)  # Maximum 5 pairs simultaneously

async def scrape_pair_with_semaphore(pair: str, intervals: list[str], context) -> tuple[str, list[FinancialDTO]]:
    """Scrape a pair with semaphore control for concurrency"""
    async with semaphore:
        page = await context.new_page()
        try:
            result = await scrape_pair(pair, page, intervals)
            return pair, result
        finally:
            await page.close()

async def scrape_multiple_pairs(pairs: list[str], intervals: list[str]) -> dict[str, list[FinancialDTO]]:
    """Main function to scrape multiple pairs with comprehensive data"""
    if not PLAYWRIGHT_AVAILABLE:
        logger.error("Playwright not available. Cannot perform advanced scraping.")
        return {}

    logger.info(f"Starting scrape for pairs: {pairs} with intervals: {intervals}")
    res_payload: dict[str, list[FinancialDTO]] = {}

    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        context = await browser.new_context()

        tasks = [
            scrape_pair_with_semaphore(pair, intervals, context)
            for pair in pairs
        ]

        results = await asyncio.gather(*tasks)

        for pair, data in results:
            res_payload[pair] = data

        await browser.close()

    return res_payload
