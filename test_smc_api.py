#!/usr/bin/env python3
"""
Test script to check SMC dashboard API
"""

import asyncio
import aiohttp
import json

async def test_smc_api():
    """Test the SMC dashboard API"""
    
    url = "http://localhost:8000/api/smc-dashboard"
    
    print("🧪 Testing SMC Dashboard API")
    print("=" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            print(f"📡 Calling: {url}")
            
            async with session.get(url) as response:
                print(f"📊 Response Status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    
                    print(f"✅ Response received!")
                    print(f"🔍 Keys: {list(data.keys())}")
                    
                    if "smc_analysis" in data:
                        smc_data = data["smc_analysis"]
                        print(f"📈 Symbols analyzed: {len(smc_data)}")
                        
                        for symbol, analysis in smc_data.items():
                            print(f"\n🔍 {symbol}:")
                            print(f"  💰 Price: {analysis.get('current_price', 'N/A')}")
                            print(f"  📊 Data Source: {analysis.get('data_source', 'N/A')}")
                            
                            # Market Structure
                            ms = analysis.get('market_structure', {})
                            print(f"  📈 Market Structure: {ms.get('trend', 'N/A')} ({ms.get('strength', 'N/A')})")
                            
                            # Order Blocks
                            ob = analysis.get('order_blocks', {})
                            print(f"  🔲 Order Blocks: {ob.get('count', 0)} active")
                            
                            # Fair Value Gaps
                            fvg = analysis.get('fair_value_gaps', {})
                            print(f"  ⚡ Fair Value Gaps: {fvg.get('count', 0)} active")
                            
                            # Liquidity Zones
                            liq = analysis.get('liquidity_zones', {})
                            print(f"  💧 Liquidity Zones: {liq.get('count', 0)} active")
                            
                            # Displacements
                            disp = analysis.get('displacements', {})
                            print(f"  🚀 Displacements: {disp.get('count', 0)} recent")
                            
                            # Confluence Summary
                            conf = analysis.get('confluence_summary', {})
                            print(f"  🎯 Confluence: {conf.get('signal_strength', 'N/A')} ({conf.get('confluence_strength', 0):.2f})")
                            
                            if conf.get('aligned_factors'):
                                print(f"  ✅ Aligned Factors: {', '.join(conf['aligned_factors'])}")
                    
                    else:
                        print("❌ No SMC analysis data found")
                        if "error" in data:
                            print(f"❌ Error: {data['error']}")
                
                else:
                    text = await response.text()
                    print(f"❌ Request failed: {response.status}")
                    print(f"Response: {text}")
                    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("✅ SMC API test completed!")

if __name__ == "__main__":
    asyncio.run(test_smc_api())
