const logger = require('../utils/logger');

// 404 handler
const notFound = (req, res, next) => {
    const error = new Error(`Not Found - ${req.originalUrl}`);
    res.status(404);
    next(error);
};

// Error handler
const errorHandler = (err, req, res, next) => {
    let statusCode = res.statusCode === 200 ? 500 : res.statusCode;
    let message = err.message;

    // Log error
    logger.error(`${req.method} ${req.originalUrl} - ${statusCode} - ${message}`, {
        stack: err.stack,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    });

    // TradingView specific errors
    if (err.message.includes('symbol not found')) {
        statusCode = 404;
        message = 'Symbol not found';
    } else if (err.message.includes('timeout')) {
        statusCode = 408;
        message = 'Request timeout';
    } else if (err.message.includes('rate limit')) {
        statusCode = 429;
        message = 'Rate limit exceeded';
    }

    res.status(statusCode).json({
        success: false,
        error: message,
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    });
};

module.exports = {
    notFound,
    errorHandler
};
