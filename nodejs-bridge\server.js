const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
require('dotenv').config();

const TradingViewService = require('./services/tradingview-service');
const logger = require('./utils/logger');
const { errorHandler, notFound } = require('./middleware/error-middleware');
const rateLimit = require('./middleware/rate-limit');

const app = express();
const PORT = process.env.PORT || 3001;

// Initialize TradingView service
const tvService = new TradingViewService();

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors({
    origin: process.env.ALLOWED_ORIGINS?.split(',') || '*',
    credentials: true
}));
app.use(express.json());
app.use(rateLimit);

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        service: 'TradingView EGX Bridge',
        version: '1.0.0'
    });
});

// API Routes
app.use('/api/v1', require('./routes/market-data'));

// Error handling
app.use(notFound);
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', async () => {
    logger.info('SIGTERM received, shutting down gracefully');
    await tvService.disconnect();
    process.exit(0);
});

process.on('SIGINT', async () => {
    logger.info('SIGINT received, shutting down gracefully');
    await tvService.disconnect();
    process.exit(0);
});

// Start server
app.listen(PORT, () => {
    logger.info(`🚀 TradingView EGX Bridge running on port ${PORT}`);
    logger.info(`📊 Environment: ${process.env.NODE_ENV}`);
    logger.info(`🔗 Health check: http://localhost:${PORT}/health`);
    
    // Initialize TradingView connection
    tvService.initialize().catch(err => {
        logger.error('Failed to initialize TradingView service:', err);
    });
});

module.exports = app;
