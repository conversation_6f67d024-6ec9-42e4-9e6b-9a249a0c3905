#!/usr/bin/env python3
"""
Test script to verify English translation is working correctly
"""

def test_language_config():
    """Test that language configuration loads correctly"""
    try:
        from config.language import COMMANDS, MENU, STATUS, INFO, ERRORS, HEARTBEAT, TRADING, RISK
        
        print("✅ Language configuration loaded successfully!")
        
        # Test some key translations
        print(f"📂 Main Menu: {MENU['main_menu']}")
        print(f"▶️ Start command: {COMMANDS['start_strategies']}")
        print(f"✅ Status message: {STATUS['strategies_started']}")
        print(f"🛡 Heartbeat title: {HEARTBEAT['report_title']}")
        print(f"🚀 Trading entry: {TRADING['entry_trade']}")
        print(f"❌ Error example: {ERRORS['telegram_token_missing']}")
        
        return True
    except Exception as e:
        print(f"❌ Error loading language config: {e}")
        return False

def test_menu_imports():
    """Test that menu system imports work"""
    try:
        from telegram_bot.menu import build_main_menu, build_strategy_menu
        print("✅ Menu imports successful!")
        return True
    except Exception as e:
        print(f"❌ Error importing menus: {e}")
        return False

def test_commands_imports():
    """Test that command imports work"""
    try:
        from telegram_bot.commands import cmd_start_strategies, cmd_status, cmd_balance
        print("✅ Command imports successful!")
        return True
    except Exception as e:
        print(f"❌ Error importing commands: {e}")
        return False

def test_config_imports():
    """Test that config imports work"""
    try:
        from config.settings import LIVE_MODE, DEFAULT_RISK_PCT
        from config.variables import LOSS_LIMIT_COUNT, LOSS_LIMIT_USDT
        print("✅ Config imports successful!")
        print(f"📊 Live mode: {LIVE_MODE}")
        print(f"🛡 Risk per trade: {DEFAULT_RISK_PCT}")
        print(f"⚠️ Loss limit count: {LOSS_LIMIT_COUNT}")
        return True
    except Exception as e:
        print(f"❌ Error importing config: {e}")
        return False

def main():
    """Run all translation tests"""
    print("🧪 Testing English Translation Implementation")
    print("=" * 50)
    
    tests = [
        test_language_config,
        test_menu_imports,
        test_commands_imports,
        test_config_imports,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print(f"\n🔍 Running {test.__name__}...")
        if test():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All translation tests passed! English interface is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Check the errors above.")
        return False

if __name__ == "__main__":
    main()
