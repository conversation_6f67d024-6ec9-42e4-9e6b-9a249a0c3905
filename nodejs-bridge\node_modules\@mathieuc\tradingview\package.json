{"name": "@mathieuc/tradingview", "version": "3.5.1", "description": "Tradingview instant stocks API, indicator alerts, trading bot, and more !", "main": "main.js", "scripts": {"test": "vitest", "example": "node --env-file=.env", "example:dev": "nodemon --env-file=.env"}, "repository": {"type": "git", "url": "git+https://github.com/Mathieu2301/TradingView-API.git"}, "engines": {"node": ">=14.0.0"}, "keywords": ["tradingwiew", "market", "stocks", "crypto", "forex", "indicator", "bitcoin", "api"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"axios": "^1.5.0", "jszip": "^3.7.1", "ws": "^7.4.3"}, "devDependencies": {"@babel/eslint-parser": "^7.15.7", "@mathieuc/console": "^1.0.1", "dotenv": "^16.3.1", "eslint": "^7.25.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.22.1", "vitest": "^0.31.4"}}