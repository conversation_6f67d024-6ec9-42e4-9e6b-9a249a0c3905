{"name": "@types/chai", "version": "4.3.5", "description": "TypeScript definitions for chai", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/chai", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/Bartvds", "githubUsername": "Bartvds"}, {"name": "<PERSON>", "url": "https://github.com/AGBrown", "githubUsername": "AGBrown"}, {"name": "<PERSON>", "url": "https://github.com/olivr70", "githubUsername": "olivr70"}, {"name": "<PERSON>", "url": "https://github.com/mwistrand", "githubUsername": "mwistrand"}, {"name": "<PERSON>", "url": "https://github.com/shaunluttin", "githubUsername": "s<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/micksatana", "githubUsername": "mi<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/Erik<PERSON>boom", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>gdan <PERSON>", "url": "https://github.com/bparan", "githubUsername": "b<PERSON>an"}, {"name": "CXuesong", "url": "https://github.com/CXuesong", "githubUsername": "CXuesong"}, {"name": "<PERSON>", "url": "https://github.com/joe<PERSON><PERSON>patrick", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/chai"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "af36f610304cc4afca1e6fdc2a11646e58e34a69849c12b85662361a5555ccce", "typeScriptVersion": "4.3"}