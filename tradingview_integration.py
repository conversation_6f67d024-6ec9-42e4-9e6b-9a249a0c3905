#!/usr/bin/env python3
"""
TradingView Integration for EGX SMC Dashboard
Professional chart replacement using TradingView widgets
"""

import streamlit as st
import streamlit.components.v1 as components

def create_tradingview_chart(symbol: str, analysis: dict = None, timeframe: str = "1D") -> str:
    """Create TradingView chart widget with SMC levels"""
    
    # Convert EGX symbol to TradingView format
    # EGX symbols in TradingView are usually prefixed with "EGX:"
    tv_symbol = f"EGX:{symbol}" if not symbol.startswith("EGX:") else symbol
    
    # Map timeframes to proper ranges for better display
    range_mapping = {
        "1D": "6M",    # 6 months for daily
        "1W": "2Y",    # 2 years for weekly
        "1M": "5Y",    # 5 years for monthly
        "4H": "3M",    # 3 months for 4H
        "1H": "1M"     # 1 month for hourly
    }

    chart_range = range_mapping.get(timeframe, "12M")

    # TradingView widget HTML with FORCED historical data
    tradingview_html = f"""
    <!-- TradingView Widget BEGIN -->
    <div class="tradingview-widget-container" style="height: 600px;">
      <div class="tradingview-widget-container__widget" style="height: 100%;"></div>
      <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js" async>
      {{
        "autosize": true,
        "symbol": "{tv_symbol}",
        "interval": "{timeframe}",
        "timezone": "Africa/Cairo",
        "theme": "light",
        "style": "1",
        "locale": "en",
        "enable_publishing": false,
        "allow_symbol_change": true,
        "calendar": false,
        "support_host": "https://www.tradingview.com",
        "container_id": "tradingview_chart_{timeframe}",
        "range": "{chart_range}",
        "studies": [
          "Volume@tv-basicstudies"
        ],
        "show_popup_button": true,
        "popup_width": "1200",
        "popup_height": "800",
        "toolbar_bg": "#f1f3f6",
        "withdateranges": true,
        "hide_side_toolbar": false,
        "details": true,
        "hotlist": true,
        "calendar": true,
        "studies_overrides": {{}},
        "overrides": {{
          "paneProperties.background": "#ffffff",
          "paneProperties.vertGridProperties.color": "#e1e3e6",
          "paneProperties.horzGridProperties.color": "#e1e3e6",
          "symbolWatermarkProperties.transparency": 90,
          "scalesProperties.textColor": "#333333",
          "paneProperties.backgroundType": "solid"
        }},
        "disabled_features": [
          "header_symbol_search",
          "symbol_search_hot_key"
        ],
        "enabled_features": [
          "study_templates",
          "use_localstorage_for_settings",
          "save_chart_properties_to_local_storage"
        ],
        "time_frames": [
          {{ "text": "6m", "resolution": "1D" }},
          {{ "text": "1y", "resolution": "1D" }},
          {{ "text": "2y", "resolution": "1W" }},
          {{ "text": "5y", "resolution": "1M" }}
        ]
      }}
      </script>
    </div>
    <!-- TradingView Widget END -->
    
    <style>
    .tradingview-widget-container {{
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin: 20px 0;
        background: white;
    }}
    </style>
    """
    
    return tradingview_html

def create_smc_overlay_info(analysis: dict) -> str:
    """Create SMC analysis overlay information"""
    
    if not analysis:
        return ""
    
    # Extract SMC data
    ob_data = analysis.get('order_blocks', {})
    fvg_data = analysis.get('fair_value_gaps', {})
    liq_data = analysis.get('liquidity_zones', {})
    disp_data = analysis.get('displacements', {})
    confluence = analysis.get('confluence_summary', {})
    
    # Create overlay HTML
    overlay_html = f"""
    <div class="smc-overlay">
        <div class="smc-panel">
            <h4>🎯 SMC Analysis Overlay</h4>
            
            <div class="smc-row">
                <div class="smc-item">
                    <span class="smc-label">🔲 Order Blocks:</span>
                    <span class="smc-value">{ob_data.get('count', 0)} active</span>
                    <span class="smc-status">{'✅' if ob_data.get('confluence', {}).get('confluence', False) else '❌'}</span>
                </div>
                
                <div class="smc-item">
                    <span class="smc-label">⚡ Fair Value Gaps:</span>
                    <span class="smc-value">{fvg_data.get('count', 0)} active</span>
                    <span class="smc-status">{'✅' if fvg_data.get('confluence', {}).get('confluence', False) else '❌'}</span>
                </div>
            </div>
            
            <div class="smc-row">
                <div class="smc-item">
                    <span class="smc-label">💧 Liquidity Zones:</span>
                    <span class="smc-value">{liq_data.get('count', 0)} active</span>
                    <span class="smc-status">{'✅' if liq_data.get('confluence', {}).get('confluence', False) else '❌'}</span>
                </div>
                
                <div class="smc-item">
                    <span class="smc-label">🚀 Displacements:</span>
                    <span class="smc-value">{disp_data.get('count', 0)} recent</span>
                    <span class="smc-status">{'✅' if disp_data.get('confluence', {}).get('confluence', False) else '❌'}</span>
                </div>
            </div>
            
            <div class="smc-confluence">
                <span class="smc-label">📊 Confluence:</span>
                <span class="smc-strength">{confluence.get('signal_strength', 'WEAK')}</span>
                <span class="smc-factors">({confluence.get('total_factors', 0)}/4)</span>
            </div>
        </div>
    </div>
    
    <style>
    .smc-overlay {{
        position: relative;
        margin-top: -50px;
        z-index: 1000;
        pointer-events: none;
    }}
    
    .smc-panel {{
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid #e1e3e6;
        border-radius: 8px;
        padding: 15px;
        margin: 0 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(5px);
        pointer-events: auto;
    }}
    
    .smc-panel h4 {{
        margin: 0 0 10px 0;
        color: #333;
        font-size: 16px;
    }}
    
    .smc-row {{
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
    }}
    
    .smc-item {{
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
    }}
    
    .smc-label {{
        font-weight: 500;
        color: #555;
        font-size: 14px;
    }}
    
    .smc-value {{
        color: #333;
        font-size: 14px;
    }}
    
    .smc-status {{
        font-size: 16px;
    }}
    
    .smc-confluence {{
        display: flex;
        align-items: center;
        gap: 8px;
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #e1e3e6;
    }}
    
    .smc-strength {{
        font-weight: bold;
        color: #007bff;
        font-size: 14px;
    }}
    
    .smc-factors {{
        color: #666;
        font-size: 14px;
    }}
    </style>
    """
    
    return overlay_html

def render_tradingview_chart(symbol: str, analysis: dict = None, timeframe: str = "1D"):
    """Render TradingView chart with SMC overlay"""

    st.markdown("### 📈 Professional TradingView Chart")

    # Create and display TradingView chart
    chart_html = create_tradingview_chart(symbol, analysis, timeframe)
    components.html(chart_html, height=650)
    
    # Add SMC overlay if analysis is provided
    if analysis:
        overlay_html = create_smc_overlay_info(analysis)
        components.html(overlay_html, height=120)
    
    # Add chart information with timeframe details
    range_info = {
        "1D": "6 months of daily data",
        "1W": "2 years of weekly data",
        "1M": "5 years of monthly data",
        "4H": "3 months of 4-hour data",
        "1H": "1 month of hourly data"
    }

    current_range = range_info.get(timeframe, "Historical data")

    st.info(f"""
    📊 **Professional Chart Features:**
    - Real-time EGX data for {symbol}
    - Timeframe: {timeframe} ({current_range})
    - Advanced technical indicators
    - Drawing tools and alerts
    - Professional trading interface
    - Historical data optimized for SMC analysis
    """)

def create_mini_tradingview_widget(symbol: str) -> str:
    """Create a mini TradingView widget for sidebar"""
    
    tv_symbol = f"EGX:{symbol}" if not symbol.startswith("EGX:") else symbol
    
    mini_widget = f"""
    <div class="tradingview-widget-container">
      <div class="tradingview-widget-container__widget"></div>
      <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-mini-symbol-overview.js" async>
      {{
        "symbol": "{tv_symbol}",
        "width": "100%",
        "height": "220",
        "locale": "en",
        "dateRange": "12M",
        "colorTheme": "light",
        "trendLineColor": "rgba(41, 98, 255, 1)",
        "underLineColor": "rgba(41, 98, 255, 0.3)",
        "underLineBottomColor": "rgba(41, 98, 255, 0)",
        "isTransparent": false,
        "autosize": false,
        "largeChartUrl": ""
      }}
      </script>
    </div>
    """
    
    return mini_widget

# EGX Symbol mapping for TradingView
EGX_SYMBOLS = {
    # Major Banks
    'COMI': 'EGX:COMI',  # Commercial International Bank Egypt
    'CIB': 'EGX:COMI',   # Commercial International Bank (same as COMI)
    'ABUK': 'EGX:ABUK',  # Arab Banking Corporation
    'ADIB': 'EGX:ADIB',  # Abu Dhabi Islamic Bank

    # Telecommunications
    'ETEL': 'EGX:ETEL',  # Egyptian Company for Mobile Services
    'ORTE': 'EGX:ORTE',  # Orascom Telecom

    # Real Estate & Development
    'PHDC': 'EGX:PHDC',  # Palm Hills Developments
    'TMGH': 'EGX:TMGH',  # TMG Holding
    'HRHO': 'EGX:HRHO',  # Hassan Allam Holding
    'OCDI': 'EGX:OCDI',  # Orascom Construction

    # Industrial & Manufacturing
    'IRON': 'EGX:IRON',  # Egyptian Iron & Steel
    'ESRS': 'EGX:ESRS',  # El Sewedy Electric

    # Consumer Goods
    'JUFO': 'EGX:JUFO',  # Juhayna Food Industries
    'DOMTY': 'EGX:DOMTY', # Domty

    # Fallback for unknown symbols
}

def get_tradingview_symbol(symbol: str) -> str:
    """Convert local symbol to TradingView format"""
    return EGX_SYMBOLS.get(symbol.upper(), f"EGX:{symbol.upper()}")

if __name__ == "__main__":
    # Test the TradingView integration
    print("🇪🇬 TradingView Integration for EGX SMC Dashboard")
    print("=" * 50)
    print("✅ Professional chart replacement ready")
    print("✅ Real-time EGX data support")
    print("✅ SMC overlay integration")
    print("✅ Mobile responsive design")

    # Test symbol conversion
    test_symbols = ['COMI', 'CIB', 'ETEL']
    print("\n📊 Symbol Conversion Test:")
    for symbol in test_symbols:
        tv_symbol = get_tradingview_symbol(symbol)
        print(f"   {symbol} → {tv_symbol}")

    print("\n🎯 TradingView Features:")
    print("   📈 Real-time EGX stock data")
    print("   🎛️ Professional trading tools")
    print("   📱 Mobile responsive design")
    print("   ⚡ SMC analysis overlay")
    print("   🔔 Price alerts and notifications")
