"""
EGX (Egyptian Exchange) configuration for the trading bot
Contains symbol definitions, market hours, and EGX-specific settings
"""

from datetime import time
from typing import Dict, List

# EGX Market Configuration
EGX_EXCHANGE = "EGX"
EGX_CURRENCY = "EGP"  # Egyptian Pound
EGX_TIMEZONE = "Africa/Cairo"

# Market Hours (Cairo Time)
EGX_MARKET_OPEN = time(10, 0)   # 10:00 AM
EGX_MARKET_CLOSE = time(14, 30)  # 2:30 PM
EGX_MARKET_DAYS = [0, 1, 2, 3, 6]  # Monday to Thursday, Sunday (0=Monday, 6=Sunday)

# Trading Configuration
EGX_MIN_ORDER_SIZE = 1  # Minimum number of shares
EGX_TICK_SIZE = 0.01    # Minimum price increment
EGX_MAX_POSITION_SIZE = 10000  # Maximum shares per position

# Commission and Fees (approximate)
EGX_COMMISSION_RATE = 0.0015  # 0.15% commission
EGX_STAMP_DUTY = 0.0015       # 0.15% stamp duty
EGX_TOTAL_FEES = EGX_COMMISSION_RATE + EGX_STAMP_DUTY

# Common EGX Symbols with Metadata
EGX_SYMBOLS = {
    # Banking Sector
    "COMI": {
        "name": "Commercial International Bank",
        "sector": "Banking",
        "market_cap": "Large",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01,
        "tradingview_symbol": "COMI",
        "common_name": "CIB"
    },
    
    # Telecommunications
    "ETEL": {
        "name": "Egyptian Company for Mobile Services",
        "sector": "Telecommunications",
        "market_cap": "Large",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01
    },
    "ORTE": {
        "name": "Orascom Telecom Media and Technology",
        "sector": "Telecommunications",
        "market_cap": "Medium",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01
    },
    
    # Real Estate
    "HELI": {
        "name": "Heliopolis Housing",
        "sector": "Real Estate",
        "market_cap": "Large",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01
    },
    "PHDC": {
        "name": "Palm Hills Developments",
        "sector": "Real Estate",
        "market_cap": "Medium",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01
    },
    "TMGH": {
        "name": "TMG Holding",
        "sector": "Real Estate",
        "market_cap": "Medium",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01
    },
    
    # Energy & Utilities
    "EGAS": {
        "name": "Egyptian Natural Gas Company",
        "sector": "Energy",
        "market_cap": "Large",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01
    },
    
    # Industrial
    "SWDY": {
        "name": "El Sewedy Electric Company",
        "sector": "Industrial",
        "market_cap": "Large",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01
    },
    "IRON": {
        "name": "Iron and Steel Company",
        "sector": "Industrial",
        "market_cap": "Medium",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01
    },
    
    # Construction
    "HRHO": {
        "name": "Hassan Allam Holding",
        "sector": "Construction",
        "market_cap": "Medium",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01
    },
    "OCDI": {
        "name": "Orascom Construction Industries",
        "sector": "Construction",
        "market_cap": "Large",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01
    },
    
    # Consumer Goods
    "JUFO": {
        "name": "Juhayna Food Industries",
        "sector": "Consumer Goods",
        "market_cap": "Medium",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01
    },
    "EAST": {
        "name": "Eastern Company",
        "sector": "Consumer Goods",
        "market_cap": "Medium",
        "currency": "EGP",
        "lot_size": 1,
        "tick_size": 0.01
    }
}

# Symbol Groups for Strategy Application
SYMBOL_GROUPS = {
    "banking": ["COMI"],
    "telecom": ["ETEL", "ORTE"],
    "real_estate": ["HELI", "PHDC", "TMGH"],
    "energy": ["EGAS"],
    "industrial": ["SWDY", "IRON"],
    "construction": ["HRHO", "OCDI"],
    "consumer": ["JUFO", "EAST"],
    "large_cap": ["COMI", "ETEL", "HELI", "EGAS", "SWDY", "OCDI"],
    "medium_cap": ["ORTE", "PHDC", "TMGH", "IRON", "HRHO", "JUFO", "EAST"]
}

# Default symbols for trading (most liquid) - using correct TradingView symbols
DEFAULT_TRADING_SYMBOLS = ["COMI", "ETEL", "HELI", "EGAS", "SWDY"]

# Risk Management Settings for EGX
EGX_RISK_SETTINGS = {
    "max_position_size_pct": 10.0,  # Maximum 10% of portfolio per position
    "max_sector_exposure_pct": 25.0,  # Maximum 25% exposure per sector
    "max_daily_trades": 5,  # Maximum trades per day
    "min_liquidity_volume": 100000,  # Minimum daily volume
    "max_spread_pct": 2.0,  # Maximum bid-ask spread percentage
}

# Timeframes for EGX Analysis
EGX_TIMEFRAMES = {
    "scalping": ["1m", "3m", "5m"],
    "intraday": ["15m", "30m", "1h"],
    "swing": ["2h", "4h", "1d"],
    "position": ["1d", "1w"]
}

# Market Holidays (approximate - should be updated annually)
EGX_HOLIDAYS_2024 = [
    "2024-01-01",  # New Year
    "2024-01-25",  # Revolution Day
    "2024-04-25",  # Sinai Liberation Day
    "2024-05-01",  # Labor Day
    "2024-07-23",  # Revolution Day
    # Add Islamic holidays (dates vary each year)
    # Add Coptic holidays
]

def get_symbol_info(symbol: str) -> Dict:
    """Get information about an EGX symbol"""
    return EGX_SYMBOLS.get(symbol, {})

def get_symbols_by_sector(sector: str) -> List[str]:
    """Get all symbols in a specific sector"""
    return [symbol for symbol, info in EGX_SYMBOLS.items() 
            if info.get("sector", "").lower() == sector.lower()]

def get_symbols_by_market_cap(market_cap: str) -> List[str]:
    """Get symbols by market capitalization"""
    return [symbol for symbol, info in EGX_SYMBOLS.items() 
            if info.get("market_cap", "").lower() == market_cap.lower()]

def is_market_open() -> bool:
    """Check if EGX market is currently open"""
    from datetime import datetime
    import pytz
    
    try:
        cairo_tz = pytz.timezone(EGX_TIMEZONE)
        now = datetime.now(cairo_tz)
        
        # Check if it's a trading day
        if now.weekday() not in EGX_MARKET_DAYS:
            return False
        
        # Check if it's within trading hours
        current_time = now.time()
        return EGX_MARKET_OPEN <= current_time <= EGX_MARKET_CLOSE
        
    except Exception:
        # If timezone handling fails, assume market is closed
        return False

def get_market_status() -> Dict:
    """Get detailed market status information"""
    from datetime import datetime
    import pytz
    
    try:
        cairo_tz = pytz.timezone(EGX_TIMEZONE)
        now = datetime.now(cairo_tz)
        
        is_trading_day = now.weekday() in EGX_MARKET_DAYS
        current_time = now.time()
        is_open = is_trading_day and EGX_MARKET_OPEN <= current_time <= EGX_MARKET_CLOSE
        
        return {
            "is_open": is_open,
            "is_trading_day": is_trading_day,
            "current_time": current_time.strftime("%H:%M:%S"),
            "market_open": EGX_MARKET_OPEN.strftime("%H:%M"),
            "market_close": EGX_MARKET_CLOSE.strftime("%H:%M"),
            "timezone": EGX_TIMEZONE,
            "next_open": None,  # Could be calculated
            "next_close": None   # Could be calculated
        }
        
    except Exception as e:
        return {
            "is_open": False,
            "error": str(e)
        }
