#!/usr/bin/env python3
"""
TradingView Data Fetcher
Extract real-time OHLCV data from TradingView for SMC analysis
"""

import pandas as pd
from typing import Optional, Dict, List

try:
    from tvDatafeed import TvDatafeed, Interval
    TVDATAFEED_AVAILABLE = True
except ImportError:
    TVDATAFEED_AVAILABLE = False
    # Create dummy Interval class for when tvdatafeed is not available
    class Interval:
        in_1_minute = "1m"
        in_5_minute = "5m"
        in_15_minute = "15m"
        in_30_minute = "30m"
        in_1_hour = "1h"
        in_2_hour = "2h"
        in_4_hour = "4h"
        in_daily = "1D"
        in_weekly = "1W"
        in_monthly = "1M"

class TradingViewDataFetcher:
    """Fetch real-time data from TradingView"""
    
    def __init__(self):
        self.tv = None
        self.connected = False
        self.initialize_connection()
    
    def initialize_connection(self):
        """Initialize TradingView connection"""
        try:
            if not TVDATAFEED_AVAILABLE:
                print("❌ tvdatafeed package not available")
                return False

            # Initialize without credentials (anonymous access)
            self.tv = TvDatafeed()
            self.connected = True
            print("✅ Connected to TradingView data feed")
            return True

        except Exception as e:
            print(f"❌ Failed to connect to TradingView: {e}")
            self.connected = False
            return False
    
    def get_interval_mapping(self, timeframe: str) -> Interval:
        """Map timeframe string to TradingView Interval"""
        mapping = {
            '1m': Interval.in_1_minute,
            '5m': Interval.in_5_minute,
            '15m': Interval.in_15_minute,
            '30m': Interval.in_30_minute,
            '1h': Interval.in_1_hour,
            '2h': Interval.in_2_hour,
            '4h': Interval.in_4_hour,
            '1D': Interval.in_daily,
            '1W': Interval.in_weekly,
            '1M': Interval.in_monthly
        }
        return mapping.get(timeframe, Interval.in_daily)
    
    def get_egx_symbol_mapping(self, symbol: str) -> str:
        """Map local EGX symbol to TradingView format"""
        
        # EGX symbol mappings for TradingView
        egx_mappings = {
            # Major Banks
            'CIB': 'COMI',  # Commercial International Bank
            'COMI': 'COMI',
            'ADIB': 'ADIB',  # Abu Dhabi Islamic Bank
            'ALEX': 'ALEX',  # Bank of Alexandria
            'SAIB': 'SAIB',  # Société Arabe Internationale de Banque
            
            # Telecom
            'ETEL': 'ETEL',  # Egyptian Company for Mobile Services
            'ORTE': 'ORTE',  # Orascom Telecom
            
            # Real Estate
            'PHDC': 'PHDC',  # Palm Hills Developments
            'SODIC': 'SODIC', # Sixth of October Development & Investment
            'TMG': 'TMG',    # Talaat Moustafa Group
            
            # Industrial
            'IRON': 'IRON',  # Egyptian Iron & Steel
            'ESRS': 'ESRS',  # Egyptian Steel
            
            # Consumer Goods
            'JUFO': 'JUFO',  # Juhayna Food Industries
            'DOMTY': 'DOMTY', # Domty
        }
        
        # Clean symbol and get mapping
        clean_symbol = symbol.upper().replace('.CA', '').replace('EGX:', '')
        mapped_symbol = egx_mappings.get(clean_symbol, clean_symbol)
        
        return mapped_symbol
    
    def fetch_data(self, symbol: str, timeframe: str = '1D', bars: int = 500) -> Optional[pd.DataFrame]:
        """Fetch OHLCV data from TradingView"""

        if not self.connected:
            print("❌ Not connected to TradingView")
            return None

        try:
            # Map symbol and timeframe
            tv_symbol = self.get_egx_symbol_mapping(symbol)
            interval = self.get_interval_mapping(timeframe)

            print(f"📡 Fetching {tv_symbol} data from TradingView ({timeframe}, {bars} bars)...")

            # Fetch data from TradingView
            data = self.tv.get_hist(
                symbol=tv_symbol,
                exchange='EGX',  # Egyptian Exchange
                interval=interval,
                n_bars=bars
            )

            if data is None or data.empty:
                print(f"⚠️ No data received for {tv_symbol}")
                return None

            # Standardize column names for SMC analysis
            data = data.rename(columns={
                'open': 'open',
                'high': 'high',
                'low': 'low',
                'close': 'close',
                'volume': 'volume'
            })

            # Ensure all required columns exist
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in data.columns:
                    print(f"❌ Missing column: {col}")
                    return None

            # Convert to numeric and clean data
            for col in required_columns:
                data[col] = pd.to_numeric(data[col], errors='coerce')

            # Remove any rows with NaN values
            data = data.dropna()

            if len(data) < 50:
                print(f"⚠️ Insufficient data for {tv_symbol} (got {len(data)}, need at least 50)")
                return None

            # Sort by datetime index
            data = data.sort_index()

            print(f"✅ Fetched {len(data)} bars for {tv_symbol}")

            return data

        except Exception as e:
            print(f"❌ Error fetching data for {symbol}: {e}")
            return None
    
    def get_available_symbols(self) -> List[str]:
        """Get list of available EGX symbols"""
        return [
            'CIB', 'COMI', 'ADIB', 'ALEX', 'SAIB',  # Banks
            'ETEL', 'ORTE',  # Telecom
            'PHDC', 'SODIC', 'TMG',  # Real Estate
            'IRON', 'ESRS',  # Industrial
            'JUFO', 'DOMTY'  # Consumer Goods
        ]
    
    def test_connection(self) -> Dict:
        """Test TradingView connection with sample data"""
        
        test_results = {
            'connected': self.connected,
            'test_symbol': 'CIB',
            'test_successful': False,
            'data_points': 0,
            'error': None
        }
        
        if not self.connected:
            test_results['error'] = "Not connected to TradingView"
            return test_results
        
        try:
            # Test with CIB (Commercial International Bank)
            test_data = self.fetch_data('CIB', '1D', 100)
            
            if test_data is not None and not test_data.empty:
                test_results['test_successful'] = True
                test_results['data_points'] = len(test_data)
            else:
                test_results['error'] = "No data received"
                
        except Exception as e:
            test_results['error'] = str(e)
        
        return test_results

# Global instance
tv_fetcher = TradingViewDataFetcher()

def get_tradingview_data(symbol: str, timeframe: str = '1D', bars: int = 500) -> Optional[pd.DataFrame]:
    """Convenience function to fetch TradingView data"""
    return tv_fetcher.fetch_data(symbol, timeframe, bars)

def test_tradingview_connection() -> Dict:
    """Test TradingView data connection"""
    return tv_fetcher.test_connection()
