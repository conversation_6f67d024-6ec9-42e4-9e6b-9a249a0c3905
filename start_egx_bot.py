#!/usr/bin/env python3
"""
EGX Trading Bot Startup Script
Simple script to start the EGX trading bot with proper initialization
"""

import asyncio
import sys
import os
from datetime import datetime

# Add project root to path
sys.path.append('.')

from launcher_egx import main as egx_main
from log_setup import logger
from utils.telegram_utils import send_telegram_message

def print_banner():
    """Print startup banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🇪🇬 EGX TRADING BOT 🇪🇬                    ║
    ║                                                              ║
    ║              Egyptian Exchange Stock Trading Bot             ║
    ║                   Powered by TradingView                     ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)
    print(f"🕐 Starting at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python version: {sys.version}")
    print(f"📁 Working directory: {os.getcwd()}")
    print("=" * 66)

async def check_prerequisites():
    """Check if all prerequisites are met"""
    logger.info("🔍 Checking prerequisites...")
    
    # Check if Node.js bridge is running
    try:
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:3001/health", timeout=5) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ TradingView bridge is running: {data.get('status', 'Unknown')}")
                else:
                    logger.error("❌ TradingView bridge returned error status")
                    return False
    except Exception as e:
        logger.error(f"❌ TradingView bridge not accessible: {e}")
        logger.error("💡 Make sure to start the Node.js bridge first:")
        logger.error("   cd nodejs-bridge && npm start")
        return False
    
    # Check configuration files
    config_files = [
        "config/pairs.json",
        "config/egx_config.py",
        "api/egx_async.py"
    ]
    
    for config_file in config_files:
        if not os.path.exists(config_file):
            logger.error(f"❌ Missing configuration file: {config_file}")
            return False
        else:
            logger.info(f"✅ Found: {config_file}")
    
    # Test EGX API connection
    try:
        from api.egx_async import check_bridge_status
        status = await check_bridge_status()
        logger.info(f"✅ EGX bridge status: Connected={status.get('connected', False)}")
    except Exception as e:
        logger.error(f"❌ EGX API test failed: {e}")
        return False
    
    logger.info("✅ All prerequisites met!")
    return True

async def startup_test():
    """Run startup tests"""
    logger.info("🧪 Running startup tests...")
    
    try:
        # Test symbol fetching
        from api.egx_async import get_common_egx_symbols
        symbols = await get_common_egx_symbols()
        logger.info(f"✅ Available EGX symbols: {len(symbols)}")
        
        # Test price fetching for one symbol
        from api.egx_async import get_current_price
        test_symbol = "COMI"
        price_data = await get_current_price(test_symbol)
        logger.info(f"✅ Test price for {test_symbol}: {price_data['price']:.2f} EGP")
        
        # Test market status
        from config.egx_config import get_market_status
        market_status = get_market_status()
        logger.info(f"✅ Market status: {'OPEN' if market_status.get('is_open') else 'CLOSED'}")
        
        logger.info("✅ All startup tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Startup test failed: {e}")
        return False

async def main():
    """Main startup function"""
    print_banner()
    
    try:
        # Check prerequisites
        if not await check_prerequisites():
            logger.error("❌ Prerequisites not met. Exiting.")
            return False
        
        # Run startup tests
        if not await startup_test():
            logger.error("❌ Startup tests failed. Exiting.")
            return False
        
        # Send startup notification
        await send_telegram_message("🇪🇬 EGX Trading Bot is starting up...")
        
        # Start the main bot
        logger.info("🚀 Starting EGX Trading Bot...")
        await egx_main()
        
    except KeyboardInterrupt:
        logger.info("⏹️ Bot stopped by user (Ctrl+C)")
        await send_telegram_message("⏹️ EGX Trading Bot stopped by user")
        return True
        
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        await send_telegram_message(f"❌ EGX Trading Bot crashed: {str(e)[:100]}")
        return False

if __name__ == "__main__":
    # Set up Windows event loop policy if needed
    if sys.platform.startswith("win"):
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    # Run with restart capability
    restart_count = 0
    max_restarts = 3
    
    while restart_count < max_restarts:
        try:
            success = asyncio.run(main())
            if success:
                break
            else:
                restart_count += 1
                if restart_count < max_restarts:
                    logger.info(f"🔄 Restarting bot (attempt {restart_count + 1}/{max_restarts})...")
                    asyncio.run(asyncio.sleep(30))
                
        except Exception as e:
            logger.error(f"❌ Unexpected error: {e}")
            restart_count += 1
            if restart_count < max_restarts:
                logger.info(f"🔄 Restarting after error (attempt {restart_count + 1}/{max_restarts})...")
                asyncio.run(asyncio.sleep(30))
    
    if restart_count >= max_restarts:
        logger.error("❌ Maximum restart attempts reached. Exiting.")
        sys.exit(1)
    else:
        logger.info("✅ Bot exited successfully.")
        sys.exit(0)
