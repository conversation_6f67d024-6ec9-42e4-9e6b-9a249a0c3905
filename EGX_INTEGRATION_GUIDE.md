# 🇪🇬 EGX Trading Bot Integration Guide

## 🎯 Overview

Your trading bot has been successfully adapted from **Bybit cryptocurrency trading** to **EGX (Egyptian Exchange) stock trading**. The bot now trades Egyptian stocks using real-time data from TradingView.

## 🔄 What Changed

### **Before (Bybit Crypto)**
- ❌ Crypto futures trading (BTC/USDT, ETH/USDT, etc.)
- ❌ Leverage trading (3x-5x)
- ❌ 24/7 trading
- ❌ USD-based

### **After (EGX Stocks)**
- ✅ Egyptian stock trading (COMI, ETEL, HELI, etc.)
- ✅ Cash trading (no leverage)
- ✅ Market hours trading (10:00-14:30 Cairo time)
- ✅ EGP-based

## 📁 New Files Created

### **Core EGX Files**
- `trade_executor_egx.py` - EGX trading executor (replaces Bybit functionality)
- `launcher_egx.py` - EGX bot launcher (replaces launcher_async.py)
- `api/egx_async.py` - EGX API client (replaces api/bybit_async.py)
- `config/egx_config.py` - EGX market configuration
- `strategies/smc_strategy_egx.py` - EGX-adapted SMC strategy

### **Integration Files**
- `start_egx_bot.py` - Simple startup script
- `test_egx_integration.py` - Integration test suite
- `nodejs-bridge/` - TradingView data bridge

### **Updated Files**
- `config/pairs.json` - Now contains EGX symbols instead of crypto pairs

## 🚀 How to Start the EGX Bot

### **Method 1: Simple Startup (Recommended)**
```bash
python start_egx_bot.py
```

### **Method 2: Direct Launch**
```bash
python launcher_egx.py
```

## 📊 EGX Symbols Being Traded

The bot now trades these Egyptian stocks:

| Symbol | Company Name | Sector |
|--------|-------------|---------|
| **COMI** | Commercial International Bank | Banking |
| **ETEL** | Egyptian Company for Mobile Services | Telecom |
| **HELI** | Heliopolis Housing | Real Estate |
| **EGAS** | Egyptian Natural Gas Company | Energy |
| **SWDY** | El Sewedy Electric Company | Industrial |
| **HRHO** | Hassan Allam Holding | Construction |
| **PHDC** | Palm Hills Developments | Real Estate |
| **TMGH** | TMG Holding | Real Estate |

## ⏰ Trading Schedule

- **Market Hours**: 10:00 AM - 2:30 PM (Cairo Time)
- **Trading Days**: Sunday to Thursday
- **Holidays**: Egyptian national and religious holidays

## 💰 Trading Logic Changes

### **Position Sizing**
- **Before**: Leverage-based position sizing
- **After**: Cash-based share calculation
- **Risk**: 2% of capital per trade (vs 1% for crypto)

### **Entry/Exit**
- **Before**: Market orders with automatic SL/TP
- **After**: Stock purchases with percentage-based targets
- **Stop Loss**: 5% (vs dynamic for crypto)
- **Take Profit**: 10% (vs dynamic for crypto)

### **Strategy Adaptations**
- **Timeframe**: 1-hour candles (same as crypto)
- **Confidence**: Higher threshold (40% vs 30%)
- **Re-entry**: 1-hour cooldown (vs 30 minutes)
- **Direction**: Long-only (no short selling)

## 🔧 Configuration

### **Capital Settings**
```python
# In trade_executor_egx.py
egx_balance = 100000.0  # Starting capital in EGP
```

### **Risk Settings**
```python
# In strategies/smc_strategy_egx.py
default_risk_pct = 0.02  # 2% risk per trade
sl_percent = 5.0         # 5% stop loss
tp_percent = 10.0        # 10% take profit
```

### **Symbol Selection**
Edit `config/pairs.json` to change traded symbols:
```json
{
    "pairs": [
        "COMI",
        "ETEL", 
        "HELI",
        "EGAS",
        "SWDY"
    ]
}
```

## 📈 Performance Monitoring

### **Balance Tracking**
- **Total Portfolio Value**: Cash + Stock positions
- **Free Cash**: Available for new trades
- **Used Capital**: Value of current positions

### **Position Monitoring**
- **Unrealized P&L**: Mark-to-market of open positions
- **Realized P&L**: Closed position profits/losses
- **Position Count**: Number of stocks held

### **Telegram Notifications**
All existing Telegram notifications work:
- 🇪🇬 Trade entries/exits
- 📊 Daily performance
- ⚠️ Errors and alerts
- 🛑 Cooldown activations

## 🔍 Monitoring & Debugging

### **Log Files**
- `logs/egx_signal_debug.log` - EGX strategy signals
- `logs/bridge.log` - TradingView bridge logs
- Main bot logs (unchanged)

### **Health Checks**
```bash
# Check bridge status
curl http://localhost:3001/health

# Test EGX integration
python test_egx_integration.py

# Check current prices
python -c "import asyncio; from api.egx_async import get_current_price; print(asyncio.run(get_current_price('COMI')))"
```

## 🛠️ Troubleshooting

### **Common Issues**

1. **"Bridge not accessible"**
   ```bash
   cd nodejs-bridge
   npm start
   ```

2. **"Market is closed"**
   - Check EGX trading hours (10:00-14:30 Cairo time)
   - Verify it's a trading day (Sunday-Thursday)

3. **"No EGX symbols available"**
   - Check `config/pairs.json`
   - Verify symbols exist in `config/egx_config.py`

4. **"Insufficient balance"**
   - Check paper trading balance in `trade_executor_egx.py`
   - Adjust starting capital if needed

### **Performance Issues**
- **Slow responses**: Check TradingView bridge logs
- **Failed trades**: Verify market hours and symbol availability
- **No signals**: Check strategy confidence thresholds

## 🔄 Switching Back to Crypto

If you want to switch back to crypto trading:

1. **Stop EGX bot**: `Ctrl+C`
2. **Start crypto bot**: `python launcher_async.py`
3. **Update pairs**: Edit `config/pairs.json` with crypto symbols

## 📞 Support

### **Log Analysis**
1. Check `logs/egx_signal_debug.log` for strategy issues
2. Check `nodejs-bridge/logs/bridge.log` for data issues
3. Check main bot logs for execution issues

### **Testing**
```bash
# Full integration test
python test_egx_integration.py

# Bridge test
cd nodejs-bridge && npm test

# Strategy test
python -c "import asyncio; from strategies.smc_strategy_egx import run_smc_strategy; print(asyncio.run(run_smc_strategy('COMI', 100000)))"
```

## 🎉 Success Metrics

Your EGX integration is working when you see:
- ✅ **Bridge Connected**: TradingView data flowing
- ✅ **Prices Accurate**: Real EGX stock prices (e.g., COMI ~80.30 EGP)
- ✅ **Signals Generated**: EGX strategy producing buy signals
- ✅ **Positions Tracked**: Stock holdings monitored
- ✅ **Telegram Active**: Notifications working

---

**🇪🇬 Welcome to EGX stock trading! Your bot is now trading Egyptian stocks instead of crypto! 📈**
