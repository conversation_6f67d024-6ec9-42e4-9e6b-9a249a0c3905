export { A as Arrayable, a as Awaitable, C as CreateHotContextFunction, g as DebuggerOptions, D as DepsHandlingOptions, b as FetchFunction, F as FetchResult, H as HotContext, d as ModuleCache, M as ModuleCacheMap, N as Nullable, R as RawSourceMap, c as ResolveIdFunction, S as StartOfSourceMap, e as ViteNodeResolveId, V as ViteNodeRunnerOptions, f as ViteNodeServerOptions } from './types-33f0e4ff.js';
export { D as DecodedSourceMap, E as EncodedSourceMap } from './types.d-7442d07f.js';
import 'vite/types/hot';
