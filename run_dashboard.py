#!/usr/bin/env python3
"""
SMC Dashboard Launcher
Installs dependencies and runs the dashboard
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Installed {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ Failed to install {package}")
        return False

def check_and_install_dependencies():
    """Check and install required dependencies"""
    
    required_packages = [
        "streamlit>=1.28.0",
        "pandas>=2.0.0", 
        "numpy>=1.24.0",
        "plotly>=5.15.0",
        "ta>=0.10.2",
        "python-dateutil",
        "protobuf"
    ]
    
    print("🔍 Checking dependencies...")
    
    for package in required_packages:
        try:
            # Try to import the main module
            module_name = package.split(">=")[0].split("==")[0]
            if module_name == "python-dateutil":
                module_name = "dateutil"
            
            __import__(module_name)
            print(f"✅ {module_name} is available")
            
        except ImportError:
            print(f"❌ {module_name} not found, installing...")
            install_package(package)

def run_dashboard():
    """Run the SMC dashboard"""
    try:
        print("\n🚀 Starting EGX Professional SMC Dashboard...")
        print("📊 Dashboard will open in your browser at http://localhost:8501")
        print("🛑 Press Ctrl+C to stop the dashboard\n")
        
        # Run streamlit
        subprocess.run([sys.executable, "-m", "streamlit", "run", "streamlit_smc_dashboard.py"])
        
    except KeyboardInterrupt:
        print("\n🛑 Dashboard stopped by user")
    except Exception as e:
        print(f"❌ Error running dashboard: {e}")
        print("\n💡 Try running manually:")
        print("   python -m streamlit run streamlit_smc_dashboard.py")

if __name__ == "__main__":
    print("🇪🇬 EGX Professional SMC Dashboard Launcher")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not os.path.exists("streamlit_smc_dashboard.py"):
        print("❌ streamlit_smc_dashboard.py not found!")
        print("💡 Make sure you're in the correct directory")
        sys.exit(1)
    
    # Check and install dependencies
    check_and_install_dependencies()
    
    # Run the dashboard
    run_dashboard()
