<!--
 Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
    <link rel="shortcut icon" href="./favicon.png" />
    <link
      rel="preload"
      href="./static/media/SourceSansPro-Regular.DZLUzqI4.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="./static/media/SourceSansPro-SemiBold.sKQIyTMz.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />
    <link
      rel="preload"
      href="./static/media/SourceSansPro-Bold.-6c9oR8J.woff2"
      as="font"
      type="font/woff2"
      crossorigin
    />

    <title>Streamlit</title>

    <!-- initialize window.prerenderReady to false and then set to true in React app when app is ready for indexing -->
    <script>
      window.prerenderReady = false
    </script>
    <script type="module" crossorigin src="./static/js/index.BqDl3eRM.js"></script>
    <link rel="stylesheet" crossorigin href="./static/css/index.DqDwtg6_.css">
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>
