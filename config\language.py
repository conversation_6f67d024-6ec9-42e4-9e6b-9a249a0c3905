# config/language.py
# Language configuration for the trading bot

# Bot Commands and Descriptions
COMMANDS = {
    "start": "📂 Main Menu",
    "start_strategies": "▶️ Start Strategies",
    "stop_strategies": "⏹ Stop Strategies", 
    "pause_trading": "⏸ Pause Entries",
    "resume_trading": "▶️ Resume Entries",
    "halt": "🛑 Full Stop (halt.flag)",
    "unhalt": "♻️ Remove halt and allow startup",
    "status": "📊 Strategy Status",
    "positions": "📈 Open Positions",
    "balance": "💰 Account Balance",
    "restart_bot": "♻️ Restart Bot",
    "risk": "🛡 Risk Parameters",
    "reentry": "🔁 Re-entries",
    "version": "📦 Bot Version",
    "optimize_confidence": "🧠 Optimize Confidence Weights",
    "heartbeat": "🛡 Bot Status (HEARTBEAT)",
    "debug_status": "🧠 Debug: Strategy Status",
    "debug_threads": "📚 Debug: Active Threads",
    "debug_memory": "📀 Debug: Memory",
    "debug_weights": "📊 Debug: Confidence Weights",
    "debug_signals": "🔍 Debug: Recent Signals",
    "last_signal": "📄 Last Signal by Symbol",
    "equity_plot": "📈 Equity Curve Chart",
    "drawdown_plot": "📉 Drawdown Curve Chart",
    "equity_balance": "💼 Current Equity Balance",
    "pnl_summary": "📊 PnL Summary",
}

# Menu Items
MENU = {
    "main_menu": "📂 Main Menu:",
    "strategies": "📈 Strategies",
    "monitoring": "📊 Monitoring", 
    "risk_management": "🛡 Risk Management",
    "settings": "⚙️ Settings",
    "back": "⬅️ Back",
    "risk_status": "🛡 Risk Status",
    "reentries": "🔁 Re-entries",
    "pnl_summary": "📦 PnL Summary",
}

# Status Messages
STATUS = {
    "strategies_running": "▶️ Strategies are already running.",
    "strategies_started": "✅ Strategies started.",
    "strategies_stopped": "⏹ Strategies stopped.",
    "already_stopped": "⛔ Already stopped.",
    "restarting_strategies": "♻️ Restarting strategies...",
    "restart_error": "❌ Error during restart: {}",
    "active_hourly": "✅ Active (hourly)",
    "not_started": "⛔ Not started",
    "strategy_status": "📊 Strategy Status: {}",
    "halt_removed": "♻️ Halt removed. Trading allowed.",
    "bot_working": "✅ Bot is working normally.",
}

# Trading Messages
TRADING = {
    "entry_trade": "🚀 <b>TRADE ENTRY</b>",
    "volume": "• Volume: <b>{}</b>",
    "entry_price": "• Entry Price: <b>{}</b>",
    "tp_reached": "✅ TP reached for <b>{}</b>",
    "sl_triggered": "🛑 SL triggered for <b>{}</b>",
    "position_closed_manually": "🚪 Position for <b>{}</b> closed manually.",
    "result_for": "{} <b>Result for {}</b>: PnL = <b>{:.2f} USDT</b>",
    "flip_position": "🔄 Flip for <b>{}</b>",
}

# Error Messages
ERRORS = {
    "telegram_token_missing": "❌ Error: TELEGRAM_TOKEN or TELEGRAM_CHAT_ID not set in .env file",
    "telegram_token_not_set": "❌ TELEGRAM_TOKEN not set in environment variables.",
    "telegram_token_env_missing": "❌ TELEGRAM_TOKEN not set in .env",
    "telegram_chat_id_missing": "❌ TELEGRAM_CHAT_ID not set in .env",
    "heartbeat_error": "❌ Error executing heartbeat command.\n{}",
    "telegram_send_error": "❌ Error sending message to Telegram: {}",
    "telegram_photo_error": "❌ Error sending photo to Telegram: {}",
}

# Info Messages
INFO = {
    "telegram_loaded": "✅ Telegram token and chat ID successfully loaded.",
    "active_strategies": "📘 Active strategies:\n- SMC (timeframe: 1H)",
    "analyzing_pairs": "🔍 Currently analyzing pairs: BTC/USDT, ETH/USDT",
    "reentry_activated": "🔁 Re-entry activated.",
    "risk_info": "🛡 Risk: 1% per trade\nRR: 2.5",
    "bot_version": "📦 Bot version: v3.3",
    "strategy_version": "📦 Strategy version: v1.0",
    "recent_signals": "🔍 Recent signals:\n- BTC/USDT: BUY\n- ETH/USDT: SELL",
}

# Heartbeat Report
HEARTBEAT = {
    "report_title": "🛡 <b>HEARTBEAT REPORT:</b>\n\n",
    "balance": "• 💰 Balance: <b>{:.2f} USDT</b>\n",
    "open_positions": "• 📉 Open positions: <b>{}</b>\n", 
    "total_pnl": "• 📈 Total PnL: <b>{:+.2f} USDT</b>\n",
    "strategies": "• 🧠 Strategies: <b>{}</b>\n",
    "uptime": "• ⏰ Uptime: <b>{}</b>\n",
    "working_normally": "• ✅ Bot working normally.",
}

# Risk and Limits
RISK = {
    "loss_limit_count": "Maximum consecutive losses",
    "loss_limit_usdt": "Maximum loss amount per period ($)",
    "risk_reset_interval": "Risk limits reset period in seconds (1 hour)",
    "trading_disabled": "⛔ Trading disabled! Position opening for {} cancelled.",
    "risk_limit_reached": "⛔ Trade rejected for {}: daily loss limit reached.",
    "consecutive_loss_limit": "⛔ Consecutive loss limit exceeded.",
    "total_loss_limit": "⛔ Total loss limit exceeded.",
    "bot_stopped_consecutive": "⚠️ Bot stopped: consecutive loss limit.",
    "bot_stopped_total": "⚠️ Bot stopped: total loss limit.",
}

# Comments for variables.py
COMMENTS = {
    "risk_limits_settings": "Risk limits settings",
    "max_consecutive_losses": "Maximum number of consecutive losses",
    "max_loss_amount": "Maximum loss amount per period ($)",
    "reset_interval": "Risk limits reset period in seconds (1 hour)",
}
