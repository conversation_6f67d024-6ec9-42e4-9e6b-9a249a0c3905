"""
EGX Trading Bot Launcher - Replaces launcher_async.py for Egyptian stock market
Handles EGX stock trading instead of crypto futures
"""

import asyncio
import time
import nest_asyncio
import json

from trade_executor_egx import (
    fetch_egx_balance, get_egx_open_positions, init_egx_exchange,
    place_egx_order, close_stock_position
)
from api.egx_async import get_ohlcv
from config.egx_config import (
    DEFAULT_TRADING_SYMBOLS, get_market_status, EGX_SYMBOLS
)
from heartbeat_task import start_heartbeat
from log_setup import logger
from monitor_balance_status import monitor_balance_status
from monitor_connection_status import monitor_connection_status
from position_manager.monitor_positions import monitor_all_positions
from strategies.smc_strategy_egx import run_smc_strategy
from telegram_bot.bot_main import start_telegram
from telegram_bot.monitor_strategies_status import monitor_strategies_status
from tools.daily_performance_checker import daily_performance_check
from position_manager.advanced_risk_manager import AdvancedRiskManager
from utils.telegram_utils import send_telegram_message
from backtest.optimize_confidence import scheduler

# EGX-specific configuration
cooldown_active = False
cooldown_until = None
loss_streak = 0
LOSS_STREAK_LIMIT = 3
COOLDOWN_DURATION_MINUTES = 120

# Risk manager for EGX trading
risk_manager = AdvancedRiskManager()

async def fetch_egx_symbols():
    """Get available EGX symbols from configuration"""
    try:
        # Load symbols from pairs.json
        with open("config/pairs.json", "r") as f:
            config = json.load(f)
            symbols = config.get("pairs", DEFAULT_TRADING_SYMBOLS)
        
        # Validate symbols exist in EGX_SYMBOLS
        valid_symbols = []
        for symbol in symbols:
            if symbol in EGX_SYMBOLS:
                valid_symbols.append(symbol)
            else:
                logger.warning(f"⚠️ Symbol {symbol} not found in EGX configuration")
        
        logger.info(f"📈 Available EGX symbols: {valid_symbols}")
        return valid_symbols
        
    except Exception as e:
        logger.error(f"❌ Error loading EGX symbols: {e}")
        return DEFAULT_TRADING_SYMBOLS

async def filter_top_egx_symbols(symbols, top_n=5):
    """Filter top EGX symbols based on volume and volatility"""
    scored = []
    
    for symbol in symbols:
        try:
            # Get OHLCV data for analysis
            df = await get_ohlcv(symbol, interval="1h", limit=24)  # Last 24 hours
            if df and len(df) > 10:
                closes = [c["close"] for c in df]
                volumes = [c["volume"] for c in df]
                
                # Calculate volatility and average volume
                volatility = (max(closes) / min(closes) - 1) if min(closes) > 0 else 0
                avg_volume = sum(volumes) / len(volumes) if volumes else 0
                
                # Score based on volatility and volume (good for trading)
                score = volatility * avg_volume
                scored.append((symbol, score, volatility, avg_volume))
                
                logger.info(f"📊 {symbol}: volatility={volatility:.3f}, avg_volume={avg_volume:,.0f}, score={score:.2f}")
                
        except Exception as e:
            logger.warning(f"⚠️ Could not analyze {symbol}: {e}")
            continue
    
    # Sort by score and return top symbols
    scored.sort(key=lambda x: x[1], reverse=True)
    top_symbols = [s[0] for s in scored[:top_n]]
    
    logger.info(f"🏆 Top {top_n} EGX symbols selected: {top_symbols}")
    return top_symbols

async def check_market_hours():
    """Check if EGX market is open"""
    market_status = get_market_status()
    if not market_status.get("is_open", False):
        logger.info(f"🕐 EGX Market is closed. Current time: {market_status.get('current_time', 'Unknown')}")
        logger.info(f"📅 Trading hours: {market_status.get('market_open', 'Unknown')} - {market_status.get('market_close', 'Unknown')}")
        return False
    return True

async def periodic_performance_check():
    """Run daily performance checks"""
    while True:
        await daily_performance_check()
        await asyncio.sleep(86400)  # 24 hours

async def egx_strategy_worker(symbol: str):
    """EGX-specific strategy worker for each symbol"""
    global cooldown_active, cooldown_until, loss_streak
    
    try:
        while True:
            # Check cooldown
            now = time.time()
            if cooldown_active and cooldown_until and now < cooldown_until:
                logger.info(f"🛑 Cooldown active. Skipping signals for {symbol}")
                await asyncio.sleep(300)  # 5 minutes during cooldown
                continue
                
            if cooldown_active and cooldown_until and now >= cooldown_until:
                cooldown_active = False
                loss_streak = 0
                await send_telegram_message("✅ Cooldown ended. Trading resumed.")
            
            # Check market hours
            if not await check_market_hours():
                logger.info(f"🕐 Market closed. Waiting for {symbol}...")
                await asyncio.sleep(1800)  # 30 minutes
                continue
            
            # Get balance
            balance_data = await fetch_egx_balance()
            if balance_data is None:
                logger.warning(f"⚠️ Could not get balance, skipping {symbol}")
                await asyncio.sleep(60)
                continue
            
            total_balance, free_balance, _ = balance_data
            risk_manager.set_equity(total_balance)
            
            # Run strategy
            capital = free_balance
            signal = await run_smc_strategy(symbol=symbol, capital=capital)
            
            if signal:
                entry_price = signal.get("entry_price")
                if not isinstance(entry_price, (float, int)) or entry_price <= 0:
                    logger.warning(f"⚠️ Invalid entry price {entry_price} for {symbol}, skipping signal")
                    await asyncio.sleep(60)
                    continue
                
                if risk_manager.allowed_to_trade():
                    # Calculate risk parameters for stocks
                    sl_percent = 5.0  # 5% stop loss for stocks
                    tp_percent = 10.0  # 10% take profit for stocks
                    risk_percent = 2.0  # Risk 2% of capital per trade
                    
                    logger.info(f"📊 EGX Signal for {symbol}: {signal['direction']} at {entry_price:.2f} EGP")
                    
                    if signal["direction"].lower() == "buy":
                        result = await place_egx_order(
                            symbol=symbol,
                            side="buy",
                            capital=capital,
                            sl_percent=sl_percent,
                            tp_percent=tp_percent,
                            risk_percent=risk_percent
                        )
                        
                        if result is False:
                            loss_streak += 1
                            if loss_streak >= LOSS_STREAK_LIMIT:
                                cooldown_active = True
                                cooldown_until = now + (COOLDOWN_DURATION_MINUTES * 60)
                                await send_telegram_message("🚨 Auto-Cooldown activated for 2 hours!")
                    
                    # For stocks, we typically only go long (buy)
                    # Short selling is more complex and often restricted
                    elif signal["direction"].lower() == "sell":
                        logger.info(f"📊 Sell signal for {symbol} - checking if we have position to close")
                        # Check if we have a position to close
                        positions = await get_egx_open_positions()
                        for pos in positions:
                            if pos["symbol"] == symbol:
                                await close_stock_position(symbol)
                                break
            
            # Sleep between checks (longer for stocks than crypto)
            await asyncio.sleep(120 if not cooldown_active else 300)  # 2-5 minutes
            
    except Exception as e:
        logger.error(f"❌ Error in EGX worker for {symbol}: {e}")

async def launch_egx_background_tasks():
    """Launch background monitoring tasks for EGX"""
    await asyncio.gather(
        start_heartbeat(),
        monitor_all_positions(),
        monitor_strategies_status(),
        monitor_connection_status(),
        monitor_balance_status(),
        periodic_performance_check(),
    )

async def start_egx_full():
    """Start full EGX trading system"""
    nest_asyncio.apply()
    scheduler.start()
    await start_telegram()

async def main():
    """Main EGX trading bot entry point"""
    logger.info("🇪🇬 EGX Trading Bot starting...")
    
    # Initialize EGX connection
    await init_egx_exchange()
    
    # Check market status
    market_status = get_market_status()
    logger.info(f"🏛️ EGX Market Status: {'OPEN' if market_status.get('is_open') else 'CLOSED'}")
    logger.info(f"🕐 Current time: {market_status.get('current_time')} ({market_status.get('timezone')})")
    
    # Start background tasks
    await asyncio.gather(
        launch_egx_background_tasks(),
        start_egx_full()
    )
    
    # Get and filter symbols
    all_symbols = await fetch_egx_symbols()
    if not all_symbols:
        logger.error("❌ No EGX symbols available!")
        return
    
    # Filter top symbols for trading
    top_symbols = await filter_top_egx_symbols(all_symbols, top_n=5)
    if not top_symbols:
        logger.warning("⚠️ No suitable symbols found, using default symbols")
        top_symbols = DEFAULT_TRADING_SYMBOLS[:3]
    
    logger.info(f"🎯 Selected EGX symbols for trading: {top_symbols}")
    
    # Send startup message
    await send_telegram_message(f"🇪🇬 EGX Trading Bot started!\n📈 Trading symbols: {', '.join(top_symbols)}\n💰 Market: {'OPEN' if market_status.get('is_open') else 'CLOSED'}")
    
    # Start strategy workers for each symbol
    strategy_tasks = [
        asyncio.create_task(egx_strategy_worker(symbol)) 
        for symbol in top_symbols
    ]
    
    await asyncio.gather(*strategy_tasks)

if __name__ == "__main__":
    import sys
    if sys.platform.startswith("win"):
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    while True:
        try:
            asyncio.run(main())
        except Exception as e:
            logger.exception("❌ Fatal error in EGX launcher. Restarting in 30 seconds...")
            try:
                asyncio.run(send_telegram_message("❌ EGX Bot crashed! Restarting in 30 seconds..."))
            except Exception:
                logger.warning("⚠️ Could not send crash message")
            time.sleep(30)
            logger.info("🔄 Attempting to restart EGX bot...")
