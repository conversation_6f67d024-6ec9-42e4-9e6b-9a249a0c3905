"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.arrayExpression = arrayExpression;
exports.assignmentExpression = assignmentExpression;
exports.binaryExpression = binaryExpression;
exports.interpreterDirective = interpreterDirective;
exports.directive = directive;
exports.directiveLiteral = directiveLiteral;
exports.blockStatement = blockStatement;
exports.breakStatement = breakStatement;
exports.callExpression = callExpression;
exports.catchClause = catchClause;
exports.conditionalExpression = conditionalExpression;
exports.continueStatement = continueStatement;
exports.debuggerStatement = debuggerStatement;
exports.doWhileStatement = doWhileStatement;
exports.emptyStatement = emptyStatement;
exports.expressionStatement = expressionStatement;
exports.file = file;
exports.forInStatement = forInStatement;
exports.forStatement = forStatement;
exports.functionDeclaration = functionDeclaration;
exports.functionExpression = functionExpression;
exports.identifier = identifier;
exports.ifStatement = ifStatement;
exports.labeledStatement = labeledStatement;
exports.stringLiteral = stringLiteral;
exports.numericLiteral = numericLiteral;
exports.nullLiteral = nullLiteral;
exports.booleanLiteral = booleanLiteral;
exports.regExpLiteral = regExpLiteral;
exports.logicalExpression = logicalExpression;
exports.memberExpression = memberExpression;
exports.newExpression = newExpression;
exports.program = program;
exports.objectExpression = objectExpression;
exports.objectMethod = objectMethod;
exports.objectProperty = objectProperty;
exports.restElement = restElement;
exports.returnStatement = returnStatement;
exports.sequenceExpression = sequenceExpression;
exports.parenthesizedExpression = parenthesizedExpression;
exports.switchCase = switchCase;
exports.switchStatement = switchStatement;
exports.thisExpression = thisExpression;
exports.throwStatement = throwStatement;
exports.tryStatement = tryStatement;
exports.unaryExpression = unaryExpression;
exports.updateExpression = updateExpression;
exports.variableDeclaration = variableDeclaration;
exports.variableDeclarator = variableDeclarator;
exports.whileStatement = whileStatement;
exports.withStatement = withStatement;
exports.assignmentPattern = assignmentPattern;
exports.arrayPattern = arrayPattern;
exports.arrowFunctionExpression = arrowFunctionExpression;
exports.classBody = classBody;
exports.classExpression = classExpression;
exports.classDeclaration = classDeclaration;
exports.exportAllDeclaration = exportAllDeclaration;
exports.exportDefaultDeclaration = exportDefaultDeclaration;
exports.exportNamedDeclaration = exportNamedDeclaration;
exports.exportSpecifier = exportSpecifier;
exports.forOfStatement = forOfStatement;
exports.importDeclaration = importDeclaration;
exports.importDefaultSpecifier = importDefaultSpecifier;
exports.importNamespaceSpecifier = importNamespaceSpecifier;
exports.importSpecifier = importSpecifier;
exports.metaProperty = metaProperty;
exports.classMethod = classMethod;
exports.objectPattern = objectPattern;
exports.spreadElement = spreadElement;
exports.super = _super;
exports.taggedTemplateExpression = taggedTemplateExpression;
exports.templateElement = templateElement;
exports.templateLiteral = templateLiteral;
exports.yieldExpression = yieldExpression;
exports.awaitExpression = awaitExpression;
exports.import = _import;
exports.bigIntLiteral = bigIntLiteral;
exports.exportNamespaceSpecifier = exportNamespaceSpecifier;
exports.optionalMemberExpression = optionalMemberExpression;
exports.optionalCallExpression = optionalCallExpression;
exports.classProperty = classProperty;
exports.classPrivateProperty = classPrivateProperty;
exports.classPrivateMethod = classPrivateMethod;
exports.privateName = privateName;
exports.anyTypeAnnotation = anyTypeAnnotation;
exports.arrayTypeAnnotation = arrayTypeAnnotation;
exports.booleanTypeAnnotation = booleanTypeAnnotation;
exports.booleanLiteralTypeAnnotation = booleanLiteralTypeAnnotation;
exports.nullLiteralTypeAnnotation = nullLiteralTypeAnnotation;
exports.classImplements = classImplements;
exports.declareClass = declareClass;
exports.declareFunction = declareFunction;
exports.declareInterface = declareInterface;
exports.declareModule = declareModule;
exports.declareModuleExports = declareModuleExports;
exports.declareTypeAlias = declareTypeAlias;
exports.declareOpaqueType = declareOpaqueType;
exports.declareVariable = declareVariable;
exports.declareExportDeclaration = declareExportDeclaration;
exports.declareExportAllDeclaration = declareExportAllDeclaration;
exports.declaredPredicate = declaredPredicate;
exports.existsTypeAnnotation = existsTypeAnnotation;
exports.functionTypeAnnotation = functionTypeAnnotation;
exports.functionTypeParam = functionTypeParam;
exports.genericTypeAnnotation = genericTypeAnnotation;
exports.inferredPredicate = inferredPredicate;
exports.interfaceExtends = interfaceExtends;
exports.interfaceDeclaration = interfaceDeclaration;
exports.interfaceTypeAnnotation = interfaceTypeAnnotation;
exports.intersectionTypeAnnotation = intersectionTypeAnnotation;
exports.mixedTypeAnnotation = mixedTypeAnnotation;
exports.emptyTypeAnnotation = emptyTypeAnnotation;
exports.nullableTypeAnnotation = nullableTypeAnnotation;
exports.numberLiteralTypeAnnotation = numberLiteralTypeAnnotation;
exports.numberTypeAnnotation = numberTypeAnnotation;
exports.objectTypeAnnotation = objectTypeAnnotation;
exports.objectTypeInternalSlot = objectTypeInternalSlot;
exports.objectTypeCallProperty = objectTypeCallProperty;
exports.objectTypeIndexer = objectTypeIndexer;
exports.objectTypeProperty = objectTypeProperty;
exports.objectTypeSpreadProperty = objectTypeSpreadProperty;
exports.opaqueType = opaqueType;
exports.qualifiedTypeIdentifier = qualifiedTypeIdentifier;
exports.stringLiteralTypeAnnotation = stringLiteralTypeAnnotation;
exports.stringTypeAnnotation = stringTypeAnnotation;
exports.symbolTypeAnnotation = symbolTypeAnnotation;
exports.thisTypeAnnotation = thisTypeAnnotation;
exports.tupleTypeAnnotation = tupleTypeAnnotation;
exports.typeofTypeAnnotation = typeofTypeAnnotation;
exports.typeAlias = typeAlias;
exports.typeAnnotation = typeAnnotation;
exports.typeCastExpression = typeCastExpression;
exports.typeParameter = typeParameter;
exports.typeParameterDeclaration = typeParameterDeclaration;
exports.typeParameterInstantiation = typeParameterInstantiation;
exports.unionTypeAnnotation = unionTypeAnnotation;
exports.variance = variance;
exports.voidTypeAnnotation = voidTypeAnnotation;
exports.enumDeclaration = enumDeclaration;
exports.enumBooleanBody = enumBooleanBody;
exports.enumNumberBody = enumNumberBody;
exports.enumStringBody = enumStringBody;
exports.enumSymbolBody = enumSymbolBody;
exports.enumBooleanMember = enumBooleanMember;
exports.enumNumberMember = enumNumberMember;
exports.enumStringMember = enumStringMember;
exports.enumDefaultedMember = enumDefaultedMember;
exports.indexedAccessType = indexedAccessType;
exports.optionalIndexedAccessType = optionalIndexedAccessType;
exports.jSXAttribute = exports.jsxAttribute = jsxAttribute;
exports.jSXClosingElement = exports.jsxClosingElement = jsxClosingElement;
exports.jSXElement = exports.jsxElement = jsxElement;
exports.jSXEmptyExpression = exports.jsxEmptyExpression = jsxEmptyExpression;
exports.jSXExpressionContainer = exports.jsxExpressionContainer = jsxExpressionContainer;
exports.jSXSpreadChild = exports.jsxSpreadChild = jsxSpreadChild;
exports.jSXIdentifier = exports.jsxIdentifier = jsxIdentifier;
exports.jSXMemberExpression = exports.jsxMemberExpression = jsxMemberExpression;
exports.jSXNamespacedName = exports.jsxNamespacedName = jsxNamespacedName;
exports.jSXOpeningElement = exports.jsxOpeningElement = jsxOpeningElement;
exports.jSXSpreadAttribute = exports.jsxSpreadAttribute = jsxSpreadAttribute;
exports.jSXText = exports.jsxText = jsxText;
exports.jSXFragment = exports.jsxFragment = jsxFragment;
exports.jSXOpeningFragment = exports.jsxOpeningFragment = jsxOpeningFragment;
exports.jSXClosingFragment = exports.jsxClosingFragment = jsxClosingFragment;
exports.noop = noop;
exports.placeholder = placeholder;
exports.v8IntrinsicIdentifier = v8IntrinsicIdentifier;
exports.argumentPlaceholder = argumentPlaceholder;
exports.bindExpression = bindExpression;
exports.importAttribute = importAttribute;
exports.decorator = decorator;
exports.doExpression = doExpression;
exports.exportDefaultSpecifier = exportDefaultSpecifier;
exports.recordExpression = recordExpression;
exports.tupleExpression = tupleExpression;
exports.decimalLiteral = decimalLiteral;
exports.staticBlock = staticBlock;
exports.moduleExpression = moduleExpression;
exports.topicReference = topicReference;
exports.pipelineTopicExpression = pipelineTopicExpression;
exports.pipelineBareFunction = pipelineBareFunction;
exports.pipelinePrimaryTopicReference = pipelinePrimaryTopicReference;
exports.tSParameterProperty = exports.tsParameterProperty = tsParameterProperty;
exports.tSDeclareFunction = exports.tsDeclareFunction = tsDeclareFunction;
exports.tSDeclareMethod = exports.tsDeclareMethod = tsDeclareMethod;
exports.tSQualifiedName = exports.tsQualifiedName = tsQualifiedName;
exports.tSCallSignatureDeclaration = exports.tsCallSignatureDeclaration = tsCallSignatureDeclaration;
exports.tSConstructSignatureDeclaration = exports.tsConstructSignatureDeclaration = tsConstructSignatureDeclaration;
exports.tSPropertySignature = exports.tsPropertySignature = tsPropertySignature;
exports.tSMethodSignature = exports.tsMethodSignature = tsMethodSignature;
exports.tSIndexSignature = exports.tsIndexSignature = tsIndexSignature;
exports.tSAnyKeyword = exports.tsAnyKeyword = tsAnyKeyword;
exports.tSBooleanKeyword = exports.tsBooleanKeyword = tsBooleanKeyword;
exports.tSBigIntKeyword = exports.tsBigIntKeyword = tsBigIntKeyword;
exports.tSIntrinsicKeyword = exports.tsIntrinsicKeyword = tsIntrinsicKeyword;
exports.tSNeverKeyword = exports.tsNeverKeyword = tsNeverKeyword;
exports.tSNullKeyword = exports.tsNullKeyword = tsNullKeyword;
exports.tSNumberKeyword = exports.tsNumberKeyword = tsNumberKeyword;
exports.tSObjectKeyword = exports.tsObjectKeyword = tsObjectKeyword;
exports.tSStringKeyword = exports.tsStringKeyword = tsStringKeyword;
exports.tSSymbolKeyword = exports.tsSymbolKeyword = tsSymbolKeyword;
exports.tSUndefinedKeyword = exports.tsUndefinedKeyword = tsUndefinedKeyword;
exports.tSUnknownKeyword = exports.tsUnknownKeyword = tsUnknownKeyword;
exports.tSVoidKeyword = exports.tsVoidKeyword = tsVoidKeyword;
exports.tSThisType = exports.tsThisType = tsThisType;
exports.tSFunctionType = exports.tsFunctionType = tsFunctionType;
exports.tSConstructorType = exports.tsConstructorType = tsConstructorType;
exports.tSTypeReference = exports.tsTypeReference = tsTypeReference;
exports.tSTypePredicate = exports.tsTypePredicate = tsTypePredicate;
exports.tSTypeQuery = exports.tsTypeQuery = tsTypeQuery;
exports.tSTypeLiteral = exports.tsTypeLiteral = tsTypeLiteral;
exports.tSArrayType = exports.tsArrayType = tsArrayType;
exports.tSTupleType = exports.tsTupleType = tsTupleType;
exports.tSOptionalType = exports.tsOptionalType = tsOptionalType;
exports.tSRestType = exports.tsRestType = tsRestType;
exports.tSNamedTupleMember = exports.tsNamedTupleMember = tsNamedTupleMember;
exports.tSUnionType = exports.tsUnionType = tsUnionType;
exports.tSIntersectionType = exports.tsIntersectionType = tsIntersectionType;
exports.tSConditionalType = exports.tsConditionalType = tsConditionalType;
exports.tSInferType = exports.tsInferType = tsInferType;
exports.tSParenthesizedType = exports.tsParenthesizedType = tsParenthesizedType;
exports.tSTypeOperator = exports.tsTypeOperator = tsTypeOperator;
exports.tSIndexedAccessType = exports.tsIndexedAccessType = tsIndexedAccessType;
exports.tSMappedType = exports.tsMappedType = tsMappedType;
exports.tSLiteralType = exports.tsLiteralType = tsLiteralType;
exports.tSExpressionWithTypeArguments = exports.tsExpressionWithTypeArguments = tsExpressionWithTypeArguments;
exports.tSInterfaceDeclaration = exports.tsInterfaceDeclaration = tsInterfaceDeclaration;
exports.tSInterfaceBody = exports.tsInterfaceBody = tsInterfaceBody;
exports.tSTypeAliasDeclaration = exports.tsTypeAliasDeclaration = tsTypeAliasDeclaration;
exports.tSAsExpression = exports.tsAsExpression = tsAsExpression;
exports.tSTypeAssertion = exports.tsTypeAssertion = tsTypeAssertion;
exports.tSEnumDeclaration = exports.tsEnumDeclaration = tsEnumDeclaration;
exports.tSEnumMember = exports.tsEnumMember = tsEnumMember;
exports.tSModuleDeclaration = exports.tsModuleDeclaration = tsModuleDeclaration;
exports.tSModuleBlock = exports.tsModuleBlock = tsModuleBlock;
exports.tSImportType = exports.tsImportType = tsImportType;
exports.tSImportEqualsDeclaration = exports.tsImportEqualsDeclaration = tsImportEqualsDeclaration;
exports.tSExternalModuleReference = exports.tsExternalModuleReference = tsExternalModuleReference;
exports.tSNonNullExpression = exports.tsNonNullExpression = tsNonNullExpression;
exports.tSExportAssignment = exports.tsExportAssignment = tsExportAssignment;
exports.tSNamespaceExportDeclaration = exports.tsNamespaceExportDeclaration = tsNamespaceExportDeclaration;
exports.tSTypeAnnotation = exports.tsTypeAnnotation = tsTypeAnnotation;
exports.tSTypeParameterInstantiation = exports.tsTypeParameterInstantiation = tsTypeParameterInstantiation;
exports.tSTypeParameterDeclaration = exports.tsTypeParameterDeclaration = tsTypeParameterDeclaration;
exports.tSTypeParameter = exports.tsTypeParameter = tsTypeParameter;
exports.numberLiteral = NumberLiteral;
exports.regexLiteral = RegexLiteral;
exports.restProperty = RestProperty;
exports.spreadProperty = SpreadProperty;

var _builder = require("../builder");

function arrayExpression(elements) {
  return (0, _builder.default)("ArrayExpression", ...arguments);
}

function assignmentExpression(operator, left, right) {
  return (0, _builder.default)("AssignmentExpression", ...arguments);
}

function binaryExpression(operator, left, right) {
  return (0, _builder.default)("BinaryExpression", ...arguments);
}

function interpreterDirective(value) {
  return (0, _builder.default)("InterpreterDirective", ...arguments);
}

function directive(value) {
  return (0, _builder.default)("Directive", ...arguments);
}

function directiveLiteral(value) {
  return (0, _builder.default)("DirectiveLiteral", ...arguments);
}

function blockStatement(body, directives) {
  return (0, _builder.default)("BlockStatement", ...arguments);
}

function breakStatement(label) {
  return (0, _builder.default)("BreakStatement", ...arguments);
}

function callExpression(callee, _arguments) {
  return (0, _builder.default)("CallExpression", ...arguments);
}

function catchClause(param, body) {
  return (0, _builder.default)("CatchClause", ...arguments);
}

function conditionalExpression(test, consequent, alternate) {
  return (0, _builder.default)("ConditionalExpression", ...arguments);
}

function continueStatement(label) {
  return (0, _builder.default)("ContinueStatement", ...arguments);
}

function debuggerStatement() {
  return (0, _builder.default)("DebuggerStatement", ...arguments);
}

function doWhileStatement(test, body) {
  return (0, _builder.default)("DoWhileStatement", ...arguments);
}

function emptyStatement() {
  return (0, _builder.default)("EmptyStatement", ...arguments);
}

function expressionStatement(expression) {
  return (0, _builder.default)("ExpressionStatement", ...arguments);
}

function file(program, comments, tokens) {
  return (0, _builder.default)("File", ...arguments);
}

function forInStatement(left, right, body) {
  return (0, _builder.default)("ForInStatement", ...arguments);
}

function forStatement(init, test, update, body) {
  return (0, _builder.default)("ForStatement", ...arguments);
}

function functionDeclaration(id, params, body, generator, async) {
  return (0, _builder.default)("FunctionDeclaration", ...arguments);
}

function functionExpression(id, params, body, generator, async) {
  return (0, _builder.default)("FunctionExpression", ...arguments);
}

function identifier(name) {
  return (0, _builder.default)("Identifier", ...arguments);
}

function ifStatement(test, consequent, alternate) {
  return (0, _builder.default)("IfStatement", ...arguments);
}

function labeledStatement(label, body) {
  return (0, _builder.default)("LabeledStatement", ...arguments);
}

function stringLiteral(value) {
  return (0, _builder.default)("StringLiteral", ...arguments);
}

function numericLiteral(value) {
  return (0, _builder.default)("NumericLiteral", ...arguments);
}

function nullLiteral() {
  return (0, _builder.default)("NullLiteral", ...arguments);
}

function booleanLiteral(value) {
  return (0, _builder.default)("BooleanLiteral", ...arguments);
}

function regExpLiteral(pattern, flags) {
  return (0, _builder.default)("RegExpLiteral", ...arguments);
}

function logicalExpression(operator, left, right) {
  return (0, _builder.default)("LogicalExpression", ...arguments);
}

function memberExpression(object, property, computed, optional) {
  return (0, _builder.default)("MemberExpression", ...arguments);
}

function newExpression(callee, _arguments) {
  return (0, _builder.default)("NewExpression", ...arguments);
}

function program(body, directives, sourceType, interpreter) {
  return (0, _builder.default)("Program", ...arguments);
}

function objectExpression(properties) {
  return (0, _builder.default)("ObjectExpression", ...arguments);
}

function objectMethod(kind, key, params, body, computed, generator, async) {
  return (0, _builder.default)("ObjectMethod", ...arguments);
}

function objectProperty(key, value, computed, shorthand, decorators) {
  return (0, _builder.default)("ObjectProperty", ...arguments);
}

function restElement(argument) {
  return (0, _builder.default)("RestElement", ...arguments);
}

function returnStatement(argument) {
  return (0, _builder.default)("ReturnStatement", ...arguments);
}

function sequenceExpression(expressions) {
  return (0, _builder.default)("SequenceExpression", ...arguments);
}

function parenthesizedExpression(expression) {
  return (0, _builder.default)("ParenthesizedExpression", ...arguments);
}

function switchCase(test, consequent) {
  return (0, _builder.default)("SwitchCase", ...arguments);
}

function switchStatement(discriminant, cases) {
  return (0, _builder.default)("SwitchStatement", ...arguments);
}

function thisExpression() {
  return (0, _builder.default)("ThisExpression", ...arguments);
}

function throwStatement(argument) {
  return (0, _builder.default)("ThrowStatement", ...arguments);
}

function tryStatement(block, handler, finalizer) {
  return (0, _builder.default)("TryStatement", ...arguments);
}

function unaryExpression(operator, argument, prefix) {
  return (0, _builder.default)("UnaryExpression", ...arguments);
}

function updateExpression(operator, argument, prefix) {
  return (0, _builder.default)("UpdateExpression", ...arguments);
}

function variableDeclaration(kind, declarations) {
  return (0, _builder.default)("VariableDeclaration", ...arguments);
}

function variableDeclarator(id, init) {
  return (0, _builder.default)("VariableDeclarator", ...arguments);
}

function whileStatement(test, body) {
  return (0, _builder.default)("WhileStatement", ...arguments);
}

function withStatement(object, body) {
  return (0, _builder.default)("WithStatement", ...arguments);
}

function assignmentPattern(left, right) {
  return (0, _builder.default)("AssignmentPattern", ...arguments);
}

function arrayPattern(elements) {
  return (0, _builder.default)("ArrayPattern", ...arguments);
}

function arrowFunctionExpression(params, body, async) {
  return (0, _builder.default)("ArrowFunctionExpression", ...arguments);
}

function classBody(body) {
  return (0, _builder.default)("ClassBody", ...arguments);
}

function classExpression(id, superClass, body, decorators) {
  return (0, _builder.default)("ClassExpression", ...arguments);
}

function classDeclaration(id, superClass, body, decorators) {
  return (0, _builder.default)("ClassDeclaration", ...arguments);
}

function exportAllDeclaration(source) {
  return (0, _builder.default)("ExportAllDeclaration", ...arguments);
}

function exportDefaultDeclaration(declaration) {
  return (0, _builder.default)("ExportDefaultDeclaration", ...arguments);
}

function exportNamedDeclaration(declaration, specifiers, source) {
  return (0, _builder.default)("ExportNamedDeclaration", ...arguments);
}

function exportSpecifier(local, exported) {
  return (0, _builder.default)("ExportSpecifier", ...arguments);
}

function forOfStatement(left, right, body, _await) {
  return (0, _builder.default)("ForOfStatement", ...arguments);
}

function importDeclaration(specifiers, source) {
  return (0, _builder.default)("ImportDeclaration", ...arguments);
}

function importDefaultSpecifier(local) {
  return (0, _builder.default)("ImportDefaultSpecifier", ...arguments);
}

function importNamespaceSpecifier(local) {
  return (0, _builder.default)("ImportNamespaceSpecifier", ...arguments);
}

function importSpecifier(local, imported) {
  return (0, _builder.default)("ImportSpecifier", ...arguments);
}

function metaProperty(meta, property) {
  return (0, _builder.default)("MetaProperty", ...arguments);
}

function classMethod(kind, key, params, body, computed, _static, generator, async) {
  return (0, _builder.default)("ClassMethod", ...arguments);
}

function objectPattern(properties) {
  return (0, _builder.default)("ObjectPattern", ...arguments);
}

function spreadElement(argument) {
  return (0, _builder.default)("SpreadElement", ...arguments);
}

function _super() {
  return (0, _builder.default)("Super", ...arguments);
}

function taggedTemplateExpression(tag, quasi) {
  return (0, _builder.default)("TaggedTemplateExpression", ...arguments);
}

function templateElement(value, tail) {
  return (0, _builder.default)("TemplateElement", ...arguments);
}

function templateLiteral(quasis, expressions) {
  return (0, _builder.default)("TemplateLiteral", ...arguments);
}

function yieldExpression(argument, delegate) {
  return (0, _builder.default)("YieldExpression", ...arguments);
}

function awaitExpression(argument) {
  return (0, _builder.default)("AwaitExpression", ...arguments);
}

function _import() {
  return (0, _builder.default)("Import", ...arguments);
}

function bigIntLiteral(value) {
  return (0, _builder.default)("BigIntLiteral", ...arguments);
}

function exportNamespaceSpecifier(exported) {
  return (0, _builder.default)("ExportNamespaceSpecifier", ...arguments);
}

function optionalMemberExpression(object, property, computed, optional) {
  return (0, _builder.default)("OptionalMemberExpression", ...arguments);
}

function optionalCallExpression(callee, _arguments, optional) {
  return (0, _builder.default)("OptionalCallExpression", ...arguments);
}

function classProperty(key, value, typeAnnotation, decorators, computed, _static) {
  return (0, _builder.default)("ClassProperty", ...arguments);
}

function classPrivateProperty(key, value, decorators, _static) {
  return (0, _builder.default)("ClassPrivateProperty", ...arguments);
}

function classPrivateMethod(kind, key, params, body, _static) {
  return (0, _builder.default)("ClassPrivateMethod", ...arguments);
}

function privateName(id) {
  return (0, _builder.default)("PrivateName", ...arguments);
}

function anyTypeAnnotation() {
  return (0, _builder.default)("AnyTypeAnnotation", ...arguments);
}

function arrayTypeAnnotation(elementType) {
  return (0, _builder.default)("ArrayTypeAnnotation", ...arguments);
}

function booleanTypeAnnotation() {
  return (0, _builder.default)("BooleanTypeAnnotation", ...arguments);
}

function booleanLiteralTypeAnnotation(value) {
  return (0, _builder.default)("BooleanLiteralTypeAnnotation", ...arguments);
}

function nullLiteralTypeAnnotation() {
  return (0, _builder.default)("NullLiteralTypeAnnotation", ...arguments);
}

function classImplements(id, typeParameters) {
  return (0, _builder.default)("ClassImplements", ...arguments);
}

function declareClass(id, typeParameters, _extends, body) {
  return (0, _builder.default)("DeclareClass", ...arguments);
}

function declareFunction(id) {
  return (0, _builder.default)("DeclareFunction", ...arguments);
}

function declareInterface(id, typeParameters, _extends, body) {
  return (0, _builder.default)("DeclareInterface", ...arguments);
}

function declareModule(id, body, kind) {
  return (0, _builder.default)("DeclareModule", ...arguments);
}

function declareModuleExports(typeAnnotation) {
  return (0, _builder.default)("DeclareModuleExports", ...arguments);
}

function declareTypeAlias(id, typeParameters, right) {
  return (0, _builder.default)("DeclareTypeAlias", ...arguments);
}

function declareOpaqueType(id, typeParameters, supertype) {
  return (0, _builder.default)("DeclareOpaqueType", ...arguments);
}

function declareVariable(id) {
  return (0, _builder.default)("DeclareVariable", ...arguments);
}

function declareExportDeclaration(declaration, specifiers, source) {
  return (0, _builder.default)("DeclareExportDeclaration", ...arguments);
}

function declareExportAllDeclaration(source) {
  return (0, _builder.default)("DeclareExportAllDeclaration", ...arguments);
}

function declaredPredicate(value) {
  return (0, _builder.default)("DeclaredPredicate", ...arguments);
}

function existsTypeAnnotation() {
  return (0, _builder.default)("ExistsTypeAnnotation", ...arguments);
}

function functionTypeAnnotation(typeParameters, params, rest, returnType) {
  return (0, _builder.default)("FunctionTypeAnnotation", ...arguments);
}

function functionTypeParam(name, typeAnnotation) {
  return (0, _builder.default)("FunctionTypeParam", ...arguments);
}

function genericTypeAnnotation(id, typeParameters) {
  return (0, _builder.default)("GenericTypeAnnotation", ...arguments);
}

function inferredPredicate() {
  return (0, _builder.default)("InferredPredicate", ...arguments);
}

function interfaceExtends(id, typeParameters) {
  return (0, _builder.default)("InterfaceExtends", ...arguments);
}

function interfaceDeclaration(id, typeParameters, _extends, body) {
  return (0, _builder.default)("InterfaceDeclaration", ...arguments);
}

function interfaceTypeAnnotation(_extends, body) {
  return (0, _builder.default)("InterfaceTypeAnnotation", ...arguments);
}

function intersectionTypeAnnotation(types) {
  return (0, _builder.default)("IntersectionTypeAnnotation", ...arguments);
}

function mixedTypeAnnotation() {
  return (0, _builder.default)("MixedTypeAnnotation", ...arguments);
}

function emptyTypeAnnotation() {
  return (0, _builder.default)("EmptyTypeAnnotation", ...arguments);
}

function nullableTypeAnnotation(typeAnnotation) {
  return (0, _builder.default)("NullableTypeAnnotation", ...arguments);
}

function numberLiteralTypeAnnotation(value) {
  return (0, _builder.default)("NumberLiteralTypeAnnotation", ...arguments);
}

function numberTypeAnnotation() {
  return (0, _builder.default)("NumberTypeAnnotation", ...arguments);
}

function objectTypeAnnotation(properties, indexers, callProperties, internalSlots, exact) {
  return (0, _builder.default)("ObjectTypeAnnotation", ...arguments);
}

function objectTypeInternalSlot(id, value, optional, _static, method) {
  return (0, _builder.default)("ObjectTypeInternalSlot", ...arguments);
}

function objectTypeCallProperty(value) {
  return (0, _builder.default)("ObjectTypeCallProperty", ...arguments);
}

function objectTypeIndexer(id, key, value, variance) {
  return (0, _builder.default)("ObjectTypeIndexer", ...arguments);
}

function objectTypeProperty(key, value, variance) {
  return (0, _builder.default)("ObjectTypeProperty", ...arguments);
}

function objectTypeSpreadProperty(argument) {
  return (0, _builder.default)("ObjectTypeSpreadProperty", ...arguments);
}

function opaqueType(id, typeParameters, supertype, impltype) {
  return (0, _builder.default)("OpaqueType", ...arguments);
}

function qualifiedTypeIdentifier(id, qualification) {
  return (0, _builder.default)("QualifiedTypeIdentifier", ...arguments);
}

function stringLiteralTypeAnnotation(value) {
  return (0, _builder.default)("StringLiteralTypeAnnotation", ...arguments);
}

function stringTypeAnnotation() {
  return (0, _builder.default)("StringTypeAnnotation", ...arguments);
}

function symbolTypeAnnotation() {
  return (0, _builder.default)("SymbolTypeAnnotation", ...arguments);
}

function thisTypeAnnotation() {
  return (0, _builder.default)("ThisTypeAnnotation", ...arguments);
}

function tupleTypeAnnotation(types) {
  return (0, _builder.default)("TupleTypeAnnotation", ...arguments);
}

function typeofTypeAnnotation(argument) {
  return (0, _builder.default)("TypeofTypeAnnotation", ...arguments);
}

function typeAlias(id, typeParameters, right) {
  return (0, _builder.default)("TypeAlias", ...arguments);
}

function typeAnnotation(typeAnnotation) {
  return (0, _builder.default)("TypeAnnotation", ...arguments);
}

function typeCastExpression(expression, typeAnnotation) {
  return (0, _builder.default)("TypeCastExpression", ...arguments);
}

function typeParameter(bound, _default, variance) {
  return (0, _builder.default)("TypeParameter", ...arguments);
}

function typeParameterDeclaration(params) {
  return (0, _builder.default)("TypeParameterDeclaration", ...arguments);
}

function typeParameterInstantiation(params) {
  return (0, _builder.default)("TypeParameterInstantiation", ...arguments);
}

function unionTypeAnnotation(types) {
  return (0, _builder.default)("UnionTypeAnnotation", ...arguments);
}

function variance(kind) {
  return (0, _builder.default)("Variance", ...arguments);
}

function voidTypeAnnotation() {
  return (0, _builder.default)("VoidTypeAnnotation", ...arguments);
}

function enumDeclaration(id, body) {
  return (0, _builder.default)("EnumDeclaration", ...arguments);
}

function enumBooleanBody(members) {
  return (0, _builder.default)("EnumBooleanBody", ...arguments);
}

function enumNumberBody(members) {
  return (0, _builder.default)("EnumNumberBody", ...arguments);
}

function enumStringBody(members) {
  return (0, _builder.default)("EnumStringBody", ...arguments);
}

function enumSymbolBody(members) {
  return (0, _builder.default)("EnumSymbolBody", ...arguments);
}

function enumBooleanMember(id) {
  return (0, _builder.default)("EnumBooleanMember", ...arguments);
}

function enumNumberMember(id, init) {
  return (0, _builder.default)("EnumNumberMember", ...arguments);
}

function enumStringMember(id, init) {
  return (0, _builder.default)("EnumStringMember", ...arguments);
}

function enumDefaultedMember(id) {
  return (0, _builder.default)("EnumDefaultedMember", ...arguments);
}

function indexedAccessType(objectType, indexType) {
  return (0, _builder.default)("IndexedAccessType", ...arguments);
}

function optionalIndexedAccessType(objectType, indexType) {
  return (0, _builder.default)("OptionalIndexedAccessType", ...arguments);
}

function jsxAttribute(name, value) {
  return (0, _builder.default)("JSXAttribute", ...arguments);
}

function jsxClosingElement(name) {
  return (0, _builder.default)("JSXClosingElement", ...arguments);
}

function jsxElement(openingElement, closingElement, children, selfClosing) {
  return (0, _builder.default)("JSXElement", ...arguments);
}

function jsxEmptyExpression() {
  return (0, _builder.default)("JSXEmptyExpression", ...arguments);
}

function jsxExpressionContainer(expression) {
  return (0, _builder.default)("JSXExpressionContainer", ...arguments);
}

function jsxSpreadChild(expression) {
  return (0, _builder.default)("JSXSpreadChild", ...arguments);
}

function jsxIdentifier(name) {
  return (0, _builder.default)("JSXIdentifier", ...arguments);
}

function jsxMemberExpression(object, property) {
  return (0, _builder.default)("JSXMemberExpression", ...arguments);
}

function jsxNamespacedName(namespace, name) {
  return (0, _builder.default)("JSXNamespacedName", ...arguments);
}

function jsxOpeningElement(name, attributes, selfClosing) {
  return (0, _builder.default)("JSXOpeningElement", ...arguments);
}

function jsxSpreadAttribute(argument) {
  return (0, _builder.default)("JSXSpreadAttribute", ...arguments);
}

function jsxText(value) {
  return (0, _builder.default)("JSXText", ...arguments);
}

function jsxFragment(openingFragment, closingFragment, children) {
  return (0, _builder.default)("JSXFragment", ...arguments);
}

function jsxOpeningFragment() {
  return (0, _builder.default)("JSXOpeningFragment", ...arguments);
}

function jsxClosingFragment() {
  return (0, _builder.default)("JSXClosingFragment", ...arguments);
}

function noop() {
  return (0, _builder.default)("Noop", ...arguments);
}

function placeholder(expectedNode, name) {
  return (0, _builder.default)("Placeholder", ...arguments);
}

function v8IntrinsicIdentifier(name) {
  return (0, _builder.default)("V8IntrinsicIdentifier", ...arguments);
}

function argumentPlaceholder() {
  return (0, _builder.default)("ArgumentPlaceholder", ...arguments);
}

function bindExpression(object, callee) {
  return (0, _builder.default)("BindExpression", ...arguments);
}

function importAttribute(key, value) {
  return (0, _builder.default)("ImportAttribute", ...arguments);
}

function decorator(expression) {
  return (0, _builder.default)("Decorator", ...arguments);
}

function doExpression(body, async) {
  return (0, _builder.default)("DoExpression", ...arguments);
}

function exportDefaultSpecifier(exported) {
  return (0, _builder.default)("ExportDefaultSpecifier", ...arguments);
}

function recordExpression(properties) {
  return (0, _builder.default)("RecordExpression", ...arguments);
}

function tupleExpression(elements) {
  return (0, _builder.default)("TupleExpression", ...arguments);
}

function decimalLiteral(value) {
  return (0, _builder.default)("DecimalLiteral", ...arguments);
}

function staticBlock(body) {
  return (0, _builder.default)("StaticBlock", ...arguments);
}

function moduleExpression(body) {
  return (0, _builder.default)("ModuleExpression", ...arguments);
}

function topicReference() {
  return (0, _builder.default)("TopicReference", ...arguments);
}

function pipelineTopicExpression(expression) {
  return (0, _builder.default)("PipelineTopicExpression", ...arguments);
}

function pipelineBareFunction(callee) {
  return (0, _builder.default)("PipelineBareFunction", ...arguments);
}

function pipelinePrimaryTopicReference() {
  return (0, _builder.default)("PipelinePrimaryTopicReference", ...arguments);
}

function tsParameterProperty(parameter) {
  return (0, _builder.default)("TSParameterProperty", ...arguments);
}

function tsDeclareFunction(id, typeParameters, params, returnType) {
  return (0, _builder.default)("TSDeclareFunction", ...arguments);
}

function tsDeclareMethod(decorators, key, typeParameters, params, returnType) {
  return (0, _builder.default)("TSDeclareMethod", ...arguments);
}

function tsQualifiedName(left, right) {
  return (0, _builder.default)("TSQualifiedName", ...arguments);
}

function tsCallSignatureDeclaration(typeParameters, parameters, typeAnnotation) {
  return (0, _builder.default)("TSCallSignatureDeclaration", ...arguments);
}

function tsConstructSignatureDeclaration(typeParameters, parameters, typeAnnotation) {
  return (0, _builder.default)("TSConstructSignatureDeclaration", ...arguments);
}

function tsPropertySignature(key, typeAnnotation, initializer) {
  return (0, _builder.default)("TSPropertySignature", ...arguments);
}

function tsMethodSignature(key, typeParameters, parameters, typeAnnotation) {
  return (0, _builder.default)("TSMethodSignature", ...arguments);
}

function tsIndexSignature(parameters, typeAnnotation) {
  return (0, _builder.default)("TSIndexSignature", ...arguments);
}

function tsAnyKeyword() {
  return (0, _builder.default)("TSAnyKeyword", ...arguments);
}

function tsBooleanKeyword() {
  return (0, _builder.default)("TSBooleanKeyword", ...arguments);
}

function tsBigIntKeyword() {
  return (0, _builder.default)("TSBigIntKeyword", ...arguments);
}

function tsIntrinsicKeyword() {
  return (0, _builder.default)("TSIntrinsicKeyword", ...arguments);
}

function tsNeverKeyword() {
  return (0, _builder.default)("TSNeverKeyword", ...arguments);
}

function tsNullKeyword() {
  return (0, _builder.default)("TSNullKeyword", ...arguments);
}

function tsNumberKeyword() {
  return (0, _builder.default)("TSNumberKeyword", ...arguments);
}

function tsObjectKeyword() {
  return (0, _builder.default)("TSObjectKeyword", ...arguments);
}

function tsStringKeyword() {
  return (0, _builder.default)("TSStringKeyword", ...arguments);
}

function tsSymbolKeyword() {
  return (0, _builder.default)("TSSymbolKeyword", ...arguments);
}

function tsUndefinedKeyword() {
  return (0, _builder.default)("TSUndefinedKeyword", ...arguments);
}

function tsUnknownKeyword() {
  return (0, _builder.default)("TSUnknownKeyword", ...arguments);
}

function tsVoidKeyword() {
  return (0, _builder.default)("TSVoidKeyword", ...arguments);
}

function tsThisType() {
  return (0, _builder.default)("TSThisType", ...arguments);
}

function tsFunctionType(typeParameters, parameters, typeAnnotation) {
  return (0, _builder.default)("TSFunctionType", ...arguments);
}

function tsConstructorType(typeParameters, parameters, typeAnnotation) {
  return (0, _builder.default)("TSConstructorType", ...arguments);
}

function tsTypeReference(typeName, typeParameters) {
  return (0, _builder.default)("TSTypeReference", ...arguments);
}

function tsTypePredicate(parameterName, typeAnnotation, asserts) {
  return (0, _builder.default)("TSTypePredicate", ...arguments);
}

function tsTypeQuery(exprName) {
  return (0, _builder.default)("TSTypeQuery", ...arguments);
}

function tsTypeLiteral(members) {
  return (0, _builder.default)("TSTypeLiteral", ...arguments);
}

function tsArrayType(elementType) {
  return (0, _builder.default)("TSArrayType", ...arguments);
}

function tsTupleType(elementTypes) {
  return (0, _builder.default)("TSTupleType", ...arguments);
}

function tsOptionalType(typeAnnotation) {
  return (0, _builder.default)("TSOptionalType", ...arguments);
}

function tsRestType(typeAnnotation) {
  return (0, _builder.default)("TSRestType", ...arguments);
}

function tsNamedTupleMember(label, elementType, optional) {
  return (0, _builder.default)("TSNamedTupleMember", ...arguments);
}

function tsUnionType(types) {
  return (0, _builder.default)("TSUnionType", ...arguments);
}

function tsIntersectionType(types) {
  return (0, _builder.default)("TSIntersectionType", ...arguments);
}

function tsConditionalType(checkType, extendsType, trueType, falseType) {
  return (0, _builder.default)("TSConditionalType", ...arguments);
}

function tsInferType(typeParameter) {
  return (0, _builder.default)("TSInferType", ...arguments);
}

function tsParenthesizedType(typeAnnotation) {
  return (0, _builder.default)("TSParenthesizedType", ...arguments);
}

function tsTypeOperator(typeAnnotation) {
  return (0, _builder.default)("TSTypeOperator", ...arguments);
}

function tsIndexedAccessType(objectType, indexType) {
  return (0, _builder.default)("TSIndexedAccessType", ...arguments);
}

function tsMappedType(typeParameter, typeAnnotation, nameType) {
  return (0, _builder.default)("TSMappedType", ...arguments);
}

function tsLiteralType(literal) {
  return (0, _builder.default)("TSLiteralType", ...arguments);
}

function tsExpressionWithTypeArguments(expression, typeParameters) {
  return (0, _builder.default)("TSExpressionWithTypeArguments", ...arguments);
}

function tsInterfaceDeclaration(id, typeParameters, _extends, body) {
  return (0, _builder.default)("TSInterfaceDeclaration", ...arguments);
}

function tsInterfaceBody(body) {
  return (0, _builder.default)("TSInterfaceBody", ...arguments);
}

function tsTypeAliasDeclaration(id, typeParameters, typeAnnotation) {
  return (0, _builder.default)("TSTypeAliasDeclaration", ...arguments);
}

function tsAsExpression(expression, typeAnnotation) {
  return (0, _builder.default)("TSAsExpression", ...arguments);
}

function tsTypeAssertion(typeAnnotation, expression) {
  return (0, _builder.default)("TSTypeAssertion", ...arguments);
}

function tsEnumDeclaration(id, members) {
  return (0, _builder.default)("TSEnumDeclaration", ...arguments);
}

function tsEnumMember(id, initializer) {
  return (0, _builder.default)("TSEnumMember", ...arguments);
}

function tsModuleDeclaration(id, body) {
  return (0, _builder.default)("TSModuleDeclaration", ...arguments);
}

function tsModuleBlock(body) {
  return (0, _builder.default)("TSModuleBlock", ...arguments);
}

function tsImportType(argument, qualifier, typeParameters) {
  return (0, _builder.default)("TSImportType", ...arguments);
}

function tsImportEqualsDeclaration(id, moduleReference) {
  return (0, _builder.default)("TSImportEqualsDeclaration", ...arguments);
}

function tsExternalModuleReference(expression) {
  return (0, _builder.default)("TSExternalModuleReference", ...arguments);
}

function tsNonNullExpression(expression) {
  return (0, _builder.default)("TSNonNullExpression", ...arguments);
}

function tsExportAssignment(expression) {
  return (0, _builder.default)("TSExportAssignment", ...arguments);
}

function tsNamespaceExportDeclaration(id) {
  return (0, _builder.default)("TSNamespaceExportDeclaration", ...arguments);
}

function tsTypeAnnotation(typeAnnotation) {
  return (0, _builder.default)("TSTypeAnnotation", ...arguments);
}

function tsTypeParameterInstantiation(params) {
  return (0, _builder.default)("TSTypeParameterInstantiation", ...arguments);
}

function tsTypeParameterDeclaration(params) {
  return (0, _builder.default)("TSTypeParameterDeclaration", ...arguments);
}

function tsTypeParameter(constraint, _default, name) {
  return (0, _builder.default)("TSTypeParameter", ...arguments);
}

function NumberLiteral(...args) {
  console.trace("The node type NumberLiteral has been renamed to NumericLiteral");
  return (0, _builder.default)("NumberLiteral", ...args);
}

function RegexLiteral(...args) {
  console.trace("The node type RegexLiteral has been renamed to RegExpLiteral");
  return (0, _builder.default)("RegexLiteral", ...args);
}

function RestProperty(...args) {
  console.trace("The node type RestProperty has been renamed to RestElement");
  return (0, _builder.default)("RestProperty", ...args);
}

function SpreadProperty(...args) {
  console.trace("The node type SpreadProperty has been renamed to SpreadElement");
  return (0, _builder.default)("SpreadProperty", ...args);
}