const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');

// Get the global TradingView service instance
let tvService = null;

// Get current price for a symbol
router.get('/price/:symbol', async (req, res, next) => {
    try {
        const { symbol } = req.params;
        const { exchange = 'EGX' } = req.query;

        logger.info(`📊 Price request: ${exchange}:${symbol}`);

        // Get service from app.locals
        const tvService = req.app.locals.tvService;
        if (!tvService) {
            return res.status(500).json({
                success: false,
                error: 'TradingView service not available'
            });
        }

        const priceData = await tvService.getCurrentPrice(symbol, exchange);
        
        res.json({
            success: true,
            data: priceData,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error(`Error fetching price for ${req.params.symbol}:`, error);
        next(error);
    }
});

// Get OHLCV data for a symbol
router.get('/ohlcv/:symbol', async (req, res, next) => {
    try {
        const { symbol } = req.params;
        const { 
            exchange = 'EGX', 
            interval = '1h', 
            limit = 150 
        } = req.query;

        logger.info(`📈 OHLCV request: ${exchange}:${symbol} (${interval}, ${limit} bars)`);

        // Get service from app.locals
        const tvService = req.app.locals.tvService;
        if (!tvService) {
            return res.status(500).json({
                success: false,
                error: 'TradingView service not available'
            });
        }

        const ohlcvData = await tvService.getOHLCV(
            symbol,
            exchange,
            interval,
            parseInt(limit)
        );
        
        res.json({
            success: true,
            data: ohlcvData,
            meta: {
                symbol: `${exchange}:${symbol}`,
                interval,
                count: ohlcvData.length
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error(`Error fetching OHLCV for ${req.params.symbol}:`, error);
        next(error);
    }
});

// Search symbols
router.get('/symbols/search', async (req, res, next) => {
    try {
        const { q: query, exchange = 'EGX' } = req.query;

        if (!query) {
            return res.status(400).json({
                success: false,
                error: 'Query parameter "q" is required'
            });
        }

        logger.info(`🔍 Symbol search: "${query}" in ${exchange}`);

        // Get service from app.locals
        const tvService = req.app.locals.tvService;
        if (!tvService) {
            return res.status(500).json({
                success: false,
                error: 'TradingView service not available'
            });
        }

        const symbols = await tvService.searchSymbols(query, exchange);
        
        res.json({
            success: true,
            data: symbols,
            meta: {
                query,
                exchange,
                count: symbols.length
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error(`Error searching symbols for "${req.query.q}":`, error);
        next(error);
    }
});

// Get multiple symbols data (batch request)
router.post('/batch/prices', async (req, res, next) => {
    try {
        const { symbols, exchange = 'EGX' } = req.body;

        if (!symbols || !Array.isArray(symbols)) {
            return res.status(400).json({
                success: false,
                error: 'symbols array is required'
            });
        }

        logger.info(`📊 Batch price request: ${symbols.length} symbols`);

        // Get service from app.locals
        const tvService = req.app.locals.tvService;
        if (!tvService) {
            return res.status(500).json({
                success: false,
                error: 'TradingView service not available'
            });
        }

        const results = await Promise.allSettled(
            symbols.map(symbol => tvService.getCurrentPrice(symbol, exchange))
        );

        const data = results.map((result, index) => ({
            symbol: symbols[index],
            success: result.status === 'fulfilled',
            data: result.status === 'fulfilled' ? result.value : null,
            error: result.status === 'rejected' ? result.reason.message : null
        }));

        res.json({
            success: true,
            data,
            meta: {
                total: symbols.length,
                successful: data.filter(d => d.success).length,
                failed: data.filter(d => !d.success).length
            },
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        logger.error('Error in batch price request:', error);
        next(error);
    }
});

// Get service status
router.get('/status', async (req, res) => {
    try {
        // Get service from app.locals
        const tvService = req.app.locals.tvService;
        if (!tvService) {
            return res.status(500).json({
                success: false,
                error: 'TradingView service not available'
            });
        }

        const status = tvService.getConnectionStatus();
        
        res.json({
            success: true,
            data: {
                service: 'TradingView EGX Bridge',
                ...status,
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        logger.error('Error getting service status:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get service status'
        });
    }
});

// Common EGX symbols endpoint
router.get('/symbols/egx', (req, res) => {
    const commonEGXSymbols = [
        { symbol: 'CIB', name: 'Commercial International Bank', sector: 'Banking' },
        { symbol: 'ETEL', name: 'Egyptian Company for Mobile Services', sector: 'Telecommunications' },
        { symbol: 'HELI', name: 'Heliopolis Housing', sector: 'Real Estate' },
        { symbol: 'EGAS', name: 'Egyptian Natural Gas Company', sector: 'Energy' },
        { symbol: 'SWDY', name: 'El Sewedy Electric Company', sector: 'Industrial' },
        { symbol: 'HRHO', name: 'Hassan Allam Holding', sector: 'Construction' },
        { symbol: 'EKHO', name: 'El Kahera Housing', sector: 'Real Estate' },
        { symbol: 'OCDI', name: 'Orascom Construction Industries', sector: 'Construction' },
        { symbol: 'TMGH', name: 'TMG Holding', sector: 'Real Estate' },
        { symbol: 'PHDC', name: 'Palm Hills Developments', sector: 'Real Estate' }
    ];

    res.json({
        success: true,
        data: commonEGXSymbols.map(s => ({
            ...s,
            fullName: `EGX:${s.symbol}`,
            exchange: 'EGX'
        })),
        meta: {
            exchange: 'EGX',
            count: commonEGXSymbols.length
        },
        timestamp: new Date().toISOString()
    });
});

module.exports = router;
