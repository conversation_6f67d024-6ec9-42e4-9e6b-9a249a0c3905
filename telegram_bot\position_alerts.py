from log_setup import logger
from telegram_bot.alert_utils import send_telegram_alert
from typing import Optional, Awaitable

# 🔔 Take Profit notification
async def notify_take_profit(symbol: str, qty: float, price: float) -> None:
    msg = (
        f"✅ TP reached for <b>{symbol}</b>\n"
        f"Volume: <code>{qty}</code> at price <code>{price}</code>"
    )
    logger.info(f"[ALERT] {msg}")
    if callable(send_telegram_alert):
        result: Optional[Awaitable] = send_telegram_alert(msg)
        if result:
            await result


# 🔔 Stop Loss notification
async def notify_stop_loss(symbol: str, qty: float, price: float) -> None:
    msg = (
        f"🛑 SL triggered for <b>{symbol}</b>\n"
        f"Volume: <code>{qty}</code> at price <code>{price}</code>"
    )
    logger.info(f"[ALERT] {msg}")
    if callable(send_telegram_alert):
        result: Optional[Awaitable] = send_telegram_alert(msg)
        if result:
            await result


# 🔔 Manual position close notification
async def notify_manual_close(symbol: str) -> None:
    msg = f"📤 Position for <b>{symbol}</b> was closed manually."
    logger.info(f"[ALERT] {msg}")
    if callable(send_telegram_alert):
        result: Optional[Awaitable] = send_telegram_alert(msg)
        if result:
            await result


# 🔔 Position flip notification
async def notify_flip(symbol: str, from_side: str, to_side: str) -> None:
    msg = (
        f"🔄 Flip for <b>{symbol}</b>\n"
        f"{from_side.upper()} ➔ {to_side.upper()}"
    )
    logger.info(f"[ALERT] {msg}")
    if callable(send_telegram_alert):
        result: Optional[Awaitable] = send_telegram_alert(msg)
        if result:
            await result